{"name": "composer/spdx-licenses", "description": "SPDX licenses list and validation library.", "type": "library", "license": "MIT", "keywords": ["spdx", "license", "validator"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^7", "phpstan/phpstan": "^1.11"}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\Spdx\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "vendor/bin/phpstan analyse", "sync-licenses": "bin/update-spdx-licenses"}}