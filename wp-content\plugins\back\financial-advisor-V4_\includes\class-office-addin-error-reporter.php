<?php
/**
 * Office Add-in Error Reporter
 *
 * This class provides error reporting and notification functionality
 * for Office Add-in operations.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class Office_Addin_Error_Reporter {
    
    /**
     * Error levels
     */
    const LEVEL_ERROR = 'error';
    const LEVEL_WARNING = 'warning';
    const LEVEL_INFO = 'info';
    const LEVEL_DEBUG = 'debug';
    
    /**
     * Error types
     */
    const TYPE_API_ERROR = 'api_error';
    const TYPE_VALIDATION_ERROR = 'validation_error';
    const TYPE_RATE_LIMIT_ERROR = 'rate_limit_error';
    const TYPE_CACHE_ERROR = 'cache_error';
    const TYPE_DATABASE_ERROR = 'database_error';
    const TYPE_AUTHENTICATION_ERROR = 'auth_error';
    
    /**
     * Maximum number of errors to store
     */
    private $max_errors = 100;
    
    /**
     * Error retention period (in seconds)
     */
    private $retention_period = 604800; // 7 days
    
    /**
     * Constructor
     */
    public function __construct() {
        // Schedule cleanup of old errors
        if (!wp_next_scheduled('office_addin_error_cleanup')) {
            wp_schedule_event(time(), 'daily', 'office_addin_error_cleanup');
        }
        
        add_action('office_addin_error_cleanup', [$this, 'cleanup_old_errors']);
    }
    
    /**
     * Report an error
     *
     * @param string $message Error message
     * @param string $type Error type
     * @param string $level Error level
     * @param array $context Additional context data
     * @return bool Success status
     */
    public function report_error($message, $type = self::TYPE_API_ERROR, $level = self::LEVEL_ERROR, $context = []) {
        try {
            $error_data = [
                'id' => uniqid('err_', true),
                'timestamp' => time(),
                'message' => sanitize_text_field($message),
                'type' => sanitize_key($type),
                'level' => sanitize_key($level),
                'context' => $this->sanitize_context($context),
                'user_id' => get_current_user_id(),
                'ip_address' => $this->get_client_ip(),
                'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? sanitize_text_field($_SERVER['HTTP_USER_AGENT']) : '',
                'url' => isset($_SERVER['REQUEST_URI']) ? sanitize_text_field($_SERVER['REQUEST_URI']) : '',
                'plugin_version' => $this->get_plugin_version()
            ];
            
            // Store error in database
            $this->store_error($error_data);
            
            // Log error for debugging
            if (function_exists('dv_debug_log')) {
                dv_debug_log("[{$level}] {$type}: {$message}", 'office_addin_errors', $context);
            }
            
            // Send notifications for critical errors
            if ($level === self::LEVEL_ERROR) {
                $this->send_error_notification($error_data);
            }
            
            // Track error metrics
            $this->track_error_metrics($type, $level);
            
            return true;
            
        } catch (Exception $e) {
            // Fallback logging
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Error reporter exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return false;
        }
    }
    
    /**
     * Get recent errors
     *
     * @param int $limit Number of errors to retrieve
     * @param string $type Filter by error type
     * @param string $level Filter by error level
     * @return array List of errors
     */
    public function get_recent_errors($limit = 50, $type = null, $level = null) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            // Create table if it doesn't exist
            $this->create_errors_table();
            
            $where_conditions = [];
            $where_values = [];
            
            if ($type) {
                $where_conditions[] = 'type = %s';
                $where_values[] = $type;
            }
            
            if ($level) {
                $where_conditions[] = 'level = %s';
                $where_values[] = $level;
            }
            
            $where_clause = '';
            if (!empty($where_conditions)) {
                $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            }
            
            $query = "SELECT * FROM $table_name $where_clause ORDER BY timestamp DESC LIMIT %d";
            $where_values[] = $limit;
            
            $prepared_query = $wpdb->prepare($query, $where_values);
            $errors = $wpdb->get_results($prepared_query, ARRAY_A);
            
            // Decode context data
            foreach ($errors as &$error) {
                $error['context'] = json_decode($error['context'], true) ?: [];
                $error['formatted_time'] = date('Y-m-d H:i:s', $error['timestamp']);
            }
            
            return $errors;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get recent errors exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return [];
        }
    }
    
    /**
     * Get error statistics
     *
     * @param int $days Number of days to analyze
     * @return array Error statistics
     */
    public function get_error_statistics($days = 7) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            $since_timestamp = time() - ($days * 24 * 60 * 60);
            
            // Total errors by level
            $level_stats = $wpdb->get_results($wpdb->prepare(
                "SELECT level, COUNT(*) as count FROM $table_name WHERE timestamp >= %d GROUP BY level",
                $since_timestamp
            ), ARRAY_A);
            
            // Total errors by type
            $type_stats = $wpdb->get_results($wpdb->prepare(
                "SELECT type, COUNT(*) as count FROM $table_name WHERE timestamp >= %d GROUP BY type",
                $since_timestamp
            ), ARRAY_A);
            
            // Errors by day
            $daily_stats = $wpdb->get_results($wpdb->prepare(
                "SELECT DATE(FROM_UNIXTIME(timestamp)) as date, COUNT(*) as count 
                 FROM $table_name 
                 WHERE timestamp >= %d 
                 GROUP BY DATE(FROM_UNIXTIME(timestamp)) 
                 ORDER BY date DESC",
                $since_timestamp
            ), ARRAY_A);
            
            return [
                'by_level' => $level_stats,
                'by_type' => $type_stats,
                'by_day' => $daily_stats,
                'total_errors' => array_sum(array_column($level_stats, 'count')),
                'period_days' => $days
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get error statistics exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return [
                'error' => 'Unable to get error statistics',
                'by_level' => [],
                'by_type' => [],
                'by_day' => [],
                'total_errors' => 0,
                'period_days' => $days
            ];
        }
    }
    
    /**
     * Clear old errors
     *
     * @param int $older_than_days Clear errors older than this many days
     * @return int Number of errors cleared
     */
    public function clear_old_errors($older_than_days = null) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            $retention_days = $older_than_days ?: ($this->retention_period / 86400);
            $cutoff_timestamp = time() - ($retention_days * 24 * 60 * 60);
            
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM $table_name WHERE timestamp < %d",
                $cutoff_timestamp
            ));
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Cleared {$deleted} old errors (older than {$retention_days} days)", 'office_addin_errors');
            }
            
            return $deleted;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Clear old errors exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return 0;
        }
    }
    
    /**
     * Store error in database
     *
     * @param array $error_data Error data
     * @return bool Success status
     */
    private function store_error($error_data) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            // Create table if it doesn't exist
            $this->create_errors_table();
            
            $result = $wpdb->insert(
                $table_name,
                [
                    'error_id' => $error_data['id'],
                    'timestamp' => $error_data['timestamp'],
                    'message' => $error_data['message'],
                    'type' => $error_data['type'],
                    'level' => $error_data['level'],
                    'context' => wp_json_encode($error_data['context']),
                    'user_id' => $error_data['user_id'],
                    'ip_address' => $error_data['ip_address'],
                    'user_agent' => $error_data['user_agent'],
                    'url' => $error_data['url'],
                    'plugin_version' => $error_data['plugin_version']
                ],
                [
                    '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s'
                ]
            );
            
            // Limit number of stored errors
            $this->limit_stored_errors();
            
            return $result !== false;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Store error exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return false;
        }
    }
    
    /**
     * Create errors table if it doesn't exist
     *
     * @return bool Success status
     */
    private function create_errors_table() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE IF NOT EXISTS $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                error_id varchar(50) NOT NULL,
                timestamp int(11) NOT NULL,
                message text NOT NULL,
                type varchar(50) NOT NULL,
                level varchar(20) NOT NULL,
                context longtext,
                user_id int(11) DEFAULT 0,
                ip_address varchar(45),
                user_agent text,
                url text,
                plugin_version varchar(20),
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY timestamp (timestamp),
                KEY type (type),
                KEY level (level),
                KEY user_id (user_id)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            return true;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Create errors table exception: ' . $e->getMessage(), 'office_addin_errors');
            }
            return false;
        }
    }
    
    /**
     * Limit number of stored errors
     */
    private function limit_stored_errors() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_errors';
            
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            
            if ($count > $this->max_errors) {
                $excess = $count - $this->max_errors;
                $wpdb->query($wpdb->prepare(
                    "DELETE FROM $table_name ORDER BY timestamp ASC LIMIT %d",
                    $excess
                ));
            }
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Limit stored errors exception: ' . $e->getMessage(), 'office_addin_errors');
            }
        }
    }
    
    /**
     * Send error notification to administrators
     *
     * @param array $error_data Error data
     */
    private function send_error_notification($error_data) {
        try {
            // Check if notifications are enabled
            $notifications_enabled = get_option('office_addin_error_notifications', false);
            if (!$notifications_enabled) {
                return;
            }
            
            // Rate limit notifications (max 1 per hour for same error type)
            $notification_key = 'error_notification_' . $error_data['type'];
            $last_notification = get_transient($notification_key);
            
            if ($last_notification) {
                return; // Skip notification to avoid spam
            }
            
            // Set notification throttle (1 hour)
            set_transient($notification_key, time(), 3600);
            
            $admin_email = get_option('admin_email');
            $site_name = get_bloginfo('name');
            
            $subject = sprintf(
                '[%s] Office Add-in Error: %s',
                $site_name,
                $error_data['type']
            );
            
            $message = sprintf(
                "An error has occurred in the Office Add-in:\n\n" .
                "Error Type: %s\n" .
                "Level: %s\n" .
                "Message: %s\n" .
                "Time: %s\n" .
                "User ID: %s\n" .
                "IP Address: %s\n" .
                "URL: %s\n\n" .
                "Please check the Office Add-in error logs for more details.\n\n" .
                "Site: %s",
                $error_data['type'],
                $error_data['level'],
                $error_data['message'],
                date('Y-m-d H:i:s', $error_data['timestamp']),
                $error_data['user_id'] ?: 'Guest',
                $error_data['ip_address'],
                $error_data['url'],
                site_url()
            );
            
            wp_mail($admin_email, $subject, $message);
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Send error notification exception: ' . $e->getMessage(), 'office_addin_errors');
            }
        }
    }
    
    /**
     * Track error metrics
     *
     * @param string $type Error type
     * @param string $level Error level
     */
    private function track_error_metrics($type, $level) {
        try {
            $metrics = get_option('office_addin_error_metrics', [
                'total_errors' => 0,
                'by_type' => [],
                'by_level' => [],
                'last_updated' => time()
            ]);
            
            $metrics['total_errors']++;
            
            if (!isset($metrics['by_type'][$type])) {
                $metrics['by_type'][$type] = 0;
            }
            $metrics['by_type'][$type]++;
            
            if (!isset($metrics['by_level'][$level])) {
                $metrics['by_level'][$level] = 0;
            }
            $metrics['by_level'][$level]++;
            
            $metrics['last_updated'] = time();
            
            update_option('office_addin_error_metrics', $metrics);
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Track error metrics exception: ' . $e->getMessage(), 'office_addin_errors');
            }
        }
    }
    
    /**
     * Sanitize context data
     *
     * @param array $context Context data
     * @return array Sanitized context
     */
    private function sanitize_context($context) {
        if (!is_array($context)) {
            return [];
        }
        
        $sanitized = [];
        foreach ($context as $key => $value) {
            $key = sanitize_key($key);
            
            if (is_string($value)) {
                $sanitized[$key] = sanitize_text_field($value);
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitize_context($value);
            } elseif (is_scalar($value)) {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Get client IP address
     *
     * @return string IP address
     */
    private function get_client_ip() {
        $headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return '127.0.0.1';
    }
    
    /**
     * Get plugin version
     *
     * @return string Plugin version
     */
    private function get_plugin_version() {
        if (defined('DOCUMENT_VIEWER_VERSION')) {
            return DOCUMENT_VIEWER_VERSION;
        }
        
        return get_option('document_viewer_version', '1.0.0');
    }
    
    /**
     * Cleanup old errors (scheduled task)
     */
    public function cleanup_old_errors() {
        $deleted = $this->clear_old_errors();
        
        if (function_exists('dv_debug_log')) {
            dv_debug_log("Scheduled cleanup: deleted {$deleted} old errors", 'office_addin_errors');
        }
    }
}
