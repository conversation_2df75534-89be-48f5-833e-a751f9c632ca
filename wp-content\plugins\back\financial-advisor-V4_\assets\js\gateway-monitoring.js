/**
 * Payment Gateway Monitoring JavaScript
 * 
 * This file contains the real-time monitoring functionality for payment gateways
 */

(function($) {
    'use strict';

    // Object to hold our monitoring functions
    var GatewayMonitor = {
        /**
         * Initialize the monitoring
         */
        init: function() {
            this.setupRefreshButton();
            this.setupPeriodicRefresh();
            this.setupDiagnosticTestButtons();
            this.setupLogViewers();
        },

        /**
         * Set up the manual refresh button click event
         */
        setupRefreshButton: function() {
            $('#fa-refresh-monitoring').on('click', function(e) {
                e.preventDefault();
                GatewayMonitor.refreshAllGateways();
                $(this).addClass('refreshing');
                setTimeout(function() {
                    $('#fa-refresh-monitoring').removeClass('refreshing');
                }, 1000);
            });
        },

        /**
         * Set up periodic refresh of gateway status
         */
        setupPeriodicRefresh: function() {
            if (typeof faGatewayMonitoring !== 'undefined' && faGatewayMonitoring.refresh_interval) {
                setInterval(function() {
                    GatewayMonitor.refreshAllGateways(true);
                }, faGatewayMonitoring.refresh_interval);
            }
        },

        /**
         * Refresh all gateway status information
         * 
         * @param {boolean} silent Whether to show visual cues for the refresh
         */
        refreshAllGateways: function(silent) {
            if (!silent) {
                $('.fa-gateway-card').addClass('refreshing');
            }

            $.ajax({
                url: faGatewayMonitoring.ajax_url,
                type: 'POST',
                data: {
                    action: 'fa_check_gateway_status',
                    gateway: 'all',
                    nonce: faGatewayMonitoring.nonce
                },
                success: function(response) {
                    if (response.success) {
                        GatewayMonitor.updateGatewayCards(response.data);
                        GatewayMonitor.updateLastCheckedTime(response.data.human_time);
                    }
                },
                complete: function() {
                    if (!silent) {
                        $('.fa-gateway-card').removeClass('refreshing');
                    }
                }
            });
        },

        /**
         * Update gateway cards with new status data
         * 
         * @param {object} data Response data from the AJAX request
         */
        updateGatewayCards: function(data) {
            // Update Stripe gateway card
            if (data.stripe) {
                this.updateGatewayCard('stripe', data.stripe);
            }
            
            // Update PayPal gateway card
            if (data.paypal) {
                this.updateGatewayCard('paypal', data.paypal);
            }
        },

        /**
         * Update a specific gateway card with new status data
         * 
         * @param {string} gateway Gateway name
         * @param {object} gatewayData Gateway data object
         */
        updateGatewayCard: function(gateway, gatewayData) {
            var $card = $('#fa-gateway-' + gateway);
            
            if ($card.length === 0) {
                return;
            }
            
            // Update status indicator
            var statusClass = this.getStatusClass(gatewayData.status);
            var statusText = this.getStatusText(gatewayData.status);
            
            $card.find('.fa-status-indicator')
                .removeClass('fa-status-success fa-status-warning fa-status-neutral fa-status-error')
                .addClass(statusClass);
                
            $card.find('.fa-status-text').text(statusText);
            
            // Update metrics
            if (gatewayData.response_time) {
                this.updateOrCreateMetric($card, 'Response Time', gatewayData.response_time + 's');
            }
            
            if (gatewayData.uptime_percentage) {
                this.updateOrCreateMetric($card, 'Uptime (24h)', gatewayData.uptime_percentage + '%');
            }
            
            // Update last transaction info if available
            if (gatewayData.last_transaction) {
                var transactionDate = new Date(gatewayData.last_transaction.date);
                var timeAgo = this.getTimeAgo(transactionDate);
                this.updateOrCreateMetric($card, 'Last Transaction', timeAgo + ' ago');
            }
            
            // Update error info if available
            var $errorSection = $card.find('.fa-gateway-error');
            if (gatewayData.last_error) {
                var errorDate = new Date(gatewayData.last_error.date);
                var errorTimeAgo = this.getTimeAgo(errorDate);
                
                if ($errorSection.length === 0) {
                    // Create error section if it doesn't exist
                    $errorSection = $('<div class="fa-gateway-error">' +
                        '<h4>Last Error</h4>' +
                        '<p>' + gatewayData.last_error.message + '</p>' +
                        '<small>' + errorTimeAgo + ' ago</small>' +
                        '</div>');
                    
                    // Insert before actions section
                    $card.find('.fa-gateway-actions').before($errorSection);
                } else {
                    // Update existing error section
                    $errorSection.find('p').text(gatewayData.last_error.message);
                    $errorSection.find('small').text(errorTimeAgo + ' ago');
                }
            } else if ($errorSection.length > 0) {
                // Remove error section if no errors
                $errorSection.slideUp(200, function() {
                    $(this).remove();
                });
            }
        },

        /**
         * Update or create a metric in a gateway card
         * 
         * @param {jQuery} $card Card element
         * @param {string} label Metric label
         * @param {string} value Metric value
         */
        updateOrCreateMetric: function($card, label, value) {
            var $metrics = $card.find('.fa-gateway-metrics');
            var $metric = $metrics.find('.fa-metric').filter(function() {
                return $(this).find('.fa-metric-label').text() === label;
            });
            
            if ($metric.length > 0) {
                // Update existing metric
                $metric.find('.fa-metric-value').text(value);
            } else {
                // Create new metric
                $metric = $('<div class="fa-metric">' +
                    '<span class="fa-metric-label">' + label + '</span>' +
                    '<span class="fa-metric-value">' + value + '</span>' +
                    '</div>');
                $metrics.append($metric);
            }
        },

        /**
         * Update the "last checked" time display
         * 
         * @param {string} timeText Text to display
         */
        updateLastCheckedTime: function(timeText) {
            $('#fa-last-checked-time').text(timeText || 'Just now');
        },

        /**
         * Calculate time ago from a date
         * 
         * @param {Date} date The date to calculate from
         * @return {string} Human-readable time difference
         */
        getTimeAgo: function(date) {
            var seconds = Math.floor((new Date() - date) / 1000);
            
            var interval = Math.floor(seconds / 31536000);
            if (interval > 1) {
                return interval + " years";
            }
            
            interval = Math.floor(seconds / 2592000);
            if (interval > 1) {
                return interval + " months";
            }
            
            interval = Math.floor(seconds / 86400);
            if (interval > 1) {
                return interval + " days";
            }
            
            interval = Math.floor(seconds / 3600);
            if (interval > 1) {
                return interval + " hours";
            }
            
            interval = Math.floor(seconds / 60);
            if (interval > 1) {
                return interval + " minutes";
            }
            
            return Math.floor(seconds) + " seconds";
        },

        /**
         * Get CSS class for a status
         * 
         * @param {string} status Status code
         * @return {string} CSS class
         */
        getStatusClass: function(status) {
            var classes = {
                'operational': 'fa-status-success',
                'issues_detected': 'fa-status-warning',
                'unconfigured': 'fa-status-neutral',
                'unknown': 'fa-status-neutral'
            };
            
            return classes[status] || 'fa-status-neutral';
        },

        /**
         * Get readable text for a status
         * 
         * @param {string} status Status code
         * @return {string} Human-readable status text
         */
        getStatusText: function(status) {
            if (typeof faGatewayMonitoring !== 'undefined' && faGatewayMonitoring.i18n && faGatewayMonitoring.i18n[status]) {
                return faGatewayMonitoring.i18n[status];
            }
            
            var texts = {
                'operational': 'Operational',
                'issues_detected': 'Issues Detected',
                'unconfigured': 'Not Configured',
                'unknown': 'Unknown Status'
            };
            
            return texts[status] || 'Unknown Status';
        },

        /**
         * Set up diagnostic test buttons
         */
        setupDiagnosticTestButtons: function() {
            $('.fa-run-test').on('click', function(e) {
                e.preventDefault();
                
                var gateway = $(this).data('gateway');
                var $button = $(this);
                
                $button.addClass('running-test').text('Running Test...');
                
                $.ajax({
                    url: faGatewayMonitoring.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fa_run_gateway_test',
                        gateway: gateway,
                        nonce: faGatewayMonitoring.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            // Open test results in a modal or new page
                            if (response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                // TODO: Show results in a modal
                                alert('Test completed. Check logs for details.');
                                GatewayMonitor.refreshAllGateways();
                            }
                        } else {
                            alert('Test failed: ' + (response.data.message || 'Unknown error'));
                        }
                    },
                    complete: function() {
                        $button.removeClass('running-test').text('Run Diagnostic Test');
                    }
                });
            });
        },
        
        /**
         * Set up log viewer links
         */
        setupLogViewers: function() {
            $('.fa-view-logs').on('click', function(e) {
                e.preventDefault();
                
                var gateway = $(this).data('gateway');
                var logViewUrl = faGatewayMonitoring.admin_url + '?page=financial-advisor-logs&gateway=' + gateway;
                
                window.location.href = logViewUrl;
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        GatewayMonitor.init();
    });

})(jQuery);
