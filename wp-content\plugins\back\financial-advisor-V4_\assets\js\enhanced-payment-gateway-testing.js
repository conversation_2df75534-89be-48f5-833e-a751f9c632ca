/**
 * Enhanced Payment Gateway Testing JS Module
 * 
 * Provides improved user interface for payment gateway testing
 */
(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        if ($('.payment-gateway-testing-container').length) {
            initEnhancedPaymentGatewayTesting();
        }
    });
    
    /**
     * Initialize enhanced payment gateway testing UI
     */
    function initEnhancedPaymentGatewayTesting() {
        console.log('🧪 Initializing Enhanced Payment Gateway Testing');
        
        // Real-time diagnostic monitoring
        setupRealTimeMonitoring();
        
        // Gateway selection for targeted tests
        $('#gateway-selector').on('change', function() {
            updateTestInterface($(this).val());
        });
        
        // Run automated tests button
        $('#run-automated-tests').on('click', function() {
            runAutomatedTests();
        });
        
        // Run advanced diagnostic button
        $('#run-advanced-diagnostic').on('click', function() {
            const gateway = $('#gateway-selector').val();
            runAdvancedDiagnostic(gateway);
        });
        
        // Test specific endpoint button
        $('#test-endpoint').on('click', function() {
            const gateway = $('#gateway-selector').val();
            const endpoint = $('#endpoint-selector').val();
            testSpecificEndpoint(gateway, endpoint);
        });
        
        // Export diagnostic report
        $('#export-diagnostic-report').on('click', function() {
            exportEnhancedDiagnosticReport();
        });
        
        // Transaction flow tester
        $('#test-transaction-flow').on('click', function() {
            const gateway = $('#gateway-selector').val();
            testTransactionFlow(gateway);
        });
        
        // Performance tester
        $('#test-gateway-performance').on('click', function() {
            const gateway = $('#gateway-selector').val();
            const testCount = $('#performance-test-count').val();
            testGatewayPerformance(gateway, testCount);
        });
        
        // View diagnostic history
        $('#view-diagnostic-history').on('click', function() {
            const gateway = $('#gateway-selector').val();
            viewDiagnosticHistory(gateway);
        });
        
        // Initialize tabs
        initTestingTabs();
        
        // Initialize real-time charts
        initDiagnosticCharts();
        
        // Initialize endpoint selectors
        updateEndpointSelectors($('#gateway-selector').val());
    }
    
    /**
     * Set up real-time monitoring interface
     */
    function setupRealTimeMonitoring() {
        console.log('Setting up real-time monitoring');
        
        // Initialize monitoring data
        const monitoringData = {
            paypal: {
                status: 'unknown',
                lastChecked: null,
                responseTime: [],
                successRate: [],
                recentActivity: []
            },
            stripe: {
                status: 'unknown',
                lastChecked: null,
                responseTime: [],
                successRate: [],
                recentActivity: []
            }
        };
        
        // Set up monitoring charts
        setupMonitoringCharts();
        
        // Start monitoring loop if enabled
        if ($('#enable-real-time-monitoring').is(':checked')) {
            startMonitoringLoop();
        }
        
        // Toggle monitoring on/off
        $('#enable-real-time-monitoring').on('change', function() {
            if ($(this).is(':checked')) {
                startMonitoringLoop();
                $('#monitoring-status').text('Active').addClass('status-active').removeClass('status-inactive');
            } else {
                stopMonitoringLoop();
                $('#monitoring-status').text('Inactive').addClass('status-inactive').removeClass('status-active');
            }
        });
        
        // Monitoring interval selector
        $('#monitoring-interval').on('change', function() {
            if ($('#enable-real-time-monitoring').is(':checked')) {
                // Restart monitoring with new interval
                stopMonitoringLoop();
                startMonitoringLoop();
            }
        });
        
        /**
         * Start the gateway monitoring loop
         */
        function startMonitoringLoop() {
            console.log('Starting monitoring loop');
            
            // Clear any existing interval
            stopMonitoringLoop();
            
            // Get monitoring interval (in seconds)
            const intervalSeconds = parseInt($('#monitoring-interval').val()) || 60;
            
            // Store the interval ID in a data attribute
            $('#monitoring-container').data('monitoringInterval', setInterval(function() {
                checkGatewayStatus('paypal');
                checkGatewayStatus('stripe');
                updateMonitoringTimestamp();
            }, intervalSeconds * 1000));
            
            // Initial check
            checkGatewayStatus('paypal');
            checkGatewayStatus('stripe');
            updateMonitoringTimestamp();
        }
        
        /**
         * Stop the gateway monitoring loop
         */
        function stopMonitoringLoop() {
            const intervalId = $('#monitoring-container').data('monitoringInterval');
            if (intervalId) {
                clearInterval(intervalId);
                $('#monitoring-container').data('monitoringInterval', null);
                console.log('Monitoring loop stopped');
            }
        }
        
        /**
         * Check gateway status via AJAX
         * 
         * @param {string} gateway Gateway to check (paypal or stripe)
         */
        function checkGatewayStatus(gateway) {
            const startTime = new Date().getTime();
            
            // Update UI to show check in progress
            $(`#${gateway}-status-indicator`)
                .removeClass('status-good status-warning status-error status-unknown')
                .addClass('status-checking')
                .html('<span class="spinner"></span>');
            
            // Make AJAX request to check gateway status
            jQuery.ajax({
                url: paymentGatewayTesting.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'check_gateway_status',
                    gateway: gateway,
                    nonce: paymentGatewayTesting.nonce
                },
                success: function(response) {
                    const endTime = new Date().getTime();
                    const responseTime = endTime - startTime;
                    
                    // Update monitoring data
                    monitoringData[gateway].lastChecked = new Date();
                    monitoringData[gateway].responseTime.push(responseTime);
                    
                    // Trim arrays if they get too long
                    if (monitoringData[gateway].responseTime.length > 20) {
                        monitoringData[gateway].responseTime.shift();
                    }
                    
                    if (response.success) {
                        // Process successful response
                        monitoringData[gateway].status = response.data.status;
                        monitoringData[gateway].successRate.push(100);
                        
                        if (response.data.recent_activity) {
                            monitoringData[gateway].recentActivity = response.data.recent_activity.concat(
                                monitoringData[gateway].recentActivity
                            ).slice(0, 10); // Keep only most recent 10 activities
                        }
                        
                        // Update UI
                        updateGatewayStatusUI(gateway, response.data.status, responseTime);
                        updateGatewayActivityLog(gateway, monitoringData[gateway].recentActivity);
                    } else {
                        // Process error response
                        monitoringData[gateway].status = 'error';
                        monitoringData[gateway].successRate.push(0);
                        
                        // Add error to recent activity
                        monitoringData[gateway].recentActivity.unshift({
                            time: new Date().toISOString(),
                            type: 'error',
                            message: response.data.message || 'Unknown error occurred'
                        });
                        monitoringData[gateway].recentActivity = monitoringData[gateway].recentActivity.slice(0, 10);
                        
                        // Update UI
                        updateGatewayStatusUI(gateway, 'error', responseTime, response.data.message);
                        updateGatewayActivityLog(gateway, monitoringData[gateway].recentActivity);
                    }
                    
                    // Trim success rate array if it gets too long
                    if (monitoringData[gateway].successRate.length > 20) {
                        monitoringData[gateway].successRate.shift();
                    }
                    
                    // Update charts
                    updateMonitoringCharts();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    const endTime = new Date().getTime();
                    const responseTime = endTime - startTime;
                    
                    // Update monitoring data
                    monitoringData[gateway].lastChecked = new Date();
                    monitoringData[gateway].status = 'error';
                    monitoringData[gateway].responseTime.push(responseTime);
                    monitoringData[gateway].successRate.push(0);
                    
                    // Add error to recent activity
                    monitoringData[gateway].recentActivity.unshift({
                        time: new Date().toISOString(),
                        type: 'error',
                        message: `AJAX error: ${textStatus} - ${errorThrown}`
                    });
                    monitoringData[gateway].recentActivity = monitoringData[gateway].recentActivity.slice(0, 10);
                    
                    // Update UI
                    updateGatewayStatusUI(gateway, 'error', responseTime, `AJAX error: ${textStatus}`);
                    updateGatewayActivityLog(gateway, monitoringData[gateway].recentActivity);
                    
                    // Trim arrays if they get too long
                    if (monitoringData[gateway].responseTime.length > 20) {
                        monitoringData[gateway].responseTime.shift();
                    }
                    if (monitoringData[gateway].successRate.length > 20) {
                        monitoringData[gateway].successRate.shift();
                    }
                    
                    // Update charts
                    updateMonitoringCharts();
                }
            });
        }
        
        /**
         * Update gateway status UI
         * 
         * @param {string} gateway Gateway name
         * @param {string} status Status (ok, warning, error, unknown)
         * @param {number} responseTime Response time in ms
         * @param {string} message Optional error message
         */
        function updateGatewayStatusUI(gateway, status, responseTime, message = '') {
            const statusIndicator = $(`#${gateway}-status-indicator`);
            const responseTimeElement = $(`#${gateway}-response-time`);
            const lastCheckedElement = $(`#${gateway}-last-checked`);
            
            // Remove all status classes and add the appropriate one
            statusIndicator
                .removeClass('status-checking status-good status-warning status-error status-unknown');
            
            // Set status text and class
            switch (status) {
                case 'ok':
                    statusIndicator.text('Good').addClass('status-good');
                    break;
                case 'warning':
                    statusIndicator.text('Warning').addClass('status-warning');
                    break;
                case 'error':
                    statusIndicator.text('Error').addClass('status-error');
                    if (message) {
                        statusIndicator.attr('title', message);
                    }
                    break;
                default:
                    statusIndicator.text('Unknown').addClass('status-unknown');
            }
            
            // Update response time
            responseTimeElement.text(`${responseTime}ms`);
            
            // Update last checked time
            lastCheckedElement.text(new Date().toLocaleTimeString());
        }
        
        /**
         * Update gateway activity log
         * 
         * @param {string} gateway Gateway name
         * @param {Array} activities List of recent activities
         */
        function updateGatewayActivityLog(gateway, activities) {
            const activityLog = $(`#${gateway}-activity-log`);
            activityLog.empty();
            
            if (activities.length === 0) {
                activityLog.append('<li class="no-activity">No recent activity</li>');
                return;
            }
            
            activities.forEach(activity => {
                const time = new Date(activity.time).toLocaleTimeString();
                const typeClass = `activity-${activity.type}`;
                
                activityLog.append(`
                    <li class="${typeClass}">
                        <span class="activity-time">${time}</span>
                        <span class="activity-message">${activity.message}</span>
                    </li>
                `);
            });
        }
        
        /**
         * Update monitoring timestamp
         */
        function updateMonitoringTimestamp() {
            $('#monitoring-last-update').text(new Date().toLocaleString());
        }
        
        /**
         * Update monitoring charts with latest data
         */
        function updateMonitoringCharts() {
            // Get time labels (last 20 checks)
            const timeLabels = [];
            const now = new Date();
            
            for (let i = 0; i < Math.max(
                monitoringData.paypal.responseTime.length, 
                monitoringData.stripe.responseTime.length
            ); i++) {
                // Create labels showing minutes ago
                const minutesAgo = i;
                timeLabels.unshift(minutesAgo === 0 ? 'now' : `${minutesAgo}m ago`);
            }
            
            // Update response time chart
            if (window.responseTimeChart) {
                window.responseTimeChart.data.labels = timeLabels;
                window.responseTimeChart.data.datasets[0].data = monitoringData.paypal.responseTime;
                window.responseTimeChart.data.datasets[1].data = monitoringData.stripe.responseTime;
                window.responseTimeChart.update();
            }
            
            // Update success rate chart
            if (window.successRateChart) {
                window.successRateChart.data.labels = timeLabels;
                window.successRateChart.data.datasets[0].data = monitoringData.paypal.successRate;
                window.successRateChart.data.datasets[1].data = monitoringData.stripe.successRate;
                window.successRateChart.update();
            }
        }
    }
    
    /**
     * Initialize testing tabs
     */
    function initTestingTabs() {
        $('.testing-tabs-nav a').on('click', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const target = $this.attr('href');
            
            // Update active tab
            $('.testing-tabs-nav a').removeClass('active');
            $this.addClass('active');
            
            // Show target content
            $('.testing-tab-content').removeClass('active');
            $(target).addClass('active');
        });
        
        // Initialize with first tab active
        $('.testing-tabs-nav a:first').trigger('click');
    }
    
    /**
     * Initialize diagnostic charts
     */
    function initDiagnosticCharts() {
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, charts will not be displayed');
            return;
        }
        
        // Response time chart
        const responseTimeCtx = document.getElementById('response-time-chart');
        if (responseTimeCtx) {
            window.responseTimeChart = new Chart(responseTimeCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Success rate chart
        const successRateCtx = document.getElementById('success-rate-chart');
        if (successRateCtx) {
            window.successRateChart = new Chart(successRateCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Success Rate (%)',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        tension: 0.1,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
    }
    
    /**
     * Update test interface based on selected gateway
     */
    function updateTestInterface(gateway) {
        // Update endpoint selectors
        updateEndpointSelectors(gateway);
        
        // Update UI elements specific to the gateway
        $('.gateway-specific').hide();
        $(`.gateway-specific[data-gateway="${gateway}"]`).show();
        
        // Update test panel title
        $('#gateway-test-panel-title').text(`${gateway.charAt(0).toUpperCase() + gateway.slice(1)} Gateway Tests`);
    }
    
    /**
     * Update endpoint selectors based on gateway
     */
    function updateEndpointSelectors(gateway) {
        const $endpointSelector = $('#endpoint-selector');
        $endpointSelector.empty();
        
        if (gateway === 'paypal') {
            $endpointSelector.append('<option value="auth">Authentication Endpoint</option>');
            $endpointSelector.append('<option value="orders">Orders API</option>');
            $endpointSelector.append('<option value="webhooks">Webhooks</option>');
        } else if (gateway === 'stripe') {
            $endpointSelector.append('<option value="auth">Authentication Endpoint</option>');
            $endpointSelector.append('<option value="payment_intents">Payment Intents</option>');
            $endpointSelector.append('<option value="webhooks">Webhooks</option>');
        }
    }
    
    /**
     * Run automated gateway tests
     */
    function runAutomatedTests() {
        const $resultsContainer = $('#test-results-container');
        
        // Show loading indicator
        $resultsContainer.html('<div class="testing-loading">Running automated tests...<div class="spinner"></div></div>');
        
        // Run tests via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_payment_gateway_tests',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render test results
                    renderTestResults($resultsContainer, response.data);
                } else {
                    $resultsContainer.html('<div class="error-message">Error running tests</div>');
                }
            },
            error: function() {
                $resultsContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * Run advanced diagnostic
     */
    function runAdvancedDiagnostic(gateway) {
        const $diagnosticContainer = $('#diagnostic-results-container');
        
        // Show loading indicator
        $diagnosticContainer.html('<div class="testing-loading">Running advanced diagnostic...<div class="spinner"></div></div>');
        
        // Run diagnostic via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_payment_gateway_diagnostic',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render diagnostic results
                    renderDiagnosticResults($diagnosticContainer, response.data);
                } else {
                    $diagnosticContainer.html('<div class="error-message">Error running diagnostic</div>');
                }
            },
            error: function() {
                $diagnosticContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * Test specific endpoint
     */
    function testSpecificEndpoint(gateway, endpoint) {
        const $endpointContainer = $('#endpoint-test-results');
        
        // Show loading indicator
        $endpointContainer.html('<div class="testing-loading">Testing endpoint...<div class="spinner"></div></div>');
        
        // Run endpoint test via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_endpoint_test',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                endpoint: endpoint
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render endpoint test results
                    renderEndpointResults($endpointContainer, response.data);
                    
                    // Update chart if available
                    if (response.data.endpoint_test && 
                        response.data.endpoint_test.tests && 
                        response.data.endpoint_test.tests.endpoint_access && 
                        response.data.endpoint_test.tests.endpoint_access.response_time) {
                        
                        updateResponseTimeChart(
                            endpoint, 
                            response.data.endpoint_test.tests.endpoint_access.response_time
                        );
                    }
                } else {
                    $endpointContainer.html('<div class="error-message">Error testing endpoint</div>');
                }
            },
            error: function() {
                $endpointContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * Test transaction flow
     */
    function testTransactionFlow(gateway) {
        const $flowContainer = $('#transaction-flow-container');
        
        // Show loading indicator
        $flowContainer.html('<div class="testing-loading">Testing transaction flow...<div class="spinner"></div></div>');
        
        // Run transaction flow test via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_transaction_flow_test',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render transaction flow results
                    renderTransactionFlowResults($flowContainer, response.data);
                } else {
                    $flowContainer.html('<div class="error-message">Error testing transaction flow</div>');
                }
            },
            error: function() {
                $flowContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * Test gateway performance
     */
    function testGatewayPerformance(gateway, testCount) {
        const $perfContainer = $('#performance-test-container');
        
        // Show loading indicator
        $perfContainer.html('<div class="testing-loading">Running performance test...<div class="spinner"></div></div>');
        
        // Run performance test via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'check_gateway_performance',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                test_count: testCount
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render performance test results
                    renderPerformanceResults($perfContainer, response.data);
                } else {
                    $perfContainer.html('<div class="error-message">Error testing performance</div>');
                }
            },
            error: function() {
                $perfContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * View diagnostic history
     */
    function viewDiagnosticHistory(gateway) {
        const $historyContainer = $('#diagnostic-history-container');
        
        // Show loading indicator
        $historyContainer.html('<div class="testing-loading">Loading diagnostic history...<div class="spinner"></div></div>');
        
        // Get diagnostic history via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_diagnostic_history',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Render diagnostic history
                    renderDiagnosticHistory($historyContainer, response.data.history);
                } else {
                    $historyContainer.html('<div class="error-message">Error loading diagnostic history</div>');
                }
            },
            error: function() {
                $historyContainer.html('<div class="error-message">Error connecting to server</div>');
            }
        });
    }
    
    /**
     * Export diagnostic report
     */
    function exportEnhancedDiagnosticReport() {
        const gateway = $('#gateway-selector').val();
        const format = $('#export-format').val();
        const diagnosticId = $('#diagnostic-id').val() || 0;
        
        // Make export request via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'export_diagnostic_report',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                format: format,
                diagnostic_id: diagnosticId
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Create download for the content
                    const blob = new Blob([response.data.content], { type: response.data.mime_type });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = response.data.filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    alert('Error exporting diagnostic report');
                }
            },
            error: function() {
                alert('Error connecting to server');
            }
        });
    }
    
    /**
     * Start real-time monitoring
     */
    let monitoringInterval = null;
    function startRealTimeMonitoring() {
        const gateway = $('#gateway-selector').val();
        let monitoringData = {
            responseTimes: [],
            successRates: [],
            timestamps: []
        };
        
        // Clear any existing interval
        if (monitoringInterval) {
            clearInterval(monitoringInterval);
        }
        
        // Run initial test
        runMonitoringCheck(gateway, monitoringData);
        
        // Set interval for repeated checks
        monitoringInterval = setInterval(function() {
            runMonitoringCheck(gateway, monitoringData);
        }, 60000); // Check every minute
    }
    
    /**
     * Stop real-time monitoring
     */
    function stopRealTimeMonitoring() {
        if (monitoringInterval) {
            clearInterval(monitoringInterval);
            monitoringInterval = null;
        }
    }
    
    /**
     * Run a single monitoring check
     */
    function runMonitoringCheck(gateway, monitoringData) {
        // Test authentication endpoint for real-time monitoring
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_endpoint_test',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                endpoint: 'auth'
            },
            success: function(response) {
                if (response.success && response.data && 
                    response.data.endpoint_test && 
                    response.data.endpoint_test.tests &&
                    response.data.endpoint_test.tests.endpoint_access) {
                    
                    const check = response.data.endpoint_test.tests.endpoint_access;
                    const now = new Date();
                    const timeStr = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0');
                    
                    // Update monitoring data
                    monitoringData.timestamps.push(timeStr);
                    monitoringData.responseTimes.push(check.response_time);
                    monitoringData.successRates.push(check.result ? 100 : 0);
                    
                    // Keep only the last 30 data points
                    if (monitoringData.timestamps.length > 30) {
                        monitoringData.timestamps.shift();
                        monitoringData.responseTimes.shift();
                        monitoringData.successRates.shift();
                    }
                    
                    // Update charts
                    updateMonitoringCharts(monitoringData);
                    
                    // Update metrics
                    updateMonitoringMetrics(monitoringData, check);
                    
                    // Update time of last check
                    $('#last-check-time').text(timeStr);
                    
                    // Add alert if needed
                    if (!check.result) {
                        addMonitoringAlert(gateway, check.message);
                    }
                }
            }
        });
    }
    
    /**
     * Update monitoring charts with new data
     */
    function updateMonitoringCharts(data) {
        if (window.responseTimeChart) {
            window.responseTimeChart.data.labels = data.timestamps;
            window.responseTimeChart.data.datasets[0].data = data.responseTimes;
            window.responseTimeChart.update();
        }
        
        if (window.successRateChart) {
            window.successRateChart.data.labels = data.timestamps;
            window.successRateChart.data.datasets[0].data = data.successRates;
            window.successRateChart.update();
        }
    }
    
    /**
     * Update monitoring metrics display
     */
    function updateMonitoringMetrics(data, check) {
        // Calculate average response time
        const avgResponseTime = data.responseTimes.reduce((a, b) => a + b, 0) / data.responseTimes.length;
        $('#avg-response-time').text(Math.round(avgResponseTime) + 'ms');
        
        // Calculate success rate
        const successRate = data.successRates.reduce((a, b) => a + b, 0) / data.successRates.length;
        $('#success-rate').text(Math.round(successRate) + '%');
        
        // Get class based on performance
        const responseTimeClass = avgResponseTime < 300 ? 'metric-good' : (avgResponseTime < 1000 ? 'metric-warning' : 'metric-poor');
        const successRateClass = successRate > 95 ? 'metric-good' : (successRate > 80 ? 'metric-warning' : 'metric-poor');
        
        // Apply classes
        $('#avg-response-time').removeClass('metric-good metric-warning metric-poor').addClass(responseTimeClass);
        $('#success-rate').removeClass('metric-good metric-warning metric-poor').addClass(successRateClass);
    }
    
    /**
     * Add an alert to the monitoring panel
     */
    function addMonitoringAlert(gateway, message) {
        const $alertsContainer = $('#alerts-container');
        const now = new Date();
        const timeStr = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0') + ':' + now.getSeconds().toString().padStart(2, '0');
        
        // Remove no alerts message if present
        $alertsContainer.find('.no-alerts-message').remove();
        
        // Add new alert
        $alertsContainer.prepend(`
            <div class="alert-item">
                <div class="alert-time">${timeStr}</div>
                <div class="alert-gateway">${gateway.toUpperCase()}</div>
                <div class="alert-message">${message}</div>
            </div>
        `);
        
        // Keep only the last 10 alerts
        if ($alertsContainer.children().length > 10) {
            $alertsContainer.children().last().remove();
        }
        
        // Update error count
        const $errorCount = $('#error-count');
        let currentCount = parseInt($errorCount.text()) || 0;
        if (!isNaN(currentCount)) {
            $errorCount.text(currentCount + 1);
        }
    }
    
    /**
     * Update response time chart with new data
     */
    function updateResponseTimeChart(endpoint, responseTime) {
        if (window.responseTimeChart) {
            // Add new data point
            const now = new Date();
            const timeStr = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0');
            
            window.responseTimeChart.data.labels.push(timeStr + ' ' + endpoint);
            window.responseTimeChart.data.datasets[0].data.push(responseTime);
            
            // Keep only the last 10 data points
            if (window.responseTimeChart.data.labels.length > 10) {
                window.responseTimeChart.data.labels.shift();
                window.responseTimeChart.data.datasets[0].data.shift();
            }
            
            window.responseTimeChart.update();
        }
    }
    
    /**
     * Render test results in the container
     */
    function renderTestResults($container, results) {
        let html = '<div class="test-results-wrapper">';
        
        // Iterate through each test category
        for (const categoryKey in results) {
            const category = results[categoryKey];
            if (typeof category !== 'object' || !category.title || !category.tests) {
                continue;
            }
            
            html += `<div class="test-category">
                <h3>${category.title}</h3>
                <table class="test-results-table">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Result</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>`;
            
            // Add each test in this category
            for (const testKey in category.tests) {
                const test = category.tests[testKey];
                const resultClass = test.result ? 'test-success' : 'test-fail';
                const resultIcon = test.result ? '✓' : '✗';
                
                html += `<tr>
                    <td>${test.name}</td>
                    <td class="${resultClass}">${resultIcon}</td>
                    <td>${test.message}</td>
                </tr>`;
            }
            
            html += `</tbody></table></div>`;
        }
        
        html += `<div class="test-summary">
            <button type="button" id="export-test-results" class="button">Export Results</button>
            <button type="button" id="run-tests-again" class="button button-primary">Run Tests Again</button>
        </div>`;
        
        html += '</div>';
        
        $container.html(html);
        
        // Add event handlers for buttons
        $('#run-tests-again').on('click', function() {
            runAutomatedTests();
        });
        
        $('#export-test-results').on('click', function() {
            exportTestResults(results);
        });
    }
    
    /**
     * Render diagnostic results
     */
    function renderDiagnosticResults($container, results) {
        let html = '<div class="diagnostic-results-wrapper">';
        
        // Add diagnostic title and timestamp
        html += `<div class="diagnostic-header">
            <h3>${results.title || 'Diagnostic Results'}</h3>
            <div class="diagnostic-timestamp">Generated: ${results.timestamp || new Date().toLocaleString()}</div>
        </div>`;
        
        // Iterate through result sections
        for (const key in results) {
            const section = results[key];
            if (typeof section !== 'object' || !section.title || !section.tests) {
                continue;
            }
            
            html += `<div class="diagnostic-section">
                <h4>${section.title}</h4>
                <table class="diagnostic-results-table">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Result</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>`;
            
            // Add test rows
            for (const testKey in section.tests) {
                const test = section.tests[testKey];
                const resultClass = test.result ? 'test-success' : (test.critical ? 'test-critical' : 'test-fail');
                const resultIcon = test.result ? '✓' : '✗';
                
                html += `<tr>
                    <td>${test.name}</td>
                    <td class="${resultClass}">${resultIcon}</td>
                    <td>${test.message}</td>
                </tr>`;
            }
            
            html += `</tbody></table></div>`;
        }
        
        html += `<div class="diagnostic-actions">
            <button type="button" id="export-diagnostic-results" class="button">Export Report</button>
            <button type="button" id="run-diagnostic-again" class="button button-primary">Run Diagnostic Again</button>
        </div>`;
        
        html += '</div>';
        
        $container.html(html);
        
        // Add event handlers for buttons
        $('#run-diagnostic-again').on('click', function() {
            runAdvancedDiagnostic($('#gateway-selector').val());
        });
        
        $('#export-diagnostic-results').on('click', function() {
            exportDiagnosticResults(results);
        });
    }
    
    /**
     * Render endpoint test results
     */
    function renderEndpointResults($container, results) {
        if (!results.endpoint_test || !results.endpoint_test.tests) {
            $container.html('<div class="error-message">Invalid endpoint test results</div>');
            return;
        }
        
        const test = results.endpoint_test.tests.endpoint_access;
        const resultClass = test.result ? 'test-success' : 'test-fail';
        const responseTime = test.response_time ? `${test.response_time}ms` : 'N/A';
        
        let html = `<div class="endpoint-test-result ${resultClass}">
            <h4>${results.endpoint_test.title}</h4>
            <div class="endpoint-result-details">
                <div class="endpoint-result-status">
                    Status: <span class="${resultClass}">${test.result ? 'Success' : 'Failed'}</span>
                </div>
                <div class="endpoint-result-message">
                    ${test.message}
                </div>
                <div class="endpoint-result-response-time">
                    Response Time: ${responseTime}
                </div>
            </div>
        </div>`;
        
        $container.html(html);
    }
    
    /**
     * Render transaction flow results
     */
    function renderTransactionFlowResults($container, results) {
        if (!results.tests) {
            $container.html('<div class="error-message">Invalid transaction flow test results</div>');
            return;
        }
        
        let html = `<div class="transaction-flow-results">
            <h4>${results.title || 'Transaction Flow Test'}</h4>
            <div class="transaction-flow-diagram">`;
        
        // Build visual flow diagram
        html += `<div class="flow-diagram">`;
        
        // Track critical failure
        let hasCriticalFailure = false;
        let lastSuccessful = true;
        
        // Add steps to flow diagram
        for (const testKey in results.tests) {
            const test = results.tests[testKey];
            const resultClass = test.result ? 'flow-step-success' : 'flow-step-fail';
            const stepIcon = test.result ? '✓' : '✗';
            
            // Check for critical failure
            if (!test.result && test.critical) {
                hasCriticalFailure = true;
            }
            
            // Add connector line between steps (except for first step)
            if (testKey !== 'missing_config') {
                const connectorClass = lastSuccessful && test.result ? 'connector-success' : 'connector-fail';
                html += `<div class="flow-connector ${connectorClass}"></div>`;
            }
            
            // Add the step
            html += `<div class="flow-step ${resultClass}">
                <div class="flow-step-icon">${stepIcon}</div>
                <div class="flow-step-name">${test.name}</div>
                <div class="flow-step-message">${test.message}</div>
            </div>`;
            
            lastSuccessful = test.result;
            
            // Stop showing steps after critical failure
            if (hasCriticalFailure && test.critical && !test.result) {
                break;
            }
        }
        
        html += `</div>`; // Close flow-diagram
        
        // Overall result
        const overallResult = hasCriticalFailure ? 'Critical Failure' : (lastSuccessful ? 'Success' : 'Issues Detected');
        const overallClass = hasCriticalFailure ? 'test-critical' : (lastSuccessful ? 'test-success' : 'test-warning');
        
        html += `<div class="transaction-flow-summary ${overallClass}">
            <div class="summary-title">Overall Result:</div>
            <div class="summary-result">${overallResult}</div>
        </div>`;
        
        html += `</div></div>`; // Close transaction-flow-diagram and transaction-flow-results
        
        $container.html(html);
    }
    
    /**
     * Render performance test results
     */
    function renderPerformanceResults($container, results) {
        if (!results.endpoints) {
            $container.html('<div class="error-message">Invalid performance test results</div>');
            return;
        }
        
        let html = `<div class="performance-results">
            <h4>${results.title || 'Performance Test'}</h4>
            <div class="performance-summary">
                <div class="summary-item">Test Count: <strong>${results.test_count}</strong></div>
                <div class="summary-item">Timestamp: <strong>${results.timestamp}</strong></div>
            </div>
            <div class="endpoints-performance">`;
        
        // Add results for each endpoint
        for (const endpointKey in results.endpoints) {
            const endpoint = results.endpoints[endpointKey];
            const successRateClass = endpoint.success_rate > 90 ? 'success-high' : 
                (endpoint.success_rate > 70 ? 'success-medium' : 'success-low');
            
            html += `<div class="endpoint-performance">
                <h5>${endpoint.name}</h5>
                <div class="performance-metrics">
                    <div class="metric">
                        <div class="metric-name">Avg. Response Time</div>
                        <div class="metric-value">${endpoint.avg_time}ms</div>
                    </div>
                    <div class="metric">
                        <div class="metric-name">Min Response Time</div>
                        <div class="metric-value">${endpoint.min_time}ms</div>
                    </div>
                    <div class="metric">
                        <div class="metric-name">Max Response Time</div>
                        <div class="metric-value">${endpoint.max_time}ms</div>
                    </div>
                    <div class="metric">
                        <div class="metric-name">Success Rate</div>
                        <div class="metric-value ${successRateClass}">${endpoint.success_rate}%</div>
                    </div>
                </div>
            </div>`;
        }
        
        html += `</div></div>`;
        
        $container.html(html);
    }
    
    /**
     * Render diagnostic history
     */
    function renderDiagnosticHistory($container, history) {
        if (!history || !history.length) {
            $container.html('<div class="no-history-message">No diagnostic history found</div>');
            return;
        }
        
        let html = `<div class="diagnostic-history">
            <table class="history-table">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Gateway</th>
                        <th>Success</th>
                        <th>Failures</th>
                        <th>Critical</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>`;
        
        // Add history rows
        history.forEach(item => {
            const statusClass = item.status === 'critical' ? 'status-critical' : 
                (item.status === 'warning' ? 'status-warning' : 'status-success');
            
            html += `<tr>
                <td>${item.time}</td>
                <td>${item.gateway}</td>
                <td>${item.success_count}</td>
                <td>${item.fail_count}</td>
                <td>${item.critical_count}</td>
                <td><span class="status-badge ${statusClass}">${item.status}</span></td>
                <td><button type="button" class="button view-details-btn" data-id="${item.id}">View Details</button></td>
            </tr>`;
        });
        
        html += `</tbody></table></div>`;
        
        $container.html(html);
        
        // Add event handlers for view details buttons
        $('.view-details-btn').on('click', function() {
            const diagnosticId = $(this).data('id');
            viewDiagnosticDetails(diagnosticId);
        });
    }
    
    /**
     * Export test results as JSON or CSV
     */
    function exportTestResults(results) {
        const format = $('#export-format').val() || 'json';
        const filename = 'payment_gateway_tests_' + new Date().toISOString().split('T')[0] + '.' + format;
        let content;
        
        if (format === 'json') {
            content = JSON.stringify(results, null, 2);
        } else {
            // Simple CSV conversion
            content = 'Category,Test,Result,Message\n';
            
            for (const categoryKey in results) {
                const category = results[categoryKey];
                if (typeof category !== 'object' || !category.title || !category.tests) {
                    continue;
                }
                
                for (const testKey in category.tests) {
                    const test = category.tests[testKey];
                    content += `"${category.title}","${test.name}","${test.result ? 'Pass' : 'Fail'}","${test.message}"\n`;
                }
            }
        }
        
        // Create and trigger download
        const blob = new Blob([content], { type: format === 'json' ? 'application/json' : 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }
    
    /**
     * Export diagnostic results
     */
    function exportDiagnosticResults(results) {
        exportEnhancedDiagnosticReport();
    }
    
    /**
     * View diagnostic details by ID
     */
    function viewDiagnosticDetails(diagnosticId) {
        // Set the diagnostic ID in the hidden field
        $('#diagnostic-id').val(diagnosticId);
        
        // Run export with HTML format
        const gateway = $('#gateway-selector').val();
        
        // Make export request via AJAX
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'export_diagnostic_report',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                format: 'html',
                diagnostic_id: diagnosticId
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Show the diagnostic details in a modal
                    showDiagnosticDetailsModal(response.data.content);
                } else {
                    alert('Error retrieving diagnostic details');
                }
            },
            error: function() {
                alert('Error connecting to server');
            }
        });
    }
    
    /**
     * Show diagnostic details in a modal
     */
    function showDiagnosticDetailsModal(content) {
        // Create modal if it doesn't exist
        if (!$('#diagnostic-details-modal').length) {
            $('body').append(`
                <div id="diagnostic-details-modal" class="modal">
                    <div class="modal-content">
                        <span class="modal-close">&times;</span>
                        <div id="diagnostic-details-content"></div>
                    </div>
                </div>
            `);
            
            // Modal close button
            $('.modal-close').on('click', function() {
                $('#diagnostic-details-modal').hide();
            });
            
            // Close modal when clicking outside
            $(window).on('click', function(event) {
                if ($(event.target).is('#diagnostic-details-modal')) {
                    $('#diagnostic-details-modal').hide();
                }
            });
        }
        
        // Set content and show modal
        $('#diagnostic-details-content').html(content);
        $('#diagnostic-details-modal').show();
    }
})(jQuery);
