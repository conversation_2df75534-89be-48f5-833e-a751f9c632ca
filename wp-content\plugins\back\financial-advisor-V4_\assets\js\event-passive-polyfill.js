/**
 * Polyfill for passive event listeners
 * This makes all touch/wheel events passive by default to improve scrolling performance
 */
(function() {
  // Check if the browser supports passive event listeners
  var supportsPassive = false;
  try {
    var opts = Object.defineProperty({}, 'passive', {
      get: function() {
        supportsPassive = true;
        return true;
      }
    });
    window.addEventListener('testPassive', null, opts);
    window.removeEventListener('testPassive', null, opts);
  } catch (e) {}

  // Override addEventListener to make touch/wheel events passive by default
  if (supportsPassive) {
    var originalAddEventListener = EventTarget.prototype.addEventListener;
    
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      var passiveEvents = ['touchstart', 'touchmove', 'wheel', 'mousewheel'];
      
      // Convert options to an object if it's not already
      var optionsObj = options;
      if (options === undefined || options === false) {
        optionsObj = {};
      } else if (options === true) {
        optionsObj = { capture: true };
      }
      
      // Make touch/wheel events passive by default
      if (passiveEvents.indexOf(type) !== -1 && optionsObj.passive === undefined) {
        optionsObj.passive = true;
      }
      
      // Call the original method with the modified options
      originalAddEventListener.call(this, type, listener, optionsObj);
    };
  }
})();
