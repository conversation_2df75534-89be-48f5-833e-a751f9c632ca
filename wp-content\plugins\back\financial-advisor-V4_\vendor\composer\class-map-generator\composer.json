{"name": "composer/class-map-generator", "description": "Utilities to scan PHP code and generate class maps.", "type": "library", "license": "MIT", "keywords": ["classmap"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "require": {"php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7", "composer/pcre": "^2.1 || ^3.1"}, "require-dev": {"phpunit/phpunit": "^8", "phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "symfony/filesystem": "^5.4 || ^6"}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\ClassMapGenerator\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "scripts": {"test": "@php phpunit", "phpstan": "@php phpstan analyse"}}