/**
 * Script di debug per il problema COST_RATE
 * 
 * Questo script raccoglie informazioni di debugging sul 
 * valore COST_RATE e sul tipo di abbonamento dell'utente.
 * Inserire questo script nella pagina per tracciare i valori.
 */

(function() {
    console.log('=== DEBUG COST_RATE inizializzato ===');
    
    // Intercetta e registra il valore COST_RATE ogni volta che viene modificato
    let originalCOST_RATE = window.COST_RATE;
    let originalCalculateEstimatedCost = window.calculateEstimatedCost;
    
    // Verifica se il valore COST_RATE è già stato definito
    if (typeof window.COST_RATE !== 'undefined') {
        console.log('COST_RATE iniziale:', window.COST_RATE);
    } else {
        console.log('COST_RATE non ancora definito');
    }
    
    // Traccia tutte le chiamate AJAX che potrebbero influenzare il COST_RATE
    const originalAjax = jQuery.ajax;
    jQuery.ajax = function(options) {
        if (options && options.data) {
            if (typeof options.data === 'string' && 
                (options.data.includes('get_document_stats') || 
                 options.data.includes('update_actual_cost_after_analysis'))) {
                console.log('Richiesta AJAX che potrebbe contenere cost_per_token:', options);
                
                // Intercetta la risposta
                const originalSuccess = options.success;
                options.success = function(response) {
                    if (response && response.success && response.data) {
                        console.log('Risposta AJAX con potenziali dati cost_per_token:', response);
                        
                        if (response.data.stats && response.data.stats.cost_per_token !== undefined) {
                            console.log('COST_PER_TOKEN ricevuto dal server:', response.data.stats.cost_per_token);
                        }
                        
                        if (response.data.user && response.data.user.role) {
                            console.log('Tipo di abbonamento utente:', response.data.user.role);
                        }
                    }
                    
                    if (originalSuccess) {
                        originalSuccess.apply(this, arguments);
                    }
                };
            }
        }
        
        return originalAjax.apply(this, arguments);
    };
      // Monitora gli aggiornamenti del valore COST_RATE
    setInterval(function() {
        if (window.COST_RATE !== originalCOST_RATE) {
            console.log('COST_RATE cambiato:', window.COST_RATE, '(valore precedente:', originalCOST_RATE, ')');
            console.log('USER_SUBSCRIPTION_TYPE attuale:', window.USER_SUBSCRIPTION_TYPE);
            originalCOST_RATE = window.COST_RATE;
        }
        
        // Verifica se la funzione di calcolo costo è stata sovrascritta
        if (window.calculateEstimatedCost !== originalCalculateEstimatedCost) {
            console.log('La funzione calculateEstimatedCost è stata modificata!');
            originalCalculateEstimatedCost = window.calculateEstimatedCost;
        }
    }, 1000);
    
    // Aggiungi un pulsante di debug nella console
    console.log('%c Debug COST_RATE ', 'background: #ff6b6b; color: white; font-weight: bold');
    console.log('Usa questo comando per visualizzare il valore corrente:');
    console.log('console.log("COST_RATE attuale:", window.COST_RATE, "Tipo abbonamento:", window.USER_SUBSCRIPTION_TYPE)');
})();
