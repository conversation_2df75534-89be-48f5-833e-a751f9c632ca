<?php
/**
 * Financial Academy Manager
 * 
 * Class per gestire le domande finanziarie preconfigurate
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Financial_Academy_Manager {
    /**
     * Table name for academy questions
     * @var string
     */
    private $questions_table;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->questions_table = $wpdb->prefix . 'financial_academy_questions';

        // Create table on plugin activation
        register_activation_hook(plugin_dir_path(dirname(__FILE__)) . 'document-advisor-plugin.php', array($this, 'create_questions_table'));

        // AJAX handlers
        add_action('wp_ajax_get_academy_questions', array($this, 'get_academy_questions_ajax'));
        add_action('wp_ajax_nopriv_get_academy_questions', array($this, 'get_academy_questions_ajax'));

        // AJAX handlers for admin
        add_action('wp_ajax_add_academy_question', array($this, 'add_academy_question_ajax'));
        add_action('wp_ajax_update_academy_question', array($this, 'update_academy_question_ajax'));
        add_action('wp_ajax_delete_academy_question', array($this, 'delete_academy_question_ajax'));

        // Check if table exists, create if not
        add_action('plugins_loaded', array($this, 'check_table_exists'));
    }

    /**
     * Check if the questions table exists, and create it if not
     */
    public function check_table_exists() {
        global $wpdb;

        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->questions_table}'") != $this->questions_table) {
            $this->create_questions_table();
        }
    }

    /**
     * Create the financial academy questions table
     */
    public function create_questions_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->questions_table} (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            question_text TEXT NOT NULL,
            category VARCHAR(50) DEFAULT 'general',
            active BOOLEAN NOT NULL DEFAULT 1,
            sort_order INT UNSIGNED DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default questions if table is empty
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->questions_table}");
        if ($count == 0) {
            $this->insert_default_questions();
        }
    }

    /**
     * Insert default financial academy questions
     */
    private function insert_default_questions() {
        global $wpdb;

        $default_questions = array(
            "Cos'è un portafoglio d'investimento diversificato?",
            "Come funzionano i fondi comuni di investimento?",
            "Quali sono i vantaggi e gli svantaggi dei fondi ETF?",
            "Cosa sono i titoli di Stato e come funzionano?",
            "Come si calcola il rendimento di un investimento?",
            "Quali sono i principali indici di borsa?",
            "Cosa significa investire in fondi ESG?",
            "Come posso iniziare a investire con un budget limitato?",
            "Quali sono le strategie di gestione del rischio finanziario?",
            "Qual è la differenza tra azioni e obbligazioni?",
            "Come valutare la solidità finanziaria di un'azienda?",
            "Quali sono i vantaggi fiscali degli investimenti a lungo termine?",
            "Come funziona il Piano Individuale di Risparmio (PIR)?",
            "Quali sono le migliori strategie per la pianificazione pensionistica?",
            "Come si legge un bilancio aziendale?",
            "Quali sono i rischi dell'investimento in criptovalute?",
            "Come funziona il mercato immobiliare come investimento?",
            "Cos'è il trading algoritmico?",
            "Quali sono i principi del value investing?",
            "Come proteggere il proprio patrimonio dall'inflazione?",
            "Quali strumenti finanziari sono più adatti per la pianificazione educativa?",
            "Come funziona la tassazione dei dividendi?",
            "Quali sono le migliori pratiche per la gestione del debito?",
            "Come si costruisce un fondo di emergenza?",
            "Quali sono i vantaggi e gli svantaggi dell'investimento immobiliare?",
            "Come funzionano le polizze assicurative unit-linked?",
            "Quali fattori considerare prima di investire in startup?",
            "Come diversificare il portafoglio in base al profilo di rischio?"
        );

        $sort_order = 1;
        foreach ($default_questions as $question) {
            $wpdb->insert(
                $this->questions_table,
                array(
                    'question_text' => $question,
                    'category' => 'general',
                    'active' => 1,
                    'sort_order' => $sort_order++
                ),
                array('%s', '%s', '%d', '%d')
            );
        }

        dv_debug_log('Inserted ' . count($default_questions) . ' default financial academy questions', 'academy');
    }

    /**
     * Get all active academy questions
     * 
     * @param string $category Optional category filter
     * @return array List of questions
     */
    public function get_questions($category = '') {
        global $wpdb;

        $where = "WHERE active = 1";
        if (!empty($category) && $category !== 'all') {
            $where .= $wpdb->prepare(" AND category = %s", $category);
        }

        $questions = $wpdb->get_results(
            "SELECT id, question_text, category 
             FROM {$this->questions_table} 
             {$where}
             ORDER BY sort_order ASC, id ASC",
            ARRAY_A
        );

        return $questions;
    }

    /**
     * AJAX handler to get academy questions
     */
    public function get_academy_questions_ajax() {
        // We allow this for all visitors, no nonce check needed
        $category = isset($_REQUEST['category']) ? sanitize_text_field($_REQUEST['category']) : 'all';
        
        $questions = $this->get_questions($category);
        
        if (empty($questions)) {
            wp_send_json_error(array('message' => __('Nessuna domanda finanziaria trovata.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array('questions' => $questions));
    }

    /**
     * Render admin page for managing questions
     */
    public function render_admin_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi sufficienti per accedere a questa pagina.', 'document-viewer-plugin'));
        }
        
    
        // Corretto il riferimento alla pagina nel filtro di categoria
        $current_category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
        
        $questions = $this->get_all_questions();
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Financial Academy Questions', 'document-viewer-plugin'); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e('Qui puoi gestire le domande finanziarie preconfigurare che appariranno nella chat del consulente finanziario.', 'document-viewer-plugin'); ?></p>
            </div>
            
            <!-- Add New Question Form -->
            <div class="card" style="max-width: 800px; margin-top: 20px; padding: 20px;">
                <h2><?php _e('Aggiungi Nuova Domanda', 'document-viewer-plugin'); ?></h2>
                <form id="add-academy-question-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="new-question-text"><?php _e('Testo Domanda', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <textarea id="new-question-text" name="question_text" rows="3" class="large-text" required></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="new-question-category"><?php _e('Categoria', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <select id="new-question-category" name="category">
                                    <option value="general"><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                                    <option value="investment"><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                                    <option value="retirement"><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                                    <option value="tax"><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                                </select>
                            </td>
                        </tr>
                    </table>
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php _e('Aggiungi Domanda', 'document-viewer-plugin'); ?></button>
                        <span class="spinner" style="float: none; margin-top: 0;"></span>
                    </p>
                </form>
            </div>
            
            <!-- Existing Questions Table -->
            <div style="max-width: 1200px; margin-top: 40px;">
                <h2><?php _e('Domande Esistenti', 'document-viewer-plugin'); ?></h2>
                
                <div class="tablenav top">
                    <div class="alignleft actions">
                        <select id="filter-question-category">
                            <option value="all"><?php _e('Tutte le categorie', 'document-viewer-plugin'); ?></option>
                            <option value="general"><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                            <option value="investment"><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                            <option value="retirement"><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                            <option value="tax"><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                        </select>
                        <button type="button" class="button" id="filter-questions-btn"><?php _e('Filtra', 'document-viewer-plugin'); ?></button>
                    </div>
                </div>
                
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('ID', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Domanda', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Categoria', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Attiva', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Ordine', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Azioni', 'document-viewer-plugin'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="academy-questions-list">
                        <?php foreach ($questions as $question): ?>
                            <tr data-id="<?php echo esc_attr($question['id']); ?>">
                                <td><?php echo esc_html($question['id']); ?></td>
                                <td class="question-text"><?php echo esc_html($question['question_text']); ?></td>
                                <td class="question-category"><?php echo esc_html($question['category']); ?></td>
                                <td>
                                    <input type="checkbox" class="question-active" <?php checked($question['active'], 1); ?>>
                                </td>
                                <td>
                                    <input type="number" class="small-text question-order" value="<?php echo esc_attr($question['sort_order']); ?>" min="0" max="999">
                                </td>
                                <td>
                                    <button class="button edit-question" data-id="<?php echo esc_attr($question['id']); ?>"><?php _e('Modifica', 'document-viewer-plugin'); ?></button>
                                    <button class="button button-link-delete delete-question" data-id="<?php echo esc_attr($question['id']); ?>"><?php _e('Elimina', 'document-viewer-plugin'); ?></button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Edit Question Modal -->
        <div id="edit-question-modal" style="display: none; position: fixed; z-index: 100001; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div style="background-color: #fefefe; margin: 10% auto; padding: 20px; border: 1px solid #888; width: 50%; box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);">
                <h2><?php _e('Modifica Domanda', 'document-viewer-plugin'); ?></h2>
                <form id="edit-question-form">
                    <input type="hidden" id="edit-question-id">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="edit-question-text"><?php _e('Testo Domanda', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <textarea id="edit-question-text" name="question_text" rows="3" class="large-text" required></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-category"><?php _e('Categoria', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <select id="edit-question-category" name="category">
                                    <option value="general"><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                                    <option value="investment"><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                                    <option value="retirement"><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                                    <option value="tax"><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-active"><?php _e('Attiva', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <input type="checkbox" id="edit-question-active" name="active" value="1">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-order"><?php _e('Ordine', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <input type="number" id="edit-question-order" name="sort_order" class="small-text" min="0" max="999">
                            </td>
                        </tr>
                    </table>
                    <div class="submit-container" style="text-align: right; margin-top: 20px;">
                        <button type="button" class="button" id="close-modal"><?php _e('Annulla', 'document-viewer-plugin'); ?></button>
                        <button type="submit" class="button button-primary"><?php _e('Salva Modifiche', 'document-viewer-plugin'); ?></button>
                        <span class="spinner" style="float: none;"></span>
                    </div>
                </form>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Add new question
                $('#add-academy-question-form').on('submit', function(e) {
                    e.preventDefault();
                    var $form = $(this);
                    var $spinner = $form.find('.spinner');
                    
                    $spinner.addClass('is-active');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'add_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            question_text: $('#new-question-text').val(),
                            category: $('#new-question-category').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert(response.data.message || 'Errore durante il salvataggio della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        },
                        complete: function() {
                            $spinner.removeClass('is-active');
                        }
                    });
                });
                
                // Edit question - open modal
                $('.edit-question').on('click', function() {
                    var $row = $(this).closest('tr');
                    var id = $row.data('id');
                    var text = $row.find('.question-text').text();
                    var category = $row.find('.question-category').text();
                    var active = $row.find('.question-active').is(':checked');
                    var order = $row.find('.question-order').val();
                    
                    $('#edit-question-id').val(id);
                    $('#edit-question-text').val(text);
                    $('#edit-question-category').val(category);
                    $('#edit-question-active').prop('checked', active);
                    $('#edit-question-order').val(order);
                    
                    $('#edit-question-modal').show();
                });
                
                // Close modal
                $('#close-modal').on('click', function() {
                    $('#edit-question-modal').hide();
                });
                
                $(window).on('click', function(e) {
                    if ($(e.target).is('#edit-question-modal')) {
                        $('#edit-question-modal').hide();
                    }
                });
                
                // Edit question - submit form
                $('#edit-question-form').on('submit', function(e) {
                    e.preventDefault();
                    var $form = $(this);
                    var $spinner = $form.find('.spinner');
                    
                    $spinner.addClass('is-active');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: $('#edit-question-id').val(),
                            question_text: $('#edit-question-text').val(),
                            category: $('#edit-question-category').val(),
                            active: $('#edit-question-active').is(':checked') ? 1 : 0,
                            sort_order: $('#edit-question-order').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#edit-question-modal').hide();
                                location.reload();
                            } else {
                                alert(response.data.message || 'Errore durante l\'aggiornamento della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        },
                        complete: function() {
                            $spinner.removeClass('is-active');
                        }
                    });
                });
                
                // Delete question
                $('.delete-question').on('click', function() {
                    if (!confirm('<?php _e('Sei sicuro di voler eliminare questa domanda?', 'document-viewer-plugin'); ?>')) {
                        return;
                    }
                    
                    var $button = $(this);
                    var id = $button.data('id');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id
                        },
                        success: function(response) {
                            if (response.success) {
                                $button.closest('tr').fadeOut(400, function() {
                                    $(this).remove();
                                });
                            } else {
                                alert(response.data.message || 'Errore durante l\'eliminazione della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        }
                    });
                });
                
                // Update active status
                $('.question-active').on('change', function() {
                    var $checkbox = $(this);
                    var id = $checkbox.closest('tr').data('id');
                    var active = $checkbox.is(':checked') ? 1 : 0;
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id,
                            active: active,
                            partial_update: true
                        },
                        error: function() {
                            // Revert checkbox state on error
                            $checkbox.prop('checked', !$checkbox.is(':checked'));
                            alert('Errore durante l\'aggiornamento dello stato.');
                        }
                    });
                });
                
                // Update sort order
                $('.question-order').on('change', function() {
                    var $input = $(this);
                    var id = $input.closest('tr').data('id');
                    var order = $input.val();
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id,
                            sort_order: order,
                            partial_update: true
                        },
                        error: function() {
                            alert('Errore durante l\'aggiornamento dell\'ordine.');
                        }
                    });
                });
                
                // Filter questions
                $('#filter-questions-btn').on('click', function() {
                    var category = $('#filter-question-category').val();
                    // Utilizziamo lo slug corretto che abbiamo impostato nel menu
                    location.href = 'admin.php?page=financial-academy-manager&category=' + category;
                });
            });
        </script>
        <?php
    }

    /**
     * Get all questions for admin
     */
    private function get_all_questions() {
        global $wpdb;

        // Check if we have a category filter
        $category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
        
        $where = "";
        if (!empty($category) && $category !== 'all') {
            $where = $wpdb->prepare("WHERE category = %s", $category);
        }

        $questions = $wpdb->get_results(
            "SELECT * FROM {$this->questions_table} 
             {$where}
             ORDER BY sort_order ASC, id ASC",
            ARRAY_A
        );

        return $questions;
    }

    /**
     * AJAX handler to add a new academy question
     */
    public function add_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $question_text = isset($_POST['question_text']) ? sanitize_textarea_field($_POST['question_text']) : '';
        $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : 'general';
        
        if (empty($question_text)) {
            wp_send_json_error(array('message' => __('Il testo della domanda è obbligatorio.', 'document-viewer-plugin')));
            return;
        }
        
        global $wpdb;
        
        // Get the highest sort order value
        $max_order = $wpdb->get_var("SELECT MAX(sort_order) FROM {$this->questions_table}");
        $next_order = ($max_order !== null) ? intval($max_order) + 10 : 10;
        
        $result = $wpdb->insert(
            $this->questions_table,
            array(
                'question_text' => $question_text,
                'category' => $category,
                'active' => 1,
                'sort_order' => $next_order
            ),
            array('%s', '%s', '%d', '%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante il salvataggio della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda aggiunta con successo.', 'document-viewer-plugin'),
            'id' => $wpdb->insert_id
        ));
    }

    /**
     * AJAX handler to update an academy question
     */
    public function update_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if (empty($id)) {
            wp_send_json_error(array('message' => __('ID domanda mancante.', 'document-viewer-plugin')));
            return;
        }
        
        // Build data array based on available fields
        $data = array();
        $formats = array();
        
        // Check if this is a partial update (just active status or order)
        $partial_update = isset($_POST['partial_update']) && $_POST['partial_update'];
        
        if (!$partial_update) {
            // Full update requires question text
            if (!isset($_POST['question_text']) || empty($_POST['question_text'])) {
                wp_send_json_error(array('message' => __('Il testo della domanda è obbligatorio.', 'document-viewer-plugin')));
                return;
            }
            
            $data['question_text'] = sanitize_textarea_field($_POST['question_text']);
            $formats[] = '%s';
            
            if (isset($_POST['category'])) {
                $data['category'] = sanitize_text_field($_POST['category']);
                $formats[] = '%s';
            }
        }
        
        // These fields can be updated in both full and partial updates
        if (isset($_POST['active'])) {
            $data['active'] = intval($_POST['active']);
            $formats[] = '%d';
        }
        
        if (isset($_POST['sort_order'])) {
            $data['sort_order'] = intval($_POST['sort_order']);
            $formats[] = '%d';
        }
        
        global $wpdb;
        
        $result = $wpdb->update(
            $this->questions_table,
            $data,
            array('id' => $id),
            $formats,
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante l\'aggiornamento della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda aggiornata con successo.', 'document-viewer-plugin')
        ));
    }

    /**
     * AJAX handler to delete an academy question
     */
    public function delete_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if (empty($id)) {
            wp_send_json_error(array('message' => __('ID domanda mancante.', 'document-viewer-plugin')));
            return;
        }
        
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->questions_table,
            array('id' => $id),
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante l\'eliminazione della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda eliminata con successo.', 'document-viewer-plugin')
        ));
    }
}

// Initialize the Financial Academy Manager
function financial_academy_manager() {
    static $instance;
    
    if (!$instance instanceof Financial_Academy_Manager) {
        $instance = new Financial_Academy_Manager();
    }
    
    return $instance;
}

// Initialize
financial_academy_manager();