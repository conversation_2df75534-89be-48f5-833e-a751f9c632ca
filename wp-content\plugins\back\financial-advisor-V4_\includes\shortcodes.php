<?php
/**
 * Shortcodes for Financial Advisor
 * 
 * Shortcodes per l'uso del plugin nelle pagine WordPress
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Shortcode per mostrare contenuto solo ai sottoscrittori (o tutti gli utenti loggati)
 * 
 * Esempio:
 * [fa_subscriber_content]Questo contenuto è visibile solo ai sottoscrittori[/fa_subscriber_content]
 * [fa_subscriber_content type="premium"]Contenuto per sottoscrittori premium[/fa_subscriber_content]
 * [fa_subscriber_content login_message="Effettua il login per vedere questo contenuto"]...[/fa_subscriber_content]
 * 
 * @param array $atts Attributi dello shortcode
 * @param string $content Contenuto tra i tag dello shortcode
 * @return string Contenuto o messaggio di accesso negato
 */
function fa_subscriber_content_shortcode($atts, $content = null) {
    $atts = shortcode_atts(array(
        'type' => '',  // tipo specifico di sottoscrizione richiesto, lasciare vuoto per qualsiasi sottoscrittore
        'login_message' => __('Questo contenuto è riservato agli utenti registrati.', 'document-viewer-plugin'),
    ), $atts, 'fa_subscriber_content');
    
    // Se non c'è nessun contenuto, ritorna vuoto
    if ($content === null) {
        return '';
    }
    
    // Se l'utente non è loggato, mostra il messaggio di login
    if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
        $login_url = get_option('login_page_url', home_url());
        $login_url = add_query_arg('redirect_to', urlencode($_SERVER['REQUEST_URI']), $login_url);
        
        return '<div class="fa-login-required">' . 
               '<p>' . esc_html($atts['login_message']) . '</p>' .
               '<p><a href="' . esc_url($login_url) . '" class="button">' . 
               __('Accedi', 'document-viewer-plugin') . '</a></p>' .
               '</div>';
    }
    
    // Se è richiesto un tipo specifico di sottoscrizione
    if (!empty($atts['type'])) {
        // Verifica se l'utente ha quel tipo di sottoscrizione
        if (!fa_user_has_subscription_type($atts['type'])) {
            // Se l'utente non ha la sottoscrizione richiesta
            return '<div class="fa-subscription-required">' .
                   '<p>' . __('Per visualizzare questo contenuto è necessario avere una sottoscrizione di tipo:', 'document-viewer-plugin') . 
                   ' <strong>' . esc_html($atts['type']) . '</strong></p>' .
                   '</div>';
        }
    } else {
        // Se non è richiesto un tipo specifico, verifica che sia almeno un sottoscrittore
        // o un utente WordPress con permessi di frontend
        if (!fa_user_can_access('frontend')) {
            return '<div class="fa-access-denied">' .
                   '<p>' . __('Non hai il permesso di accedere a questo contenuto.', 'document-viewer-plugin') . '</p>' .
                   '</div>';
        }
    }
    
    // A questo punto l'utente ha accesso, mostra il contenuto
    return do_shortcode($content);
}
add_shortcode('fa_subscriber_content', 'fa_subscriber_content_shortcode');

/**
 * Shortcode per mostrare le informazioni del sottoscrittore corrente
 * 
 * Esempio:
 * [fa_subscriber_info field="name"] - mostra il nome
 * [fa_subscriber_info field="surname"] - mostra il cognome
 * [fa_subscriber_info field="email"] - mostra l'email
 * [fa_subscriber_info field="type"] - mostra il tipo di sottoscrizione
 * [fa_subscriber_info] - mostra tutte le informazioni
 * 
 * @param array $atts Attributi dello shortcode
 * @return string Informazioni del sottoscrittore o messaggio
 */
function fa_subscriber_info_shortcode($atts) {
    $atts = shortcode_atts(array(
        'field' => '',  // nome, cognome, email, tipo, ecc.
    ), $atts, 'fa_subscriber_info');
    
    // Ottieni i dati del sottoscrittore
    $subscriber_data = fa_get_subscriber_data();
    
    // Se non è un sottoscrittore
    if (!$subscriber_data) {
        if (is_user_logged_in()) {
            return '<span class="fa-not-subscriber">' . __('Utente WordPress', 'document-viewer-plugin') . '</span>';
        } else {
            return '<span class="fa-not-logged-in">' . __('Utente non autenticato', 'document-viewer-plugin') . '</span>';
        }
    }
    
    // Se è stato specificato un campo specifico
    if (!empty($atts['field']) && isset($subscriber_data[$atts['field']])) {
        return '<span class="fa-subscriber-' . esc_attr($atts['field']) . '">' . 
                esc_html($subscriber_data[$atts['field']]) . '</span>';
    }
    
    // Se non è stato specificato un campo, mostra tutte le informazioni
    $output = '<div class="fa-subscriber-info">';
    $output .= '<p><strong>' . __('Nome:', 'document-viewer-plugin') . '</strong> ' . 
              esc_html($subscriber_data['name']) . '</p>';
    $output .= '<p><strong>' . __('Cognome:', 'document-viewer-plugin') . '</strong> ' . 
              esc_html($subscriber_data['surname']) . '</p>';
    $output .= '<p><strong>' . __('Email:', 'document-viewer-plugin') . '</strong> ' . 
              esc_html($subscriber_data['email']) . '</p>';
    $output .= '<p><strong>' . __('Tipo di sottoscrizione:', 'document-viewer-plugin') . '</strong> ' . 
              esc_html($subscriber_data['type']) . '</p>';
    $output .= '</div>';
    
    return $output;
}
add_shortcode('fa_subscriber_info', 'fa_subscriber_info_shortcode');
