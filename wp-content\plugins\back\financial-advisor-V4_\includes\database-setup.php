<?php
/**
 * Database setup file for Financial Advisor
 *
 * This file contains the necessary SQL scripts to create tables for the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create the wpcd_user_subsciption_stats table
 *
 * This function creates a new table to store analysis statistics for non-WordPress users
 */
function wpcd_create_user_subscription_stats_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'user_subscription_stats';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        ID bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        User_id bigint(20) UNSIGNED NOT NULL,
        Username varchar(191) NOT NULL,
        Title_analysis varchar(255) NOT NULL,
        Tokens_used bigint(20) UNSIGNED NOT NULL DEFAULT 0,
        Actual_Cost decimal(10,2) NOT NULL DEFAULT 0.00,
        Created_at datetime DEFAULT CURRENT_TIMESTAMP,
        <PERSON>IMARY KEY (ID)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Create the credit transactions table
 *
 * This function creates a table to store credit recharge transactions
 */
function wpcd_create_credit_transactions_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'credit_transactions';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        subscriber_id bigint(20) unsigned NOT NULL,
        amount decimal(10,2) NOT NULL,
        transaction_type varchar(50) NOT NULL DEFAULT 'recharge',
        payment_method varchar(50) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'completed',
        transaction_id varchar(100) DEFAULT NULL,
        notes text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY subscriber_id (subscriber_id),
        KEY transaction_type (transaction_type),
        KEY status (status)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Create the PayPal configuration table
 *
 * This function creates a table to store PayPal gateway settings
 */
function wpcd_create_paypal_config_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'paypal_config';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        client_id varchar(255) NOT NULL,
        client_secret varchar(255) NOT NULL,
        environment varchar(20) NOT NULL DEFAULT 'sandbox',
        webhook_id varchar(255) DEFAULT NULL,
        is_active tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY environment (environment),
        KEY is_active (is_active)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Create the Stripe configuration table
 *
 * This function creates a table to store Stripe gateway settings
 */
function wpcd_create_stripe_config_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'stripe_config';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        public_key varchar(255) NOT NULL,
        secret_key varchar(255) NOT NULL,
        environment varchar(20) NOT NULL DEFAULT 'test',
        webhook_endpoint_secret varchar(255) DEFAULT NULL,
        is_active tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY environment (environment),
        KEY is_active (is_active)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Initialize database tables
 *
 * This function should be called during plugin activation
 */
function wpcd_initialize_database_tables() {
    wpcd_create_user_subscription_stats_table();
    wpcd_create_credit_transactions_table();
    wpcd_create_paypal_config_table();
    wpcd_create_stripe_config_table();
}
