/**
 * Chat Model Widget - JavaScript functionality
 * 
 * Manages all chat interactions between users and AI models
 */
jQuery(document).ready(function ($) {
    // Store chat messages for the session
    let chatMessages = [];
    let isTyping = false;
    let typingSpeed = 40; // Characters per second for typing effect

    // Financial questions array - will be populated from server
    let financialQuestions = [];

    /**
     * Initialize chat widget functionality
     */
    function initChatWidget() {
        // Find all chat widgets in the page
        $('.chat-widget').each(function() {
            const $widget = $(this);
            
            // Get widget configuration from data attributes
            const apiEndpoint = $widget.data('api-endpoint');
            const apiModel = $widget.data('api-model');
            
            // We don't need to get the API key here, it will be handled securely by the server
            
            if (!apiEndpoint || !apiModel) {
                console.error('Chat widget missing configuration data');
                $widget.find('.chat-log').append('<div class="message error-message">Chat configuration incomplete</div>');
                return;
            }
            
            // Load financial questions from the database
            loadFinancialQuestions().then(function() {
                // Setup financial questions menu functionality
                setupFinancialQuestionsMenu($widget);
            });
            
            // Initialize the input handlers
            initChatInputHandlers($widget);
        });
    }
    
    /**
     * Load financial questions from the database
     * @returns {Promise} - Promise that resolves when questions are loaded
     */
    function loadFinancialQuestions() {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: documentViewerParams.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'get_academy_questions'
                },
                success: function(response) {
                    if (response.success && response.data && response.data.questions) {
                        // Transform the questions data to match the expected format
                        financialQuestions = response.data.questions.map(function(item) {
                            return item.question_text;
                        });
                        resolve();
                    } else {
                        console.error('Failed to load financial questions:', response);
                        
                        // Set some default questions if loading failed
                        financialQuestions = [
                            "Cos'è un portafoglio d'investimento diversificato?",
                            "Come funzionano i fondi comuni di investimento?",
                            "Come si calcola il rendimento di un investimento?"
                        ];
                        resolve();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading financial questions:', error);
                    
                    // Set some default questions if loading failed
                    financialQuestions = [
                        "Cos'è un portafoglio d'investimento diversificato?",
                        "Come funzionano i fondi comuni di investimento?",
                        "Come si calcola il rendimento di un investimento?"
                    ];
                    resolve();
                }
            });
        });
    }
    
    /**
     * Setup financial questions menu functionality
     * @param {jQuery} $widget - The chat widget element
     */
    function setupFinancialQuestionsMenu($widget) {
        // Add the financial questions menu to the widget, next to the academy icon
        const $academyIcon = $widget.find('.academy-icon');
        const $inputContainer = $widget.find('.input-container');
        
        // Create financial questions menu
        const $questionsMenu = $('<div class="financial-questions-menu"></div>');
        $questionsMenu.append('<h4>Domande Finanziarie</h4>');
        
        // Create questions list
        const $questionsList = $('<ul></ul>');
        
        // Add questions to the list
        financialQuestions.forEach(function(question) {
            const $question = $('<li></li>').text(question);
            
            // Handle click on question
            $question.on('click', function() {
                // Close the menu
                $questionsMenu.removeClass('active');
                
                // Set the question in the input field
                $widget.find('.chat-input').val(question);
                
                // Focus on the input field
                $widget.find('.chat-input').focus();
            });
            
            $questionsList.append($question);
        });
        
        // Add questions list to the menu
        $questionsMenu.append($questionsList);
        
        // Append menu to input container
        $inputContainer.append($questionsMenu);
        
        // Toggle questions menu when Academy button is clicked
        $academyIcon.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $questionsMenu.toggleClass('active');
        });
        
        // Close questions menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.academy-icon, .financial-questions-menu').length) {
                $questionsMenu.removeClass('active');
            }
        });
    }

    /**
     * Initialize input handlers for chat widget
     * @param {jQuery} $widget - The chat widget element
     */
    function initChatInputHandlers($widget) {
        const $input = $widget.find('.chat-input');
        const $sendButton = $widget.find('.send-btn');
        
        // Handle send button click
        $sendButton.on('click', function() {
            sendChatMessage($widget);
        });
        
        // Handle pressing Enter in the input field
        $input.on('keypress', function(e) {
            if (e.which === 13 && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage($widget);
            }
        });
    }

    /**
     * Send a chat message to the API
     * @param {jQuery} $widget - The chat widget element
     */
    function sendChatMessage($widget) {
        const $input = $widget.find('.chat-input');
        const message = $input.val().trim();
        
        if (!message || isTyping) {
            return;
        }
        
        // Clear input field
        $input.val('');
        
        // Add user message to the chat log
        addMessageToChat($widget, message, 'user');
        
        // Store message in chat history
        chatMessages.push({
            role: 'user',
            content: message
        });
        
        // Show typing indicator directly in the chat history
        addTypingIndicator($widget);
        
        // Send message to the API
        sendToChatAPI($widget, message);
    }
    
    /**
     * Add typing indicator to the chat history
     * @param {jQuery} $widget - The chat widget element
     */
    function addTypingIndicator($widget) {
        const $chatLog = $widget.find('.chat-log');
        
        // Create typing indicator element with AI avatar
        const $typingIndicator = $('<div>').addClass('message ai-message typing-indicator');
        
        // Add avatar for AI
        let avatarHtml = '<div class="message-avatar ai-avatar"></div>';
        
        // Set message content with dots animation
        $typingIndicator.html(avatarHtml + '<div class="message-content"><span>AI is thinking<span class="typing-dots">...</span></span></div>');
        
        // Add the typing indicator to chat log
        $chatLog.append($typingIndicator);
        
        // Scroll to bottom
        $chatLog.scrollTop($chatLog[0].scrollHeight);
        
        // Animate the dots
        animateTypingDots($typingIndicator.find('.typing-dots'));
    }
    
    /**
     * Remove typing indicator from chat history
     * @param {jQuery} $widget - The chat widget element
     */
    function removeTypingIndicator($widget) {
        $widget.find('.typing-indicator').remove();
    }
    
    /**
     * Send message to the chat API
     * @param {jQuery} $widget - The chat widget element
     * @param {string} message - The message to send
     */
    function sendToChatAPI($widget, message) {
        isTyping = true;
        
        // Get the apiEndpoint and apiModel from data attributes
        const apiEndpoint = $widget.data('api-endpoint');
        const apiModel = $widget.data('api-model');
        
        // Using the global documentViewerParams for AJAX URL and nonce
        // This ensures we're using the same settings as the document viewer
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chat_model',
                nonce: documentViewerParams.nonce,
                chat_message: message,
                chat_history: JSON.stringify(chatMessages),
                // Include the model from data attributes if available
                api_model: apiModel
            },
            success: function(response) {
                if (response.success) {
                    // Remove typing indicator
                    removeTypingIndicator($widget);
                    
                    // Create a placeholder for the AI response with the typing animation
                    const aiMessage = response.data.reply;
                    
                    // Create message element with empty content
                    const $chatLog = $widget.find('.chat-log');
                    const $message = $('<div>').addClass('message').addClass('ai-message');
                    
                    // Add avatar for AI
                    let avatarHtml = '<div class="message-avatar ai-avatar"></div>';
                    
                    // Add a container for the message with cursor
                    $message.html(avatarHtml + '<div class="message-content typing-container"><span class="typing-text"></span><span class="typing-cursor">|</span></div>');
                    
                    // Add to chat log
                    $chatLog.append($message);
                    
                    // Scroll to bottom
                    $chatLog.scrollTop($chatLog[0].scrollHeight);
                    
                    // Start typing effect with actual message
                    startTypingEffect($message.find('.typing-text'), aiMessage, function() {
                        // Remove cursor when typing is complete
                        $message.find('.typing-cursor').remove();
                        
                        // Store the AI response in chat history
                        chatMessages.push({
                            role: 'assistant',
                            content: aiMessage
                        });
                        
                        isTyping = false;
                    });
                } else {
                    // Remove typing indicator
                    removeTypingIndicator($widget);
                    
                    // Show error message
                    addMessageToChat($widget, 'Error: ' + (response.data.message || 'Unknown error'), 'error');
                    
                    isTyping = false;
                }
            },
            error: function(xhr, status, error) {
                // Remove typing indicator
                removeTypingIndicator($widget);
                
                // Show error message
                addMessageToChat($widget, 'Connection error: ' + error, 'error');
                
                isTyping = false;
            }
        });
    }
    
    /**
     * Start typing effect animation
     * @param {jQuery} $element - The element to add text to
     * @param {string} text - The text to type
     * @param {Function} callback - Callback function to call when typing is complete
     */
    function startTypingEffect($element, text, callback) {
        // Format the text with proper HTML
        const formattedText = formatChatMessage(text);
        
        // Parse the HTML to preserve formatting
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formattedText;
        const textContent = tempDiv.textContent || tempDiv.innerText || formattedText;
        
        let charIndex = 0;
        let htmlIndex = 0;
        let inTag = false;
        let currentHtml = '';
        
        // Function to type characters
        function typeCharacter() {
            if (htmlIndex >= formattedText.length) {
                // Typing is complete
                if (callback) callback();
                return;
            }
            
            // Process HTML tags - if we encounter a < character, we're entering a tag
            if (formattedText[htmlIndex] === '<') {
                inTag = true;
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Process the tag immediately (no delay)
                return;
            }
            
            // If we encounter a > character, we're exiting a tag
            if (formattedText[htmlIndex] === '>' && inTag) {
                inTag = false;
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Continue immediately after the tag
                return;
            }
            
            // If we're inside a tag, add the character to currentHtml without delay
            if (inTag) {
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Process the next character immediately
                return;
            }
            
            // For regular text (not in tag), add with delay for typing effect
            currentHtml += formattedText[htmlIndex];
            $element.html(currentHtml);
            
            htmlIndex++;
            charIndex++;
            
            // Random typing speed variation for natural effect
            const randomDelay = Math.floor(Math.random() * 10) + 10;
            const delay = 1000 / (typingSpeed + randomDelay);
            
            // Type next character with a bit of randomness in timing
            setTimeout(typeCharacter, delay);
            
            // Ensure scrolling to bottom as text grows
            const $chatLog = $element.closest('.chat-log');
            $chatLog.scrollTop($chatLog[0].scrollHeight);
        }
        
        // Start the typing effect
        typeCharacter();
    }
    
    /**
     * Add a message to the chat log
     * @param {jQuery} $widget - The chat widget element
     * @param {string} message - The message text
     * @param {string} role - The message role (user, ai, system, error)
     */
    function addMessageToChat($widget, message, role) {
        const $chatLog = $widget.find('.chat-log');
        
        // Create message element
        const $message = $('<div>').addClass('message').addClass(role + '-message');
        
        // Add avatar or icon based on role
        let avatarHtml = '';
        if (role === 'user') {
            avatarHtml = '<div class="message-avatar user-avatar"></div>';
        } else if (role === 'ai') {
            avatarHtml = '<div class="message-avatar ai-avatar"></div>';
        }
        
        // If it's not an AI message (or if it's a system/error message), add it immediately
        // AI messages are handled differently with the typing effect
        if (role !== 'ai') {
            $message.html(avatarHtml + '<div class="message-content">' + formatChatMessage(message) + '</div>');
            
            // Add to chat log
            $chatLog.append($message);
            
            // Scroll to bottom
            $chatLog.scrollTop($chatLog[0].scrollHeight);
        }
    }
    
    /**
     * Format chat message with proper HTML
     * @param {string} message - The message to format
     * @returns {string} - Formatted HTML message
     */
    function formatChatMessage(message) {
        // Convert URLs to links
        message = message.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
        
        // Remove double ## symbols (heading markers)
        message = message.replace(/##\s*/g, '');
        
        // Remove single # symbols (heading markers)
        message = message.replace(/#\s*/g, '');
        
        // Remove double ** symbols and convert surrounding text to bold
        message = message.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        
        // Remove single * symbols and convert surrounding text to bold
        message = message.replace(/\*([^*]+)\*/g, '<strong>$1</strong>');
        
        // Convert hyphens in list items to checkmarks (✓)
        message = message.replace(/^- (.+)$/gm, '✓ $1');
        message = message.replace(/<br>- (.+?)(?=<br>|$)/g, '<br>✓ $1');
        
        // Convert line breaks to <br>
        message = message.replace(/\n/g, '<br>');
        
        return message;
    }
    
    /**
     * Animate typing dots
     * @param {jQuery} $element - The element containing typing dots
     */
    function animateTypingDots($element) {
        let count = 0;
        const interval = setInterval(function() {
            if (!$element.is(':visible')) {
                clearInterval(interval);
                return;
            }
            
            count = (count + 1) % 4;
            const dots = '.'.repeat(count);
            $element.text(dots);
        }, 300);
    }

    // Initialize the chat widget functionality
    initChatWidget();
    
    // Add CSS styles for typing effect
    const typingStyles = `
        <style>
            .typing-container {
                position: relative;
                display: inline-block;
            }
            
            .typing-cursor {
                display: inline-block;
                width: 2px;
                margin-left: 2px;
                background-color: #333;
                animation: blink 0.7s infinite;
            }
            
            .typing-indicator {
                opacity: 0.8;
            }
            
            .typing-indicator .message-content {
                display: flex;
                align-items: center;
                font-style: italic;
            }
            
            .typing-dots {
                display: inline-block;
                min-width: 12px;
                text-align: left;
            }
            
            @keyframes blink {
                0%, 100% { opacity: 1; }
                50% { opacity: 0; }
            }
        </style>
    `;
    
    // Add styling if it's not already there
    if (!$('head').find('style:contains(.typing-container)').length) {
        $('head').append(typingStyles);
    }
});