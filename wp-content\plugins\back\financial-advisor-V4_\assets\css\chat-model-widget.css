/**
 * Chat Model Widget - ChatGPT-style Theme
 */

.chat-widget {
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 15px 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    overflow: hidden;
}

.chat-widget h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
}

.chat-widget h3::before {
    content: "💬";
    margin-right: 8px;
    font-size: 18px;
}

/* Chat form */
.chat-form {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Chat log area */
.chat-log {
    background-color: #ffffff;
    height: 400px;
    margin: 0;
    overflow-y: auto;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Chat input container - the parent that holds all input elements */
.input-container {
    position: relative;
    margin: 0;
    padding: 15px;
    background-color: #f7f7f8;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Academy icon - now positioned to the left of the input */
.academy-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #0a66c2;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    transition: background-color 0.2s ease;
    margin-right: 10px;
    flex-shrink: 0;
}

.academy-icon:hover {
    background-color: #004182;
}

/* Financial questions dropdown */
.financial-questions-menu {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 15px;
    width: 280px;
    max-height: 320px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 100;
    padding: 6px 0;
    display: none;
}

.financial-questions-menu.active {
    display: block;
}

.financial-questions-menu h4 {
    margin: 0;
    padding: 8px 12px;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
}

.financial-questions-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.financial-questions-menu li {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.financial-questions-menu li:hover {
    background-color: #f0f7ff;
    color: #0a66c2;
}

/* Input wrapper - container for just the input field */
.input-wrapper {
    flex: 0 1 70%;
    position: relative;
    max-width: 70%;
}

/* Chat input field - now with more rounded corners and smaller height */
.chat-input {
    border: 1px solid #e5e5e5;
    border-radius: 25px;
    font-size: 14px;
    padding: 8px 16px;
    width: 100%;
    height: 44px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    outline: none;
    background-color: #ffffff;
}

.chat-input:focus {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
    outline: none;
}

/* Send button now positioned outside the input field */
.send-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #10a37f;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
    position: relative;
}

.send-btn:hover {
    background-color: #0e8f6f;
}

.send-btn:active {
    background-color: #0c7d60;
}

/* Send icon */
.send-btn::before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3E%3Cpath d='M15.964.686a.5.5 0 0 0-.65-.65L.767 5.855H.766l-.452.18a.5.5 0 0 0-.082.887l.41.26.001.002 4.995 3.178 3.178 4.995.002.002.26.41a.5.5 0 0 0 .886-.083l6-15zm-1.833 1.89L6.637 10.07l-.215-.338a.5.5 0 0 0-.154-.154l-.338-.215 7.494-7.494 1.178-.471-.47 1.178z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    margin: auto;
}

/* Hide the original chat controls section */
.chat-controls {
    display: none;
}

/* Message styling */
.message {
    border-radius: 0;
    clear: both;
    margin: 0;
    padding: 20px;
    position: relative;
    display: flex;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
}

.user-message {
    background-color: #f7f7f8;
}

.ai-message {
    background-color: #ffffff;
}

.system-message, .error-message {
    background-color: #fff8e1;
    color: #8a6d3b;
    padding: 10px 20px;
    text-align: center;
    font-size: 13px;
    border-top: 1px solid rgba(0,0,0,0.05);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.error-message {
    background-color: #fdecea;
    color: #a94442;
}

/* Avatar styling */
.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    background-size: cover;
    margin-right: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.user-avatar {
    background-color: #10a37f;
    color: white;
}

.user-avatar::after {
    content: "U";
    font-weight: bold;
}

.ai-avatar {
    background-color: #19c37d;
    color: white;
    position: relative;
}

.ai-avatar::after {
    content: "AI";
    font-size: 12px;
    font-weight: 700;
}

.message-content {
    flex-grow: 1;
    line-height: 1.6;
    overflow-wrap: break-word;
    font-size: 15px;
}

/* Typing indicator inside the chat */
.typing-indicator {
    opacity: 0.7;
}

.typing-indicator .message-content {
    display: flex;
    align-items: center;
    font-style: italic;
    color: #666;
}

.typing-dots {
    display: inline-block;
    min-width: 12px;
    text-align: left;
    font-weight: bold;
}

/* API configuration notice */
.notice.notice-warning {
    background-color: #fff8e1;
    border-left: 4px solid #ffb900;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin: 5px 0 15px;
    padding: 12px;
}

/* Responsive design */
@media (max-width: 600px) {
    .chat-log {
        height: 320px;
    }
    
    .message {
        padding: 15px;
    }
    
    .message-avatar {
        width: 24px;
        height: 24px;
        margin-right: 10px;
    }
    
    .ai-avatar::after {
        font-size: 10px;
    }
    
    .message-content {
        font-size: 14px;
    }
}