Rendering
==========
**_From version 1.2_**

To render an AST generated by <PERSON>east you need to create an instance of the **Renderer** class and associate a **Formatter** to it:
```php
$source = "var a;";
//Generate the AST
$ast = Peast\Peast::latest($source, $options)->parse();
//Create the renderer
$renderer = new Peast\Renderer;
//Associate the formatter
$renderer->setFormatter(new Peast\Formatter\PrettyPrint);
//Render the AST
echo $renderer->render($ast); //"var a;"
```

Formatters
-------------
A Formatter specifies how the Renderer must format the nodes to produce the output.
Peast implements 3 formatters: **PrettyPrint**, **Compact** and **Expanded**.

For example using this source code:
```js
if (fn(param)) alert("Ok");
else alert("Fail");
```

##### PrettyPrint
Produces a well formatted version of the code.
```js
if (fn(param)) {
    alert("Ok");
} else {
    alert("Fail");
}
```

##### Compact
Produces a compact version of the code by removing whitespaces and optional brackets.
```js
if (fn(param))alert("Ok");else alert("Fail");
```

##### Expanded
An expanded version of PrettyPrint.
```js
if ( fn( param ) )
{
    alert( "Ok" );
} else
{
    alert( "Fail" );
}
```

Custom Formatter
-------------
Peast allows you to create your own formatter.
You can do it by creating a class that extends `Peast\Formatter\Base` class and overwriting its protected properties:

```php
class MyFormatter extends Peast\Formatter\Base {
    //Use Windows style line endings
    protected $newLine = "\r\n";
}
$renderer = new Peast\Renderer;
$renderer->setFormatter(new MyFormatter);
echo $renderer->render($ast);
```

Available properties are:
* `$newLine`: line separator string (default: `"\n"`)
* `$indentation`: indentation string (default: `"\t"`)
* `$newLineBeforeCurlyBracket`: if true, open curly brackets of code blocks will be put on a new line (default: `false`)
* `$alwaysWrapBlocks`: if true, curly brackets around code blocks will always be inserted, also when they are optional (default: `true`)
* `$spacesAroundOperators`: if true, a space will be inserted before and after operators (default: `true`)
* `$spacesInsideRoundBrackets`: if true, content inside round brackets will be surrounded by spaces (default: `false`)

Shortcut method
-------------
Every syntax node has its own `render` method that you can use as a shortcut.
For example:

```php
$ast = Peast\Peast::latest($source, $options)->parse();
$ast->render(new Peast\Formatter\PrettyPrint);
//Equivalent to
$ast = Peast\Peast::latest($source, $options)->parse();
$renderer = new Peast\Renderer;
$renderer->setFormatter(new Peast\Formatter\PrettyPrint);
$renderer->render($ast);
```

Comments rendering
-------------
**_From version 1.14_**
Comments can be rendered by passing `true` to the formatter constructor:
```php
$ast = Peast\Peast::latest($source, array("comments" => true))->parse();
$ast->render(new Peast\Formatter\PrettyPrint(true));
```
Note that comments can be rendered only when parser is enabled to collect them, to do this you must set the `comments` option to `true`.
Also note that only PrettyPrint and Expanded formatters allow comments rendering, while Compact does not allow it by default.
