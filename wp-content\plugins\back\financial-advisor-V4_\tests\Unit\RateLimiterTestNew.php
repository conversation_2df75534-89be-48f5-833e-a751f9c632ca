<?php

namespace Tests\Unit;

use Tests\BaseTestCase;

/**
 * Unit tests for Office_Addin_Rate_Limiter class
 */
class RateLimiterTestNew extends BaseTestCase
{
    private $rate_limiter;    protected function setUp(): void
    {
        parent::setUp();
        
        // Include the rate limiter class
        require_once __DIR__ . '/../../includes/class-office-addin-rate-limiter.php';
        
        $this->rate_limiter = new \Office_Addin_Rate_Limiter();
    }

    /**
     * Test constructor initializes default limits
     */
    public function testConstructorInitializesDefaultLimits(): void
    {
        $limits = $this->getProperty($this->rate_limiter, 'default_limits');
        
        $this->assertIsArray($limits);
        $this->assertArrayHasKey('analyze_excel_data', $limits);
        $this->assertArrayHasKey('get_settings', $limits);
        $this->assertArrayHasKey('get_queries', $limits);
        $this->assertArrayHasKey('test_connection', $limits);
        
        // Check default values
        $this->assertEquals(30, $limits['analyze_excel_data']);
        $this->assertEquals(60, $limits['get_settings']);
        $this->assertEquals(60, $limits['get_queries']);
        $this->assertEquals(10, $limits['test_connection']);
    }

    /**
     * Test is_request_allowed returns true for new client
     */
    public function testIsRequestAllowedReturnsTrueForNewClient(): void
    {
        // Mock cache functions to return false (no previous requests)
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals(29, $result['remaining']); // 30 - 1
        $this->assertArrayHasKey('reset_time', $result);
        $this->assertEquals(30, $result['limit']);
    }

    /**
     * Test is_request_allowed returns false when limit exceeded
     */
    public function testIsRequestAllowedReturnsFalseWhenLimitExceeded(): void
    {
        // Mock cache to return count that exceeds limit
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(31); // Exceeds default limit of 30
        
        $result = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals(0, $result['remaining']);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals(30, $result['limit']);
    }

    /**
     * Test is_request_allowed returns true after window expires
     */
    public function testIsRequestAllowedReturnsTrueAfterWindowExpires(): void
    {
        // Mock cache to return false (cache expired)
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals(29, $result['remaining']);
    }

    /**
     * Test get_rate_limit_status returns correct data
     */
    public function testGetRateLimitStatusReturnsCorrectData(): void
    {
        // Mock cache data
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(15);
        
        $status = $this->rate_limiter->get_rate_limit_status('analyze_excel_data');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('limit', $status);
        $this->assertArrayHasKey('used', $status);
        $this->assertArrayHasKey('remaining', $status);
        $this->assertArrayHasKey('reset_time', $status);
        $this->assertArrayHasKey('window_seconds', $status);
        
        $this->assertEquals(30, $status['limit']);
        $this->assertEquals(15, $status['used']);
        $this->assertEquals(15, $status['remaining']); // 30 - 15
    }

    /**
     * Test get_client_identifier uses user ID when available
     */
    public function testGetClientIdentifierUsesUserIdWhenAvailable(): void
    {
        \Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(true);
        \Brain\Monkey\Functions\when('get_current_user_id')->justReturn(123);
        
        $identifier = $this->callMethod($this->rate_limiter, 'get_client_identifier');
        
        $this->assertEquals('user_123', $identifier);
    }

    /**
     * Test get_client_identifier uses IP when no user
     */
    public function testGetClientIdentifierUsesIpWhenNoUser(): void
    {
        \Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(false);
        $_SERVER['REMOTE_ADDR'] = '*************';
        
        $identifier = $this->callMethod($this->rate_limiter, 'get_client_identifier');
        
        $this->assertEquals('ip_*************', $identifier);
    }

    /**
     * Test get_client_identifier uses forwarded IP
     */
    public function testGetClientIdentifierUsesForwardedIp(): void
    {
        \Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(false);
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '***********, ************';
        unset($_SERVER['REMOTE_ADDR']);
        
        $identifier = $this->callMethod($this->rate_limiter, 'get_client_identifier');
        
        // Should use first IP from X-Forwarded-For header
        $this->assertEquals('ip_***********', $identifier);
    }

    /**
     * Test reset_rate_limit removes cache entry
     */
    public function testResetRateLimitRemovesCacheEntry(): void
    {
        \Brain\Monkey\Functions\when('wp_cache_delete')->justReturn(true);
        
        $result = $this->rate_limiter->reset_rate_limit('analyze_excel_data', 'test_identifier');
        
        $this->assertTrue($result);
    }

    /**
     * Test update_rate_limits updates configuration
     */
    public function testUpdateRateLimitsUpdatesConfiguration(): void
    {
        \Brain\Monkey\Functions\when('update_option')->justReturn(true);
        
        $new_limits = [
            'analyze_excel_data' => 50,
            'get_settings' => 100
        ];
        
        $result = $this->rate_limiter->update_rate_limits($new_limits);
        
        $this->assertTrue($result);
    }

    /**
     * Test custom limits can be retrieved from options
     */
    public function testCustomLimitsFromOptions(): void
    {
        $custom_limits = ['analyze_excel_data' => 50];
        \Brain\Monkey\Functions\when('get_option')
            ->with('office_addin_rate_limits', [])
            ->justReturn($custom_limits);
        
        // Create new instance to test option loading
        $rate_limiter = new \Office_Addin_Rate_Limiter();
        
        // Test with mocked cache
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertEquals(50, $result['limit']); // Should use custom limit
    }

    /**
     * Test clear_all_rate_limits returns success
     */
    public function testClearAllRateLimitsReturnsSuccess(): void
    {
        $result = $this->rate_limiter->clear_all_rate_limits();
        
        $this->assertTrue($result);
    }

    /**
     * Test with different action types
     */
    public function testDifferentActionTypes(): void
    {
        $actions = ['analyze_excel_data', 'get_settings', 'get_queries', 'test_connection'];
        
        foreach ($actions as $action) {
            \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
            \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
            
            $result = $this->rate_limiter->is_request_allowed($action);
            
            $this->assertTrue($result['allowed'], "Action $action should be allowed for new client");
            $this->assertIsInt($result['limit'], "Action $action should have a numeric limit");
            $this->assertGreaterThan(0, $result['limit'], "Action $action should have a positive limit");
        }
    }

    /**
     * Test cache key generation is consistent
     */
    public function testCacheKeyGenerationIsConsistent(): void
    {
        $key1 = $this->callMethod($this->rate_limiter, 'generate_cache_key', ['test_action', 'test_id']);
        $key2 = $this->callMethod($this->rate_limiter, 'generate_cache_key', ['test_action', 'test_id']);
        
        $this->assertEquals($key1, $key2, 'Cache keys should be consistent for same parameters');
        $this->assertIsString($key1, 'Cache key should be a string');
        $this->assertNotEmpty($key1, 'Cache key should not be empty');
    }

    /**
     * Test rate limit increment functionality
     */
    public function testRateLimitIncrement(): void
    {
        // First request - no cache
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        \Brain\Monkey\Functions\when('wp_cache_incr')->justReturn(2);
        
        $result1 = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        $this->assertTrue($result1['allowed']);
        $this->assertEquals(29, $result1['remaining']);
        
        // Second request - cache has 1
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(1);
        \Brain\Monkey\Functions\when('wp_cache_incr')->justReturn(2);
        
        $result2 = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        $this->assertTrue($result2['allowed']);
        $this->assertEquals(28, $result2['remaining']); // 30 - (1 + 1)
    }

    protected function tearDown(): void
    {
        // Clean up server variables
        unset($_SERVER['REMOTE_ADDR']);
        unset($_SERVER['HTTP_X_FORWARDED_FOR']);
        unset($_SERVER['HTTP_X_REAL_IP']);
        
        parent::tearDown();
    }
}
