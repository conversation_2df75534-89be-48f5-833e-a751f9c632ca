<?xml version="1.0"?>
<ruleset name="BladeOne Standards">

    <description>PHPCS standards for EFTEC/BladeOne</description>

    <exclude-pattern>*/docs/*</exclude-pattern>
    <exclude-pattern>*/examples/*</exclude-pattern>
    <exclude-pattern>*/tests/*</exclude-pattern>
    <exclude-pattern>*/vendor/*</exclude-pattern>

    <arg name="colors"/>

    <rule ref="PSR2"/>

    <rule ref="Generic.Files.LineLength.TooLong">
        <severity>0</severity>
    </rule>

    <!-- Exclude Camel casing for _e,_ef,_n -->
    <rule ref="PSR1.Methods.CamelCapsMethodName">
        <exclude-pattern>/lib/BladeOneLang\.php</exclude-pattern>
    </rule>

</ruleset>