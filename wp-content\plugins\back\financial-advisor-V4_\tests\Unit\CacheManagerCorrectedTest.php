<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Unit tests for Office_Addin_Cache_Manager class
 * Converted to use simplified testing infrastructure without Brain Monkey
 */
class CacheManagerCorrectedTest extends TestCase
{
    private $cache_manager;protected function setUp(): void
    {
        parent::setUp();
        
        // Clear cache data before each test
        \TestCache::clear();
        
        // Include the cache manager class
        require_once __DIR__ . '/../../includes/class-office-addin-cache-manager.php';
        
        $this->cache_manager = new \Office_Addin_Cache_Manager();
    }

    protected function tearDown(): void
    {
        // Clear cache data after each test
        \TestCache::clear();
        parent::tearDown();
    }    public function testSetAndGetCache(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $type = 'settings';
        
        // Set cache value
        $set_result = $this->cache_manager->set($key, $value, $type);
        $this->assertTrue($set_result);
        
        // Get cache value - should return unwrapped value
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertEquals($value, $get_result);
    }

    public function testGetReturnsDefaultWhenCacheMiss(): void
    {
        $key = 'nonexistent_key';
        $type = 'settings';
        
        // Test cache miss returns false
        $result = $this->cache_manager->get($key, $type);
        $this->assertFalse($result);
    }    public function testDeleteRemovesFromCache(): void
    {
        $key = 'test_key_delete';
        $value = 'test_value';
        $type = 'settings';
        
        // Set then delete
        $this->cache_manager->set($key, $value, $type);
        $delete_result = $this->cache_manager->delete($key);
        $this->assertTrue($delete_result);
        
        // Verify it's gone
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertFalse($get_result);
    }

    public function testClearAllClearsAllCache(): void
    {
        $key1 = 'test_key_1';
        $key2 = 'test_key_2';
        $value1 = 'test_value_1';
        $value2 = 'test_value_2';
        $type = 'settings';
        
        // Set multiple values
        $this->cache_manager->set($key1, $value1, $type);
        $this->cache_manager->set($key2, $value2, $type);
        
        // Clear all cache
        $clear_result = $this->cache_manager->clear_all();
        $this->assertTrue($clear_result);
        
        // Verify all values are gone (cache version invalidation)
        $result1 = $this->cache_manager->get($key1, $type);
        $result2 = $this->cache_manager->get($key2, $type);
        $this->assertFalse($result1);
        $this->assertFalse($result2);
    }    public function testSetWithExpiration(): void
    {
        $key = 'test_expiry_key';
        $value = 'test_value';
        $type = 'settings';
        $expiration = 3600; // 1 hour
        
        $result = $this->cache_manager->set($key, $value, $type, $expiration);
        $this->assertTrue($result);
        
        // Should still be available immediately
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertEquals($value, $get_result);
    }    public function testSetComplexData(): void
    {
        $key = 'complex_data';
        $value = [
            'string' => 'test',
            'number' => 123,
            'array' => [1, 2, 3],
            'object' => (object) ['prop' => 'value']
        ];
        $type = 'settings';
        
        $set_result = $this->cache_manager->set($key, $value, $type);
        $this->assertTrue($set_result);
        
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertEquals($value, $get_result);
    }public function testGetWithDifferentTypes(): void
    {
        $key = 'same_key';
        $value1 = 'value_type1';
        $value2 = 'value_type2';
        $type1 = 'settings';
        $type2 = 'queries';
        
        // Set same key with different types
        $this->cache_manager->set($key, $value1, $type1);
        $this->cache_manager->set($key, $value2, $type2);
        
        // Note: Cache manager uses global versioning, so same key will overwrite
        // This tests that the types don't interfere with basic functionality
        $result1 = $this->cache_manager->get($key, $type1);
        $result2 = $this->cache_manager->get($key, $type2);
        
        // Both should return the last set value since they use the same cache key
        $this->assertNotFalse($result1);
        $this->assertNotFalse($result2);
    }

    public function testDeleteNonExistentKey(): void
    {
        $key = 'nonexistent_key';
        
        // Deleting non-existent key should still return true (WP cache behavior)
        $result = $this->cache_manager->delete($key);
        $this->assertTrue($result);
    }    public function testSetEmptyValue(): void
    {
        $key = 'empty_key';
        $value = '';
        $type = 'settings';
        
        $set_result = $this->cache_manager->set($key, $value, $type);
        $this->assertTrue($set_result);
        
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertEquals($value, $get_result);
    }    public function testSetNullValue(): void
    {
        $key = 'null_key';
        $value = null;
        $type = 'settings';
        
        $set_result = $this->cache_manager->set($key, $value, $type);
        $this->assertTrue($set_result);
        
        $get_result = $this->cache_manager->get($key, $type);
        $this->assertEquals($value, $get_result);
    }    public function testGetReturnsExactDataTypes(): void
    {
        $test_cases = [
            'string' => 'test_string',
            'integer' => 42,
            'float' => 3.14,
            'boolean_true' => true,
            'boolean_false' => false,
            'array' => [1, 2, 3],
            'null' => null
        ];
        
        $type = 'datatypes';
        
        foreach ($test_cases as $key => $value) {
            $this->cache_manager->set($key, $value, $type);
            $result = $this->cache_manager->get($key, $type);
            $this->assertSame($value, $result, "Failed for key: $key");
        }
    }public function testCacheKeyGeneration(): void
    {
        // Use reflection to test private methods if available
        $reflection = new \ReflectionClass($this->cache_manager);
        
        if ($reflection->hasMethod('generate_cache_key')) {
            $method = $reflection->getMethod('generate_cache_key');
            $method->setAccessible(true);
            
            $key1 = $method->invoke($this->cache_manager, 'test');
            $key2 = $method->invoke($this->cache_manager, 'test');
            
            // Same key should generate same cache key
            $this->assertEquals($key1, $key2);
            
            // Different keys should be different
            $key3 = $method->invoke($this->cache_manager, 'different');
            $this->assertNotEquals($key1, $key3);
        } else {
            // If method doesn't exist, test behavior through public interface
            $this->cache_manager->set('test', 'value1', 'settings');
            $this->cache_manager->set('different', 'value2', 'settings');
              $result1 = $this->cache_manager->get('test', 'settings');
            $result2 = $this->cache_manager->get('different', 'settings');
            
            $this->assertEquals('value1', $result1);
            $this->assertEquals('value2', $result2);
        }
    }

    public function testCacheStatistics(): void
    {
        // Test cache hit/miss statistics if the method exists
        $reflection = new \ReflectionClass($this->cache_manager);
        
        if ($reflection->hasMethod('get_stats')) {
            $key = 'stats_test';
            $value = 'test_value';
            $group = 'settings';
            
            // Initial stats
            $initial_stats = $this->cache_manager->get_stats();
            
            // Set and get a value
            $this->cache_manager->set($key, $value, $group);
            $this->cache_manager->get($key, $group);
            
            // Get updated stats
            $updated_stats = $this->cache_manager->get_stats();
            
            $this->assertIsArray($initial_stats);
            $this->assertIsArray($updated_stats);
        } else {
            // Skip if stats method doesn't exist
            $this->markTestSkipped('Cache statistics method not available');
        }
    }

    public function testMemoryUsage(): void
    {
        // Test memory usage tracking if available
        $large_data = str_repeat('x', 10000); // 10KB of data
        $key = 'memory_test';
        $group = 'settings';
        
        $memory_before = memory_get_usage();
        $this->cache_manager->set($key, $large_data, $group);
        $retrieved = $this->cache_manager->get($key, $group);
        $memory_after = memory_get_usage();
        
        $this->assertEquals($large_data, $retrieved);
        // Memory usage should have increased (though this is a rough test)
        $this->assertGreaterThanOrEqual($memory_before, $memory_after);
    }

    public function testCachePerformance(): void
    {
        // Test cache performance with multiple operations
        $start_time = microtime(true);
        
        // Perform multiple cache operations
        for ($i = 0; $i < 100; $i++) {
            $key = "perf_test_$i";
            $value = "value_$i";
            $this->cache_manager->set($key, $value, 'performance');
            $result = $this->cache_manager->get($key, 'performance');
            $this->assertEquals($value, $result);
        }
        
        $end_time = microtime(true);
        $duration = $end_time - $start_time;
        
        // Should complete 100 operations in reasonable time (less than 1 second)
        $this->assertLessThan(1.0, $duration, 'Cache operations took too long');
    }
}
