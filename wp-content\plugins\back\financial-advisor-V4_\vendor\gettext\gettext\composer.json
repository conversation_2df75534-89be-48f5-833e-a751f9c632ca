{"name": "gettext/gettext", "type": "library", "description": "PHP gettext manager", "keywords": ["js", "gettext", "i18n", "translation", "po", "mo"], "homepage": "https://github.com/oscarotero/Gettext", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Gettext/issues"}, "require": {"php": ">=5.4.0", "gettext/languages": "^2.3"}, "require-dev": {"illuminate/view": "^5.0.x-dev", "twig/twig": "^1.31|^2.0", "twig/extensions": "*", "symfony/yaml": "~2", "phpunit/phpunit": "^4.8|^5.7|^6.5", "squizlabs/php_codesniffer": "^3.0"}, "suggest": {"illuminate/view": "Is necessary if you want to use the Blade extractor", "twig/twig": "Is necessary if you want to use the Twig extractor", "twig/extensions": "Is necessary if you want to use the Twig extractor", "symfony/yaml": "Is necessary if you want to use the Yaml extractor/generator"}, "autoload": {"psr-4": {"Gettext\\": "src"}}, "autoload-dev": {"psr-4": {"Gettext\\Tests\\": "tests"}}, "scripts": {"test": ["phpunit", "phpcs"]}, "config": {"allow-plugins": {"kylekatarnls/update-helper": false}}}