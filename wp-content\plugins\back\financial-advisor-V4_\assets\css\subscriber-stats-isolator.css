/*
 * SUBSCRIBER STATS ISOLATOR CSS
 * ---------------------------
 * Questo file contiene regole CSS per isolare completamente gli stili della sezione stats
 * del subscriber management widget dal document viewer widget.
 *
 * Il file viene caricato dopo tutti gli altri CSS per garantire la massima specificità.
 */

/* ===================================================================
 * RESET GRID LAYOUT
 * Ripristina forzatamente il layout grid per il subscriber stats
 * ===================================================================
 */
body .subscriber-management-widget-container .stats-grid,
html body .subscriber-management-widget-container .stats-grid,
.subscriber-management-widget-container .stats-grid,
.subscriber-management-widget-container .stats-grid[class],
[id="subscriber-stats-grid"],
div[id="subscriber-stats-grid"],
.subscriber-management-widget-container [id="subscriber-stats-grid"],
.subscriber-management-widget-container div[id="subscriber-stats-grid"] {
    /* GRID LAYOUT FORZATO - sovrascrive qualsiasi altro stile */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;

    /* RESET COMPLETO proprietà flex che potrebbero interferire */
    flex: initial !important;
    flex-direction: initial !important;
    flex-wrap: initial !important;
    flex-flow: initial !important;
    flex-grow: initial !important;
    flex-shrink: initial !important;
    flex-basis: initial !important;

    /* RESET layout singole card */
    align-items: stretch !important;
    justify-content: initial !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: initial !important;

    /* Previeni influenze da altri CSS */
    box-sizing: border-box !important;
    float: none !important;
    position: relative !important;
    margin-bottom: 30px !important;
}

/* ===================================================================
 * WRAPPER SPECIFICO
 * Regole ancora più specifiche per il wrapper subscriber-stats-wrapper
 * ===================================================================
 */
body .subscriber-management-widget-container .subscriber-stats-wrapper,
html body .subscriber-management-widget-container .subscriber-stats-wrapper,
.subscriber-management-widget-container .subscriber-stats-wrapper,
.subscriber-management-widget-container .subscriber-stats-wrapper[class],
.subscriber-stats-wrapper[data-widget-type="subscriber-management"],
div[data-widget-type="subscriber-management"][data-layout="grid"],
#subscriber-stats-grid,
#subscriber-stats-grid.stats-grid,
#subscriber-stats-grid.subscriber-stats-wrapper {
    /* GRID LAYOUT FORZATO - sovrascrive qualsiasi altro stile */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;

    /* RESET COMPLETO proprietà flex che potrebbero interferire */
    flex: initial !important;
    flex-direction: initial !important;
    flex-wrap: initial !important;
    flex-flow: initial !important;
    flex-grow: initial !important;
    flex-shrink: initial !important;
    flex-basis: initial !important;

    /* RESET layout singole card */
    align-items: stretch !important;
    justify-content: initial !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: initial !important;

    /* Previeni influenze da altri CSS */
    box-sizing: border-box !important;
    float: none !important;
    position: relative !important;
    margin-bottom: 30px !important;

    /* Stile visivo migliorato */
    background: transparent !important;
    padding: 0 !important;
}

/* ===================================================================
 * SINGOLE CARD
 * Stili forzati per ogni singola card
 * ===================================================================
 */
.subscriber-management-widget-container #subscriber-stats-grid .stats-card,
.subscriber-management-widget-container .subscriber-stats-wrapper .stats-card,
#subscriber-stats-grid .stats-card,
.subscriber-stats-wrapper .stats-card {
    /* Reset completo */
    flex: initial !important;
    width: auto !important;
    min-width: initial !important;
    max-width: initial !important;

    /* Layout e stile visivo */
    position: relative !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 18px !important;

    /* Stile visivo */
    background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    border-radius: 16px !important;
    padding: 22px !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06), 0 4px 12px rgba(0, 0, 0, 0.04) !important;

    /* Transizioni */
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    overflow: visible !important;
}

/* ===================================================================
 * OVERRIDE MASSIMO PER CONSUMPTION SECTION
 * Regole specifiche per la sezione consumi che potrebbero essere sovrascritte
 * ===================================================================
 */
html body .subscriber-management-widget-container #consumption-section .stats-grid,
html body .subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper,
html body .subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper,
html body .subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper.stats-grid,
html body .subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper.stats-grid,
body .subscriber-management-widget-container #consumption-section .stats-grid,
body .subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section .stats-grid,
.subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper.stats-grid,
.subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper.stats-grid,
#consumption-section .subscriber-stats-wrapper,
#consumption-section .stats-grid,
#consumption-section div.subscriber-stats-wrapper,
#consumption-section .subscriber-stats-wrapper.stats-grid,
#consumption-section div.subscriber-stats-wrapper.stats-grid {
    /* GRID LAYOUT FORZATO - massima specificità */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;

    /* RESET COMPLETO proprietà flex */
    flex: initial !important;
    flex-direction: initial !important;
    flex-wrap: initial !important;
    flex-flow: initial !important;
    flex-grow: initial !important;
    flex-shrink: initial !important;
    flex-basis: initial !important;

    /* RESET layout */
    align-items: stretch !important;
    justify-content: initial !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: initial !important;

    /* Stile visivo */
    background: transparent !important;
    padding: 0 !important;
    margin-bottom: 30px !important;
    box-sizing: border-box !important;
    float: none !important;
    position: relative !important;
}

/* ===================================================================
 * OVERRIDE PER STATS CARDS NELLA CONSUMPTION SECTION
 * ===================================================================
 */
html body .subscriber-management-widget-container #consumption-section .stats-card,
body .subscriber-management-widget-container #consumption-section .stats-card,
.subscriber-management-widget-container #consumption-section .stats-card,
#consumption-section .stats-card {
    /* Reset completo per evitare interferenze */
    flex: initial !important;
    width: auto !important;
    min-width: initial !important;
    max-width: initial !important;

    /* Layout specifico per le card */
    position: relative !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 18px !important;

    /* Stile visivo delle card */
    background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    border-radius: 16px !important;
    padding: 22px !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06), 0 4px 12px rgba(0, 0, 0, 0.04) !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    overflow: visible !important;
}

/* ===================================================================
 * STILI RESPONSIVI
 * Garantire la correttezza anche in ambito mobile
 * ===================================================================
 */
@media (max-width: 768px) {
    #subscriber-stats-grid,
    .subscriber-stats-wrapper,
    .subscriber-management-widget-container .subscriber-stats-wrapper,
    .subscriber-management-widget-container #subscriber-stats-grid,
    .subscriber-management-widget-container #consumption-section .stats-grid,
    .subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper,
    #consumption-section .stats-grid,
    #consumption-section .subscriber-stats-wrapper {
        grid-template-columns: 1fr !important;
    }
}

/* ===================================================================
 * JAVASCRIPT FALLBACK CLASSES
 * Classi che possono essere applicate via JavaScript per forzare il layout
 * ===================================================================
 */
.force-grid-layout,
.js-ensure-grid,
.subscriber-grid-enforced {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
}

/* ===================================================================
 * OVERRIDE FINALE CON MASSIMA SPECIFICITÀ
 * Ultima risorsa per garantire che il layout grid sia mantenuto
 * ===================================================================
 */
html body div.subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper.stats-grid[id="subscriber-stats-grid"],
html body div.subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper.stats-grid,
html body .subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper[data-widget-type="subscriber-management"],
html body .subscriber-management-widget-container #consumption-section div[data-widget-type="subscriber-management"][data-layout="grid"] {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    flex: initial !important;
    flex-direction: initial !important;
    flex-wrap: initial !important;
}
