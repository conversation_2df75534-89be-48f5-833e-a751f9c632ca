<?php
/**
 * Gestione Help contestuale per i widget del plugin Financial Advisor
 */
class Financial_Advisor_Widget_Help_Manager {
    private static $instance = null;
    private $table_name;

    private function __construct() {
        error_log('FA Widget Help: Costruttore eseguito');
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'widget_help';
        // Hook per admin
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_post_fa_save_widget_help', [$this, 'save_widget_help']);
        add_action('admin_post_fa_delete_widget_help', [$this, 'delete_widget_help']);
        // Hook AJAX per frontend
        add_action('wp_ajax_fa_get_widget_help', [$this, 'ajax_get_widget_help']);
        add_action('wp_ajax_nopriv_fa_get_widget_help', [$this, 'ajax_get_widget_help']);
    }

    /**
     * Restituisce l'istanza singleton
     * @return Financial_Advisor_Widget_Help_Manager
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function add_admin_menu() {
        $parent_slug = 'document-viewer-settings'; // Slug del menu principale usato dal plugin
        add_submenu_page(
            $parent_slug,
            __('Widget Help', 'document-viewer-plugin'),
            __('Widget Help', 'document-viewer-plugin'),
            'manage_options',
            'fa-widget-help',
            [$this, 'render_admin_page']
        );
    }

    public function render_admin_page() {
        global $wpdb;
        $widgets = $this->get_registered_widgets();
        $current = isset($_GET['widget_id']) ? sanitize_text_field($_GET['widget_id']) : '';
        $help = $current ? $this->get_help_content($current) : '';
        // Debug: log widget e nonce
        error_log('FA Widget Help: render_admin_page, widget_id=' . $current);
        if ($current && !isset($widgets[$current])) {
            echo '<div class="notice notice-error"><p>' . __('Il widget selezionato non è valido o non esiste.', 'document-viewer-plugin') . '</p></div>';
            $current = '';
            $help = '';
        }
        // ...existing code...
        ?>
        <div class="wrap">
            <h1><?php _e('Widget Help Management', 'document-viewer-plugin'); ?></h1>
            <p><?php _e('Gestisci i testi di aiuto per ogni widget del plugin.', 'document-viewer-plugin'); ?></p>
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                <input type="hidden" name="action" value="fa_save_widget_help" />
                <?php wp_nonce_field('fa_save_widget_help', 'fa_widget_help_nonce'); ?>
                <table class="form-table">
                    <tr>
                        <th><label for="widget_id"><?php _e('Widget', 'document-viewer-plugin'); ?></label></th>
                        <td>
                            <select name="widget_id" id="widget_id" required>
                                <option value=""><?php _e('Seleziona widget', 'document-viewer-plugin'); ?></option>
                                <?php foreach ($widgets as $id => $name): ?>
                                    <option value="<?php echo esc_attr($id); ?>" <?php selected($current, $id); ?>><?php echo esc_html($name); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Seleziona il widget per cui aggiungere o modificare il testo di aiuto.', 'document-viewer-plugin'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="content"><?php _e('Contenuto Help', 'document-viewer-plugin'); ?></label></th>
                        <td>
                            <textarea name="content" id="content" rows="10" style="width:100%" required><?php echo esc_textarea($help); ?></textarea>
                            <p class="description"><?php _e('Inserisci il testo di aiuto HTML. Puoi usare tag HTML di base come &lt;p&gt;, &lt;strong&gt;, &lt;em&gt;, &lt;ul&gt;, &lt;li&gt;, etc.', 'document-viewer-plugin'); ?></p>
                        </td>
                    </tr>
                </table>
                <p>
                    <input type="submit" class="button button-primary" value="<?php _e('Salva Help', 'document-viewer-plugin'); ?>" />
                    <?php if ($current): ?>
                        <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=fa_delete_widget_help&widget_id=' . urlencode($current)), 'fa_delete_widget_help'); ?>" 
                           class="button button-secondary" 
                           onclick="return confirm('<?php _e('Sei sicuro di voler eliminare questo help?', 'document-viewer-plugin'); ?>');">
                           <?php _e('Elimina', 'document-viewer-plugin'); ?>
                        </a>
                    <?php endif; ?>
                </p>
            </form>
            <hr>
            <h2><?php _e('Help attualmente configurati', 'document-viewer-plugin'); ?></h2>
            <?php
            $existing_helps = $wpdb->get_results("SELECT * FROM {$this->table_name}", ARRAY_A);
            if ($existing_helps): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Widget', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Contenuto (anteprima)', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Azioni', 'document-viewer-plugin'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($existing_helps as $help_item): ?>
                            <tr>
                                <td><?php echo esc_html($widgets[$help_item['widget_id']] ?? $help_item['widget_id']); ?></td>
                                <td><?php echo wp_trim_words(strip_tags($help_item['content']), 15); ?></td>
                                <td>
                                    <a href="<?php echo add_query_arg(['page' => 'fa-widget-help', 'widget_id' => $help_item['widget_id']], admin_url('admin.php')); ?>" class="button button-small"><?php _e('Modifica', 'document-viewer-plugin'); ?></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><em><?php _e('Nessun help configurato ancora.', 'document-viewer-plugin'); ?></em></p>
            <?php endif; ?>
        </div>
        <script>
        document.getElementById('widget_id').addEventListener('change', function() {
            if (this.value) {
                window.location = '<?php echo admin_url('admin.php?page=fa-widget-help&widget_id='); ?>' + this.value;
            }
        });
        </script>
        <?php
        // ...existing code...
    }

    public function save_widget_help() {
        error_log('FA Widget Help: save_widget_help chiamata');
        // Debug: mostra ruolo e capability
        $current_user = wp_get_current_user();
        error_log('FA Widget Help: Utente attuale: ' . $current_user->user_login . ' | Ruoli: ' . implode(',', $current_user->roles));
        if (!current_user_can('manage_options')) {
            error_log('FA Widget Help: L\'utente NON ha manage_options');
        } else {
            error_log('FA Widget Help: L\'utente HA manage_options');
        }
        if (!check_admin_referer('fa_save_widget_help', 'fa_widget_help_nonce')) {
            error_log('FA Widget Help: Nonce non valido o mancante');
        } else {
            error_log('FA Widget Help: Nonce valido');
        }
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi per modificare gli help widget.', 'document-viewer-plugin'));
        }
        if (!check_admin_referer('fa_save_widget_help', 'fa_widget_help_nonce')) {
            error_log('FA Widget Help: Nonce non valido in save_widget_help');
            echo '<div class="notice notice-error"><p>' . __('La sessione è scaduta o la pagina è stata aggiornata. Ricarica la pagina e riprova.', 'document-viewer-plugin') . '</p></div>';
            exit;
        }

        global $wpdb;
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $content = wp_kses_post($_POST['content']);

        $exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$this->table_name} WHERE widget_id = %s", $widget_id));

        if ($exists) {
            $result = $wpdb->update(
                $this->table_name, 
                ['content' => $content], 
                ['widget_id' => $widget_id]
            );
        } else {
            $result = $wpdb->insert(
                $this->table_name, 
                ['widget_id' => $widget_id, 'content' => $content]
            );
        }

        if ($result !== false) {
            wp_redirect(add_query_arg([
                'page' => 'fa-widget-help', 
                'widget_id' => $widget_id,
                'updated' => '1'
            ], admin_url('admin.php')));
        } else {
            wp_redirect(add_query_arg([
                'page' => 'fa-widget-help', 
                'widget_id' => $widget_id,
                'error' => '1'
            ], admin_url('admin.php')));
        }
        exit;
    }

    public function delete_widget_help() {
        error_log('FA Widget Help: delete_widget_help chiamata');
        $current_user = wp_get_current_user();
        error_log('FA Widget Help DELETE: Utente attuale: ' . $current_user->user_login . ' | Ruoli: ' . implode(',', $current_user->roles));
        if (!current_user_can('manage_options')) {
            error_log('FA Widget Help DELETE: L\'utente NON ha manage_options');
        } else {
            error_log('FA Widget Help DELETE: L\'utente HA manage_options');
        }
        if (!check_admin_referer('fa_delete_widget_help')) {
            error_log('FA Widget Help DELETE: Nonce non valido o mancante');
        } else {
            error_log('FA Widget Help DELETE: Nonce valido');
        }
        if (!current_user_can('manage_options') || !check_admin_referer('fa_delete_widget_help')) {
            wp_die(__('Permesso negato', 'document-viewer-plugin'));
        }

        global $wpdb;
        $widget_id = sanitize_text_field($_GET['widget_id']);
        $result = $wpdb->delete($this->table_name, ['widget_id' => $widget_id]);

        if ($result !== false) {
            wp_redirect(add_query_arg(['page' => 'fa-widget-help', 'deleted' => '1'], admin_url('admin.php')));
        } else {
            wp_redirect(add_query_arg(['page' => 'fa-widget-help', 'error' => '1'], admin_url('admin.php')));
        }
        exit;
    }

    /**
     * Handler AJAX per recuperare il contenuto dell'help
     */
    public function ajax_get_widget_help() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['_ajax_nonce'], 'fa_widget_help_nonce')) {
            wp_send_json_error(['message' => 'Nonce verification failed']);
            return;
        }
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $content = $this->get_help_content($widget_id);
        
        if ($content) {
            wp_send_json_success(['content' => $content]);
        } else {
            wp_send_json_error(['message' => 'No help content found']);
        }
    }

    public function get_help_content($widget_id) {
        global $wpdb;
        return $wpdb->get_var($wpdb->prepare("SELECT content FROM {$this->table_name} WHERE widget_id = %s", $widget_id));
    }

    public function get_registered_widgets() {
        global $wp_widget_factory;
        $widgets = [];
        
        if (isset($wp_widget_factory->widgets)) {
            foreach ($wp_widget_factory->widgets as $class => $obj) {
                $widgets[$class] = $obj->name;
            }
        }
        
        return $widgets;
    }
}

// Inizializza la classe solo in admin come singleton
if (is_admin()) {
    Financial_Advisor_Widget_Help_Manager::get_instance();
}

/**
 * Funzione helper per ottenere l'istanza singleton della classe
 * @return Financial_Advisor_Widget_Help_Manager|null
 */
function financial_advisor_widget_help_manager() {
    if (class_exists('Financial_Advisor_Widget_Help_Manager')) {
        return Financial_Advisor_Widget_Help_Manager::get_instance();
    }
    return null;
}
