<?php
/**
 * This file is part of the Peast package
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information refer to the LICENSE file
 * distributed with this source code
 */
namespace Peast\Syntax\Node;

/**
 * A node that represents the "continue" statement inside loops.
 * 
 * <AUTHOR> <<EMAIL>>
 */
class ContinueStatement extends Node implements Statement
{
    /**
     * Map of node properties
     * 
     * @var array 
     */
    protected $propertiesMap = array(
        "label" => true
    );
    
    /**
     * The optional label of the continue statement
     * 
     * @var Identifier 
     */
    protected $label;
    
    /**
     * Returns the node's label
     * 
     * @return Identifier
     */
    public function getLabel()
    {
        return $this->label;
    }
    
    /**
     * Sets the node's label
     * 
     * @param Identifier $label Node's label
     * 
     * @return $this
     */
    public function setLabel($label)
    {
        $this->assertType($label, "Identifier", true);
        $this->label = $label;
        return $this;
    }
}