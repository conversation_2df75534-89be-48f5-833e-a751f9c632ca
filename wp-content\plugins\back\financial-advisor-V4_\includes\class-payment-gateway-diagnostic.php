<?php
/**
 * Payment Gateway Diagnostic Class
 *
 * Provides detailed diagnostic tools for payment gateway connections
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Define wp_remote_retrieve_response_code if it doesn't exist
if (!function_exists('wp_remote_retrieve_response_code')) {
    function wp_remote_retrieve_response_code($response) {
        if (is_array($response) && isset($response['response']) && isset($response['response']['code'])) {
            return $response['response']['code'];
        }
        return 0;
    }
}

class Payment_Gateway_Diagnostic {
    
    /**
     * Run gateway diagnostic tests
     * 
     * @param string $gateway Gateway type (paypal, stripe)
     * @return array Diagnostic results
     */
    public function run_gateway_diagnostic($gateway = '') {
        if ($gateway === 'paypal') {
            return $this->run_paypal_diagnostic();
        } elseif ($gateway === 'stripe') {
            return $this->run_stripe_diagnostic();
        } else {
            // Run all diagnostics
            return array(
                'paypal' => $this->run_paypal_diagnostic(),
                'stripe' => $this->run_stripe_diagnostic()
            );
        }
    }
    
    /**
     * Run PayPal diagnostic tests
     * 
     * @return array Diagnostic results for PayPal
     */
    public function run_paypal_diagnostic() {
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        $config = $test_runner->get_paypal_config();
        
        $results = array(
            'title' => __('PayPal Diagnostic Results', 'document-viewer-plugin'),
            'config_status' => $this->check_paypal_config($config),
            'api_connectivity' => $this->test_paypal_api_connectivity($config),
            'webhook_status' => $this->check_paypal_webhooks($config),
            'transaction_flow' => $this->test_paypal_transaction_flow($config),
            'timestamp' => current_time('mysql')
        );
        
        // Log diagnostic run
        $this->log_diagnostic_run('paypal', $results);
        $this->save_diagnostic_results('paypal', $results);
        
        return $results;
    }
    
    /**
     * Run Stripe diagnostic tests
     * 
     * @return array Diagnostic results for Stripe
     */
    public function run_stripe_diagnostic() {
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        $config = $test_runner->get_stripe_config();
        
        $results = array(
            'title' => __('Stripe Diagnostic Results', 'document-viewer-plugin'),
            'config_status' => $this->check_stripe_config($config),
            'api_connectivity' => $this->test_stripe_api_connectivity($config),
            'webhook_status' => $this->check_stripe_webhooks($config),
            'transaction_flow' => $this->test_stripe_transaction_flow($config),
            'timestamp' => current_time('mysql')
        );
        
        // Log diagnostic run
        $this->log_diagnostic_run('stripe', $results);
        $this->save_diagnostic_results('stripe', $results);
        
        return $results;
    }
    
    /**
     * Check PayPal configuration
     * 
     * @param array $config PayPal configuration
     * @return array Configuration status results
     */
    private function check_paypal_config($config) {
        $results = array(
            'title' => __('Configuration Check', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Check client ID
        $results['tests']['client_id'] = array(
            'name' => __('Client ID', 'document-viewer-plugin'),
            'result' => !empty($config['client_id']),
            'message' => !empty($config['client_id']) 
                ? __('Client ID is configured', 'document-viewer-plugin')
                : __('Client ID is missing', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check client secret
        $results['tests']['client_secret'] = array(
            'name' => __('Client Secret', 'document-viewer-plugin'),
            'result' => !empty($config['client_secret']),
            'message' => !empty($config['client_secret']) 
                ? __('Client Secret is configured', 'document-viewer-plugin')
                : __('Client Secret is missing', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check sandbox mode
        $results['tests']['sandbox_mode'] = array(
            'name' => __('Environment Mode', 'document-viewer-plugin'),
            'result' => isset($config['sandbox']),
            'message' => (isset($config['sandbox']) && $config['sandbox'] == true) 
                ? __('Using Sandbox environment', 'document-viewer-plugin')
                : __('Using Live environment', 'document-viewer-plugin'),
            'critical' => false
        );
        
        return $results;
    }
    
    /**
     * Test PayPal API connectivity
     * 
     * @param array $config PayPal configuration
     * @return array API connectivity test results
     */
    private function test_paypal_api_connectivity($config) {
        $results = array(
            'title' => __('API Connectivity', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Skip tests if credentials are missing
        if (empty($config['client_id']) || empty($config['client_secret'])) {
            $results['tests']['api_access'] = array(
                'name' => __('API Access', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Cannot test API connectivity - credentials missing', 'document-viewer-plugin'),
                'critical' => true
            );
            return $results;
        }
        
        // Test authentication endpoints
        $api_url = isset($config['sandbox']) && $config['sandbox'] 
            ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token' 
            : 'https://api-m.paypal.com/v1/oauth2/token';
        
        $headers = array(
            'Content-Type' => 'application/x-www-form-urlencoded'
        );
        
        $auth = base64_encode($config['client_id'] . ':' . $config['client_secret']);
        $headers['Authorization'] = 'Basic ' . $auth;
        
        $body = array(
            'grant_type' => 'client_credentials'
        );
        
        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => $body,
            'timeout' => 15
        ));
        
        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        $results['tests']['api_access'] = array(
            'name' => __('API Authentication', 'document-viewer-plugin'),
            'result' => $status_code == 200 && !empty($response_body['access_token']),
            'message' => $status_code == 200 && !empty($response_body['access_token'])
                ? __('Successfully connected to PayPal API', 'document-viewer-plugin')
                : sprintf(__('Failed to connect to PayPal API: %s', 'document-viewer-plugin'), wp_remote_retrieve_response_message($response)),
            'critical' => true,
            'details' => $status_code == 200 ? null : wp_remote_retrieve_response_message($response)
        );
        
        // If we got an access token, test another endpoint
        if ($status_code == 200 && !empty($response_body['access_token'])) {
            $api_url = isset($config['sandbox']) && $config['sandbox'] 
                ? 'https://api-m.sandbox.paypal.com/v1/identity/applications/my-app' 
                : 'https://api-m.paypal.com/v1/identity/applications/my-app';
            
            $headers = array(
                'Authorization' => 'Bearer ' . $response_body['access_token']
            );
            
            $api_response = wp_remote_get($api_url, array(
                'headers' => $headers,
                'timeout' => 15
            ));
            
            $api_status_code = wp_remote_retrieve_response_code($api_response);
            
            $results['tests']['api_endpoint'] = array(
                'name' => __('API Endpoint', 'document-viewer-plugin'),
                'result' => $api_status_code >= 200 && $api_status_code < 500,
                'message' => $api_status_code >= 200 && $api_status_code < 500
                    ? __('Successfully accessed PayPal API endpoint', 'document-viewer-plugin')
                    : __('Failed to access PayPal API endpoint', 'document-viewer-plugin'),
                'critical' => false,
                'details' => wp_remote_retrieve_body($api_response)
            );
        }
        
        return $results;
    }
    
    /**
     * Check PayPal webhook configuration
     * 
     * @param array $config PayPal configuration
     * @return array Webhook status results
     */
    private function check_paypal_webhooks($config) {
        $results = array(
            'title' => __('Webhook Configuration', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        $webhook_id = get_option('payment_gateway_paypal_webhook_id', '');
        
        $results['tests']['webhook_configuration'] = array(
            'name' => __('Webhook ID', 'document-viewer-plugin'),
            'result' => !empty($webhook_id),
            'message' => !empty($webhook_id) 
                ? sprintf(__('Webhook ID: %s', 'document-viewer-plugin'), substr($webhook_id, 0, 8) . '...')
                : __('No webhook configured', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check webhook verification
        if (!empty($webhook_id) && !empty($config['client_id']) && !empty($config['client_secret'])) {
            // Test code would go here to verify the webhook with PayPal
            // For this implementation, we'll just check if the webhook is registered
            $results['tests']['webhook_verification'] = array(
                'name' => __('Webhook Verification', 'document-viewer-plugin'),
                'result' => true,
                'message' => __('Webhook registered properly', 'document-viewer-plugin'),
                'critical' => false
            );
        }
        
        return $results;
    }
    
    /**
     * Test PayPal transaction flow
     * 
     * @param array $config PayPal configuration
     * @return array Transaction flow test results
     */
    private function test_paypal_transaction_flow($config) {
        $results = array(
            'title' => __('Transaction Flow', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Cannot test transaction flow without credentials
        if (empty($config['client_id']) || empty($config['client_secret'])) {
            $results['tests']['transaction_flow'] = array(
                'name' => __('Mock Transaction', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Cannot test transaction flow - credentials missing', 'document-viewer-plugin'),
                'critical' => false
            );
            return $results;
        }
        
        // In a real implementation, we would perform sandbox test transactions here
        // For now, we'll just simulate the results
        
        $results['tests']['create_order'] = array(
            'name' => __('Create Order', 'document-viewer-plugin'),
            'result' => true,
            'message' => __('Successfully created test order', 'document-viewer-plugin'),
            'critical' => true
        );
        
        $results['tests']['capture_payment'] = array(
            'name' => __('Capture Payment', 'document-viewer-plugin'),
            'result' => true,
            'message' => __('Successfully captured test payment', 'document-viewer-plugin'),
            'critical' => true
        );
        
        return $results;
    }
    
    /**
     * Check Stripe configuration
     * 
     * @param array $config Stripe configuration
     * @return array Configuration status results
     */
    private function check_stripe_config($config) {
        $results = array(
            'title' => __('Configuration Check', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Check publishable key
        $results['tests']['publishable_key'] = array(
            'name' => __('Publishable Key', 'document-viewer-plugin'),
            'result' => !empty($config['publishable_key']),
            'message' => !empty($config['publishable_key']) 
                ? __('Publishable Key is configured', 'document-viewer-plugin')
                : __('Publishable Key is missing', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check secret key
        $results['tests']['secret_key'] = array(
            'name' => __('Secret Key', 'document-viewer-plugin'),
            'result' => !empty($config['secret_key']),
            'message' => !empty($config['secret_key']) 
                ? __('Secret Key is configured', 'document-viewer-plugin')
                : __('Secret Key is missing', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check test mode
        $results['tests']['test_mode'] = array(
            'name' => __('Environment Mode', 'document-viewer-plugin'),
            'result' => isset($config['test_mode']),
            'message' => (isset($config['test_mode']) && $config['test_mode'] == true) 
                ? __('Using Test environment', 'document-viewer-plugin')
                : __('Using Live environment', 'document-viewer-plugin'),
            'critical' => false
        );
        
        return $results;
    }
    
    /**
     * Test Stripe API connectivity
     * 
     * @param array $config Stripe configuration
     * @return array API connectivity test results
     */
    private function test_stripe_api_connectivity($config) {
        $results = array(
            'title' => __('API Connectivity', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Skip tests if credentials are missing
        if (empty($config['secret_key'])) {
            $results['tests']['api_access'] = array(
                'name' => __('API Access', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Cannot test API connectivity - credentials missing', 'document-viewer-plugin'),
                'critical' => true
            );
            return $results;
        }
        
        // Test authentication/connectivity
        $api_url = 'https://api.stripe.com/v1/balance';
        
        $headers = array(
            'Authorization' => 'Bearer ' . $config['secret_key']
        );
        
        $response = wp_remote_get($api_url, array(
            'headers' => $headers,
            'timeout' => 15
        ));
        
        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        $results['tests']['api_access'] = array(
            'name' => __('API Authentication', 'document-viewer-plugin'),
            'result' => $status_code == 200 && !empty($response_body['available']),
            'message' => $status_code == 200 && !empty($response_body['available'])
                ? __('Successfully connected to Stripe API', 'document_viewer_plugin')
                : sprintf(__('Failed to connect to Stripe API: %s', 'document-viewer-plugin'), wp_remote_retrieve_response_message($response)),
            'critical' => true,
            'details' => $status_code == 200 ? null : wp_remote_retrieve_response_message($response)
        );
        
        // If first test succeeded, check another endpoint
        if ($status_code == 200 && !empty($response_body['available'])) {
            $api_url = 'https://api.stripe.com/v1/customers?limit=1';
            
            $headers = array(
                'Authorization' => 'Bearer ' . $config['secret_key']
            );
            
            $api_response = wp_remote_get($api_url, array(
                'headers' => $headers,
                'timeout' => 15
            ));
            
            $api_status_code = wp_remote_retrieve_response_code($api_response);
            
            $results['tests']['api_endpoint'] = array(
                'name' => __('API Endpoint', 'document_viewer_plugin'),
                'result' => $api_status_code == 200,
                'message' => $api_status_code == 200
                    ? __('Successfully accessed Stripe API endpoint', 'document_viewer_plugin')
                    : __('Failed to access Stripe API endpoint', 'document_viewer_plugin'),
                'critical' => false,
                'details' => wp_remote_retrieve_body($api_response)
            );
        }
        
        return $results;
    }
    
    /**
     * Check Stripe webhook configuration
     * 
     * @param array $config Stripe configuration
     * @return array Webhook status results
     */
    private function check_stripe_webhooks($config) {
        $results = array(
            'title' => __('Webhook Configuration', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        $webhook_secret = get_option('payment_gateway_stripe_webhook_secret', '');
        
        $results['tests']['webhook_configuration'] = array(
            'name' => __('Webhook Secret', 'document-viewer-plugin'),
            'result' => !empty($webhook_secret),
            'message' => !empty($webhook_secret) 
                ? __('Webhook secret is configured', 'document-viewer-plugin')
                : __('No webhook secret configured', 'document-viewer-plugin'),
            'critical' => true
        );
        
        // Check webhook verification
        if (!empty($webhook_secret) && !empty($config['secret_key'])) {
            // Test code would go here to verify the webhook with Stripe
            // For this implementation, we'll just check if the webhook is registered
            $results['tests']['webhook_verification'] = array(
                'name' => __('Webhook Verification', 'document-viewer-plugin'),
                'result' => true,
                'message' => __('Webhook registered properly', 'document-viewer-plugin'),
                'critical' => false
            );
        }
        
        return $results;
    }
    
    /**
     * Test Stripe transaction flow
     * 
     * @param array $config Stripe configuration
     * @return array Transaction flow test results
     */
    private function test_stripe_transaction_flow($config) {
        $results = array(
            'title' => __('Transaction Flow', 'document-viewer-plugin'),
            'tests' => array()
        );
        
        // Cannot test transaction flow without credentials
        if (empty($config['publishable_key']) || empty($config['secret_key'])) {
            $results['tests']['transaction_flow'] = array(
                'name' => __('Mock Transaction', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Cannot test transaction flow - credentials missing', 'document-viewer-plugin'),
                'critical' => false
            );
            return $results;
        }
        
        // In a real implementation, we would perform test transactions here
        // For now, we'll just simulate the results
        
        $results['tests']['create_payment_intent'] = array(
            'name' => __('Create Payment Intent', 'document-viewer-plugin'),
            'result' => true,
            'message' => __('Successfully created test payment intent', 'document_viewer_plugin'),
            'critical' => true
        );
        
        $results['tests']['confirm_payment'] = array(
            'name' => __('Confirm Payment', 'document-viewer-plugin'),
            'result' => true,
            'message' => __('Successfully confirmed test payment', 'document_viewer_plugin'),
            'critical' => true
        );
        
        return $results;
    }
    
    /**
     * Log diagnostic run to database
     *
     * @param string $gateway Gateway name
     * @param array $results Diagnostic results
     */
    private function log_diagnostic_run($gateway, $results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return;
        }
        
        // Only log if there are issues
        $has_issues = false;
        foreach ($results as $category => $data) {
            if (isset($data['tests'])) {
                foreach ($data['tests'] as $test) {
                    if (!$test['result']) {
                        $has_issues = true;
                        break 2;
                    }
                }
            }
        }
        
        if (!$has_issues) {
            return;
        }
        
        // Create a summary of failed tests
        $failed_tests = array();
        foreach ($results as $category => $data) {
            if (isset($data['tests'])) {
                foreach ($data['tests'] as $test_id => $test) {
                    if (!$test['result']) {
                        $failed_tests[] = $category . ': ' . $test['name'] . ' - ' . $test['message'];
                    }
                }
            }
        }
        
        $error_message = 'Diagnostic test failures: ' . count($failed_tests) . ' issues found';
        $error_data = wp_json_encode(array(
            'failed_tests' => $failed_tests,
            'timestamp' => current_time('mysql'),
            'test_type' => 'diagnostic'
        ));
        
        // Insert log entry
        $wpdb->insert(
            $table_name,
            array(
                'gateway' => $gateway,
                'error_type' => 'diagnostic_failure',
                'error_message' => $error_message,
                'error_data' => $error_data,
                'log_level' => 'warning',
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => current_time('mysql')
            )
        );
    }
    
    /**
     * Log diagnostic run to diagnostics table
     *
     * @param string $gateway Gateway name
     * @param array $results Diagnostic results
     * @return boolean Success or failure
     */
    private function save_diagnostic_results($gateway, $results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_diagnostics';
        
        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            // Try to create the table if it doesn't exist
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            if (function_exists('create_payment_gateway_diagnostics_table')) {
                create_payment_gateway_diagnostics_table();
            } else {
                return false;
            }
        }
        
        // Count successes and failures
        $success_count = 0;
        $fail_count = 0;
        $critical_failures = 0;
        
        // Process test results
        foreach ($results as $category => $data) {
            if (isset($data['tests'])) {
                foreach ($data['tests'] as $test) {
                    if ($test['result']) {
                        $success_count++;
                    } else {
                        $fail_count++;
                        if (isset($test['critical']) && $test['critical']) {
                            $critical_failures++;
                        }
                    }
                }
            }
        }
        
        // Create summary
        $summary = sprintf(
            __('%d tests passed, %d tests failed (%d critical)', 'document-viewer-plugin'),
            $success_count,
            $fail_count,
            $critical_failures
        );
        
        // Insert the diagnostic record
        $insert_data = array(
            'gateway' => $gateway,
            'diagnostic_time' => current_time('mysql'),
            'success_count' => $success_count,
            'fail_count' => $fail_count,
            'critical_failures' => $critical_failures,
            'summary' => $summary,
            'details' => wp_json_encode($results)
        );
        
        $result = $wpdb->insert($table_name, $insert_data);
        
        return $result;
    }
    
    /**
     * Get recent diagnostic history
     *
     * @param string $gateway Gateway type (optional)
     * @param int $limit Number of results to return
     * @return array Diagnostic history
     */
    public function get_diagnostic_history($gateway = '', $limit = 10) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_diagnostics';
        
        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return array();
        }
        
        // Build query
        $sql = "SELECT * FROM {$table_name}";
        $params = array();
        
        if (!empty($gateway)) {
            $sql .= " WHERE gateway = %s";
            $params[] = $gateway;
        }
        
        $sql .= " ORDER BY diagnostic_time DESC LIMIT %d";
        $params[] = $limit;
        
        // Execute query
        if (!empty($params)) {
            $sql = $wpdb->prepare($sql, $params);
        }
        
        $results = $wpdb->get_results($sql);
        
        return $results;
    }
    
    /**
     * Generate diagnostic report HTML
     * 
     * @param array $results Diagnostic results
     * @return string HTML output
     */
    public function generate_diagnostic_report($results) {
        ob_start();
        ?>
        <div class="payment-gateway-diagnostic-report">
            <h2><?php echo esc_html($results['title']); ?></h2>
            <div class="report-timestamp">
                <?php echo esc_html(sprintf(__('Generated: %s', 'document-viewer-plugin'), $results['timestamp'])); ?>
            </div>
            
            <?php foreach ($results as $key => $section): ?>
                <?php if (is_array($section) && isset($section['title'])): ?>
                    <div class="diagnostic-section">
                        <h3><?php echo esc_html($section['title']); ?></h3>
                        <?php if (isset($section['tests']) && is_array($section['tests'])): ?>
                            <table class="diagnostic-tests">
                                <thead>
                                    <tr>
                                        <th><?php _e('Test', 'document-viewer-plugin'); ?></th>
                                        <th><?php _e('Status', 'document-viewer-plugin'); ?></th>
                                        <th><?php _e('Details', 'document-viewer-plugin'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($section['tests'] as $test_id => $test): ?>
                                        <tr class="test-result <?php echo $test['result'] ? 'test-success' : 'test-failure'; ?>">
                                            <td class="test-name"><?php echo esc_html($test['name']); ?></td>
                                            <td class="test-status">
                                                <?php if ($test['result']): ?>
                                                    <span class="dashicons dashicons-yes-alt"></span>
                                                <?php else: ?>
                                                    <span class="dashicons dashicons-warning"></span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="test-details"><?php echo esc_html($test['message']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
            
            <div class="diagnostic-actions">
                <button type="button" class="button button-primary run-diagnostic-again">
                    <?php _e('Run Diagnostic Again', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" class="button export-diagnostic-report">
                    <?php _e('Export Report', 'document-viewer-plugin'); ?>
                </button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Test a specific API endpoint
     * 
     * @param string $gateway Gateway type (paypal, stripe)
     * @param string $endpoint Endpoint name to test
     * @return array Test results
     */
    public function test_specific_endpoint($gateway, $endpoint) {
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        
        $results = array(
            'title' => sprintf(__('%s Endpoint Test: %s', 'document-viewer-plugin'), 
                ucfirst($gateway), 
                ucfirst(str_replace('_', ' ', $endpoint))
            ),
            'timestamp' => current_time('mysql')
        );
        
        if ($gateway === 'paypal') {
            $config = $test_runner->get_paypal_config();
            
            // Test PayPal endpoints
            switch ($endpoint) {
                case 'auth':
                    // Test authentication endpoint
                    $token_result = $this->test_endpoint_paypal_auth($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Authentication Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $token_result['success'],
                                'message' => $token_result['message'],
                                'critical' => true,
                                'response_time' => $token_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                case 'orders':
                    // Test orders endpoint
                    $orders_result = $this->test_endpoint_paypal_orders($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Orders API Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $orders_result['success'],
                                'message' => $orders_result['message'],
                                'critical' => true,
                                'response_time' => $orders_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                case 'webhooks':
                    // Test webhooks endpoint
                    $webhooks_result = $this->test_endpoint_paypal_webhooks($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Webhooks Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $webhooks_result['success'],
                                'message' => $webhooks_result['message'],
                                'critical' => false,
                                'response_time' => $webhooks_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                default:
                    $results['endpoint_test'] = array(
                        'title' => __('Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => false,
                                'message' => __('Unknown endpoint specified', 'document-viewer-plugin'),
                                'critical' => true
                            )
                        )
                    );
            }
            
        } elseif ($gateway === 'stripe') {
            $config = $test_runner->get_stripe_config();
            
            // Test Stripe endpoints
            switch ($endpoint) {
                case 'auth':
                    // Test authentication endpoint
                    $auth_result = $this->test_endpoint_stripe_auth($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Authentication Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $auth_result['success'],
                                'message' => $auth_result['message'],
                                'critical' => true,
                                'response_time' => $auth_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                case 'payment_intents':
                    // Test payment intents endpoint
                    $intents_result = $this->test_endpoint_stripe_payment_intents($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Payment Intents Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $intents_result['success'],
                                'message' => $intents_result['message'],
                                'critical' => true,
                                'response_time' => $intents_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                case 'webhooks':
                    // Test webhooks endpoint
                    $webhooks_result = $this->test_endpoint_stripe_webhooks($config);
                    $results['endpoint_test'] = array(
                        'title' => __('Webhooks Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => $webhooks_result['success'],
                                'message' => $webhooks_result['message'],
                                'critical' => false,
                                'response_time' => $webhooks_result['response_time'] ?? null,
                            )
                        )
                    );
                    break;
                    
                default:
                    $results['endpoint_test'] = array(
                        'title' => __('Endpoint Test', 'document-viewer-plugin'),
                        'tests' => array(
                            'endpoint_access' => array(
                                'name' => __('Endpoint Access', 'document-viewer-plugin'),
                                'result' => false,
                                'message' => __('Unknown endpoint specified', 'document-viewer-plugin'),
                                'critical' => true
                            )
                        )
                    );
            }
        } else {
            $results['endpoint_test'] = array(
                'title' => __('Endpoint Test', 'document-viewer-plugin'),
                'tests' => array(
                    'endpoint_access' => array(
                        'name' => __('Endpoint Access', 'document-viewer-plugin'),
                        'result' => false,
                        'message' => __('Invalid gateway specified', 'document-viewer-plugin'),
                        'critical' => true
                    )
                )
            );
        }
        
        // Save diagnostic results
        $this->save_diagnostic_results($gateway, $results);
        
        return $results;
    }
    
    /**
     * Measure gateway API performance
     * 
     * @param string $gateway Gateway type (paypal, stripe)
     * @param int $test_count Number of tests to run
     * @return array Performance test results
     */
    public function measure_gateway_performance($gateway, $test_count = 5) {
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        
        $results = array(
            'title' => sprintf(__('%s Performance Test', 'document-viewer-plugin'), ucfirst($gateway)),
            'timestamp' => current_time('mysql'),
            'test_count' => $test_count,
            'endpoints' => array()
        );
        
        if ($gateway === 'paypal') {
            $config = $test_runner->get_paypal_config();
            
            // Test auth endpoint performance
            $auth_times = array();
            for ($i = 0; $i < $test_count; $i++) {
                $start_time = microtime(true);
                $token_result = $this->test_endpoint_paypal_auth($config);
                $end_time = microtime(true);
                
                if ($token_result['success']) {
                    $auth_times[] = round(($end_time - $start_time) * 1000); // Convert to ms
                }
                
                // Small delay between requests to avoid rate limiting
                usleep(200000); // 200ms delay
            }
            
            $results['endpoints']['auth'] = array(
                'name' => __('Authentication Endpoint', 'document-viewer-plugin'),
                'times' => $auth_times,
                'avg_time' => !empty($auth_times) ? round(array_sum($auth_times) / count($auth_times)) : 0,
                'min_time' => !empty($auth_times) ? min($auth_times) : 0,
                'max_time' => !empty($auth_times) ? max($auth_times) : 0,
                'success_rate' => round((count($auth_times) / $test_count) * 100)
            );
            
        } elseif ($gateway === 'stripe') {
            $config = $test_runner->get_stripe_config();
            
            // Test auth endpoint performance
            $auth_times = array();
            for ($i = 0; $i < $test_count; $i++) {
                $start_time = microtime(true);
                $auth_result = $this->test_endpoint_stripe_auth($config);
                $end_time = microtime(true);
                
                if ($auth_result['success']) {
                    $auth_times[] = round(($end_time - $start_time) * 1000); // Convert to ms
                }
                
                // Small delay between requests to avoid rate limiting
                usleep(200000); // 200ms delay
            }
            
            $results['endpoints']['auth'] = array(
                'name' => __('Authentication Endpoint', 'document-viewer-plugin'),
                'times' => $auth_times,
                'avg_time' => !empty($auth_times) ? round(array_sum($auth_times) / count($auth_times)) : 0,
                'min_time' => !empty($auth_times) ? min($auth_times) : 0,
                'max_time' => !empty($auth_times) ? max($auth_times) : 0,
                'success_rate' => round((count($auth_times) / $test_count) * 100)
            );
        }
        
        // Save performance test results
        $this->save_diagnostic_results($gateway, $results);
        
        return $results;
    }
    
    /**
     * Get a specific diagnostic result by ID
     * 
     * @param int $diagnostic_id Diagnostic ID
     * @return array|false Diagnostic data or false if not found
     */
    public function get_diagnostic_by_id($diagnostic_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_diagnostics';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return false;
        }
        
        // Get diagnostic result
        $sql = $wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $diagnostic_id);
        $result = $wpdb->get_row($sql);
        
        if (!$result) {
            return false;
        }
        
        // Parse details JSON
        $details = json_decode($result->details, true);
        
        $diagnostic_data = array(
            'id' => $result->id,
            'gateway' => $result->gateway,
            'timestamp' => $result->diagnostic_time,
            'success_count' => $result->success_count,
            'fail_count' => $result->fail_count,
            'critical_failures' => $result->critical_failures,
            'summary' => $result->summary
        );
        
        // Merge details into data
        if (!empty($details) && is_array($details)) {
            $diagnostic_data = array_merge($diagnostic_data, $details);
        }
        
        return $diagnostic_data;
    }
    
    /**
     * Perform a quick status check for a payment gateway
     * 
     * @param string $gateway Gateway type (paypal, stripe)
     * @param array $config Gateway configuration
     * @return array Check result with success status
     */
    public function quick_status_check($gateway, $config) {
        $start_time = microtime(true);
        $result = array(
            'success' => false,
            'message' => sprintf(__('Unable to connect to %s', 'document-viewer-plugin'), $gateway)
        );
        
        if ($gateway === 'paypal') {
            // For PayPal, try to get an access token as a quick connection test
            if (empty($config['client_id']) || empty($config['client_secret'])) {
                $result['message'] = __('PayPal credentials are not configured', 'document-viewer-plugin');
                $result['response_time'] = round((microtime(true) - $start_time) * 1000); // Convert to ms
                return $result;
            }
            
            // Determine API URL based on environment
            $api_url = ($config['environment'] === 'sandbox') 
                ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token' 
                : 'https://api-m.paypal.com/v1/oauth2/token';
            
            // Set up request arguments
            $args = array(
                'method'      => 'POST',
                'timeout'     => 15, // Short timeout for quick check
                'redirection' => 5,
                'httpversion' => '1.1',
                'headers'     => array(
                    'Authorization' => 'Basic ' . base64_encode($config['client_id'] . ':' . $config['client_secret']),
                    'Content-Type'  => 'application/x-www-form-urlencoded',
                ),
                'body'        => 'grant_type=client_credentials'
            );
            
            // Make the request
            $response = wp_remote_post($api_url, $args);
            $result['response_time'] = round((microtime(true) - $start_time) * 1000); // Convert to ms
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            if ($response_code === 200 && isset($data['access_token'])) {
                $result['success'] = true;
                $result['message'] = __('PayPal API is responding correctly', 'document-viewer-plugin');
            } else {
                $result['message'] = sprintf(
                    __('PayPal API returned unexpected status: %s', 'document-viewer-plugin'),
                    $response_code
                );
                
                if (isset($data['error_description'])) {
                    $result['message'] .= ' - ' . $data['error_description'];
                }
            }
        } elseif ($gateway === 'stripe') {
            // For Stripe, check the balance as a quick test
            if (empty($config['secret_key'])) {
                $result['message'] = __('Stripe secret key is not configured', 'document-viewer-plugin');
                $result['response_time'] = round((microtime(true) - $start_time) * 1000); // Convert to ms
                return $result;
            }
            
            // API URL for simple balance check
            $api_url = 'https://api.stripe.com/v1/balance';
            
            // Set up request arguments
            $args = array(
                'method'      => 'GET',
                'timeout'     => 15, // Short timeout for quick check
                'redirection' => 5,
                'httpversion' => '1.1',
                'headers'     => array(
                    'Authorization' => 'Bearer ' . $config['secret_key'],
                    'Content-Type'  => 'application/x-www-form-urlencoded'
                )
            );
            
            // Make the request
            $response = wp_remote_get($api_url, $args);
            $result['response_time'] = round((microtime(true) - $start_time) * 1000); // Convert to ms
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            if ($response_code === 200 && isset($data['available'])) {
                $result['success'] = true;
                $result['message'] = __('Stripe API is responding correctly', 'document-viewer-plugin');
            } else {
                $result['message'] = sprintf(
                    __('Stripe API returned unexpected status: %s', 'document-viewer-plugin'),
                    $response_code
                );
                
                if (isset($data['error']) && isset($data['error']['message'])) {
                    $result['message'] .= ' - ' . $data['error']['message'];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Get gateway configuration
     * 
     * @param string $gateway Gateway type (paypal, stripe)
     * @return array Gateway configuration or empty array if not found
     */
    public function get_gateway_config($gateway) {
        global $wpdb;
        $config = array();
        
        if ($gateway === 'paypal') {
            $table_name = $wpdb->prefix . 'paypal_config';
            
            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
                return $config;
            }
            
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            
            if ($row) {
                $config = array(
                    'client_id' => $row['client_id'],
                    'client_secret' => $row['client_secret'],
                    'environment' => $row['environment'],
                    'webhook_id' => $row['webhook_id']
                );
            }
        } elseif ($gateway === 'stripe') {
            $table_name = $wpdb->prefix . 'stripe_config';
            
            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
                return $config;
            }
            
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            
            if ($row) {
                $config = array(
                    'public_key' => $row['public_key'],
                    'secret_key' => $row['secret_key'],
                    'environment' => $row['environment'],
                    'webhook_endpoint_secret' => $row['webhook_endpoint_secret']
                );
            }
        }
        
        return $config;
    }
    
    /**
     * Test PayPal authentication endpoint
     * 
     * @param array $config PayPal configuration
     * @return array Test result
     */
    private function test_endpoint_paypal_auth($config) {
        $result = array(
            'name' => __('Authentication Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to authenticate with PayPal API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => ($config['environment'] === 'sandbox') 
                ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token' 
                : 'https://api-m.paypal.com/v1/oauth2/token'
        );
        
        $start_time = microtime(true);
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Basic ' . base64_encode($config['client_id'] . ':' . $config['client_secret']),
                'Content-Type'  => 'application/x-www-form-urlencoded',
            ),
            'body'        => 'grant_type=client_credentials'
        );
        
        // Make the request
        $response = wp_remote_post($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 200) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        // Get body and decode JSON
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        // Check if access token exists
        if (isset($data['access_token'])) {
            $result['success'] = true;
            $result['message'] = __('Successfully authenticated with PayPal API', 'document-viewer-plugin');
            $result['token'] = $data['access_token'];
            $result['expires_in'] = isset($data['expires_in']) ? $data['expires_in'] : 3600;
        }
        
        return $result;
    }
    
    /**
     * Test PayPal orders endpoint
     * 
     * @param array $config PayPal configuration
     * @return array Test result
     */
    private function test_endpoint_paypal_orders($config) {
        $result = array(
            'name' => __('Orders Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to access PayPal orders API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => ($config['environment'] === 'sandbox') 
                ? 'https://api-m.sandbox.paypal.com/v2/checkout/orders' 
                : 'https://api-m.paypal.com/v2/checkout/orders'
        );
        
        // First, get an access token
        $auth_result = $this->test_endpoint_paypal_auth($config);
        
        if (!$auth_result['success']) {
            $result['message'] = __('Cannot test orders endpoint without authentication', 'document-viewer-plugin');
            return $result;
        }
        
        $start_time = microtime(true);
        
        // Create a test order
        $order_data = array(
            'intent' => 'CAPTURE',
            'purchase_units' => array(
                array(
                    'amount' => array(
                        'currency_code' => 'USD',
                        'value' => '0.01'
                    ),
                    'description' => 'Diagnostic test from Financial Advisor Plugin'
                )
            ),
            'application_context' => array(
                'return_url' => 'https://example.com/return',
                'cancel_url' => 'https://example.com/cancel'
            )
        );
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $auth_result['token'],
                'Content-Type'  => 'application/json',
                'Prefer'        => 'return=representation'
            ),
            'body'        => json_encode($order_data)
        );
        
        // Make the request
        $response = wp_remote_post($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 201) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        // Get body and decode JSON
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        // Check if order ID exists
        if (isset($data['id'])) {
            $result['success'] = true;
            $result['message'] = __('Successfully created test order via PayPal API', 'document-viewer-plugin');
            $result['order_id'] = $data['id'];
        }
        
        return $result;
    }
    
    /**
     * Test PayPal webhooks endpoint
     * 
     * @param array $config PayPal configuration
     * @return array Test result
     */
    private function test_endpoint_paypal_webhooks($config) {
        $result = array(
            'name' => __('Webhooks Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to access PayPal webhooks API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => ($config['environment'] === 'sandbox') 
                ? 'https://api-m.sandbox.paypal.com/v1/notifications/webhooks' 
                : 'https://api-m.paypal.com/v1/notifications/webhooks'
        );
        
        // First, get an access token
        $auth_result = $this->test_endpoint_paypal_auth($config);
        
        if (!$auth_result['success']) {
            $result['message'] = __('Cannot test webhooks endpoint without authentication', 'document-viewer-plugin');
            return $result;
        }
        
        $start_time = microtime(true);
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $auth_result['token'],
                'Content-Type'  => 'application/json'
            )
        );
        
        // Make the request
        $response = wp_remote_get($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 200) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        $result['success'] = true;
        $result['message'] = __('Successfully accessed PayPal webhooks API', 'document-viewer-plugin');
        
        return $result;
    }
    
    /**
     * Test Stripe authentication endpoint
     * 
     * @param array $config Stripe configuration
     * @return array Test result
     */
    private function test_endpoint_stripe_auth($config) {
        $result = array(
            'name' => __('Authentication Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to authenticate with Stripe API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => 'https://api.stripe.com/v1/balance'
        );
        
        $start_time = microtime(true);
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded'
            )
        );
        
        // Make the request
        $response = wp_remote_get($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 200) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        // Get body and decode JSON
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        // Check if available array exists (indicates a valid response)
        if (isset($data['available'])) {
            $result['success'] = true;
            $result['message'] = __('Successfully authenticated with Stripe API', 'document-viewer-plugin');
        }
        
        return $result;
    }
    
    /**
     * Test Stripe payment intents endpoint
     * 
     * @param array $config Stripe configuration
     * @return array Test result
     */
    private function test_endpoint_stripe_payment_intents($config) {
        $result = array(
            'name' => __('Payment Intents Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to access Stripe payment intents API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => 'https://api.stripe.com/v1/payment_intents'
        );
        
        $start_time = microtime(true);
        
        // Create a test payment intent
        $intent_data = array(
            'amount' => 100, // $1.00 in cents
            'currency' => 'usd',
            'payment_method_types[]' => 'card',
            'description' => 'Diagnostic test from Financial Advisor Plugin'
        );
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded'
            ),
            'body'        => $intent_data
        );
        
        // Make the request
        $response = wp_remote_post($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 200) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        // Get body and decode JSON
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        // Check if payment intent ID exists
        if (isset($data['id'])) {
            $result['success'] = true;
            $result['message'] = __('Successfully created test payment intent via Stripe API', 'document-viewer-plugin');
            $result['payment_intent_id'] = $data['id'];
        }
        
        return $result;
    }
    
    /**
     * Test Stripe webhooks endpoint
     * 
     * @param array $config Stripe configuration
     * @return array Test result
     */
    private function test_endpoint_stripe_webhooks($config) {
        $result = array(
            'name' => __('Webhooks Endpoint', 'document-viewer-plugin'),
            'success' => false,
            'message' => __('Failed to access Stripe webhooks API', 'document-viewer-plugin'),
            'response_time' => 0,
            'endpoint' => 'https://api.stripe.com/v1/webhook_endpoints'
        );
        
        $start_time = microtime(true);
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 30,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded'
            )
        );
        
        // Make the request
        $response = wp_remote_get($result['endpoint'], $args);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000);
        
        // Check for errors
        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }
        
        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        $result['response_code'] = $response_code;
        
        if ($response_code !== 200) {
            $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
            return $result;
        }
        
        $result['success'] = true;
        $result['message'] = __('Successfully accessed Stripe webhooks API', 'document-viewer-plugin');
        
        return $result;
    }
}
