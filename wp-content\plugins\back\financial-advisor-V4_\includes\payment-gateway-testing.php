<?php
/**
 * Payment Gateway Testing Bootstrap
 *
 * This file loads all payment gateway testing functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Load test class
require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');

// Load diagnostic class
require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');

// Load AJAX handlers
require_once(dirname(__FILE__) . '/payment-gateway-logs.php');
require_once(dirname(__FILE__) . '/payment-gateway-diagnostics.php');

/**
 * Initialize Payment Gateway Testing
 */
function initialize_payment_gateway_testing() {
    add_action('wp_enqueue_scripts', 'enqueue_payment_gateway_testing_scripts');
    add_action('admin_enqueue_scripts', 'enqueue_payment_gateway_testing_scripts');
}

/**
 * Enqueue testing scripts and styles
 */
function enqueue_payment_gateway_testing_scripts() {
    // Only load on admin pages
    if (!is_admin()) {
        return;
    }
    
    // Enqueue custom CSS for test results
    wp_enqueue_style(
        'payment-gateway-testing-styles',
        plugin_dir_url(__FILE__) . '../assets/css/payment-gateway-admin.css',
        array(),
        '1.0.0'
    );
    
    // Enqueue custom JS for test interactions
    wp_enqueue_script(
        'payment-gateway-testing-scripts',
        plugin_dir_url(__FILE__) . '../assets/js/payment-gateway-admin.js',
        array('jquery'),
        '1.0.0',
        true
    );
    
    // Add localization data
    wp_localize_script(
        'payment-gateway-testing-scripts',
        'paymentGatewayTestingData',
        array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payment_gateway_testing'),
            'messages' => array(
                'test_running' => __('Running test, please wait...', 'document-viewer-plugin'),
                'test_success' => __('Test completed successfully', 'document-viewer-plugin'),
                'test_failed' => __('Test failed', 'document-viewer-plugin')
            )
        )
    );
}

// Initialize testing functionality
initialize_payment_gateway_testing();
