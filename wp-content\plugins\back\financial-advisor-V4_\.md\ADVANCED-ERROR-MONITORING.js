/**
 * 🚨 ADVANCED ERROR MONITORING & DEBUGGING TOOLKIT
 * 
 * Sistema completo per il monitoraggio, debugging e risoluzione automatica 
 * degli errori console nel widget di gestione abbonati.
 */

// ================================
// 🔧 CORE ERROR MONITORING SYSTEM
// ================================

const SubscriberWidgetMonitor = {
    // Configurazione
    config: {
        maxErrors: 100,
        performanceThreshold: 3000, // ms
        retryAttempts: 3,
        retryDelay: 1000, // ms
        debugMode: false,
        autoFix: true
    },

    // Storage errori
    errorLog: [],
    performanceMetrics: [],
    
    // Contatori
    stats: {
        totalErrors: 0,
        fixedErrors: 0,
        ajax: { success: 0, error: 0, total: 0 },
        dom: { queries: 0, notFound: 0 },
        performance: { slow: 0, fast: 0 }
    },

    /**
     * Inizializza il sistema di monitoraggio
     */
    init() {
        this.setupGlobalErrorHandlers();
        this.setupPerformanceMonitoring();
        this.setupAjaxInterceptor();
        this.setupDOMMonitoring();
        this.createDebugInterface();
        
        console.info('🔧 Subscriber Widget Monitor initialized');
        
        // Auto-start se in modalità debug
        if (this.isDebugMode()) {
            this.config.debugMode = true;
            this.startRealTimeMonitoring();
        }
    },

    /**
     * Verifica se siamo in modalità debug
     */
    isDebugMode() {
        return window.location.hostname === 'localhost' || 
               window.location.search.includes('debug=true') ||
               localStorage.getItem('subscriberWidgetDebug') === 'true';
    },

    /**
     * Setup gestori errori globali
     */
    setupGlobalErrorHandlers() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            this.trackError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
        });

        // Promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.trackError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                error: event.reason
            });
        });

        // Console errors override
        const originalError = console.error;
        console.error = (...args) => {
            this.trackError({
                type: 'console',
                message: args.join(' '),
                args: args
            });
            originalError.apply(console, args);
        };
    },

    /**
     * Setup monitoraggio performance
     */
    setupPerformanceMonitoring() {
        // Monitor navigation timing
        if (performance.timing) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            
            this.trackPerformance({
                type: 'page_load',
                duration: loadTime,
                timestamp: Date.now()
            });
        }

        // Monitor long tasks (se supportato)
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                        if (entry.duration > 50) { // Tasks > 50ms
                            this.trackPerformance({
                                type: 'long_task',
                                duration: entry.duration,
                                timestamp: Date.now()
                            });
                        }
                    });
                });
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                console.warn('PerformanceObserver not fully supported');
            }
        }
    },

    /**
     * Setup intercettazione AJAX
     */
    setupAjaxInterceptor() {
        const self = this;
        
        if (window.jQuery) {
            // Intercetta chiamate jQuery AJAX
            $(document).ajaxStart(function() {
                self.stats.ajax.total++;
            });

            $(document).ajaxSuccess(function(event, xhr, settings) {
                self.stats.ajax.success++;
                self.trackPerformance({
                    type: 'ajax_success',
                    url: settings.url,
                    duration: Date.now() - (settings._startTime || 0)
                });
            });

            $(document).ajaxError(function(event, xhr, settings, error) {
                self.stats.ajax.error++;
                self.trackError({
                    type: 'ajax',
                    message: `AJAX Error: ${error}`,
                    url: settings.url,
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText
                });

                // Auto-fix per errori comuni
                if (self.config.autoFix) {
                    self.attemptAjaxAutoFix(xhr, settings, error);
                }
            });

            // Hook per timing AJAX
            const originalAjax = $.ajax;
            $.ajax = function(settings) {
                settings._startTime = Date.now();
                return originalAjax.call(this, settings);
            };
        }
    },

    /**
     * Setup monitoraggio DOM
     */
    setupDOMMonitoring() {
        const self = this;
        
        // Override jQuery selettori per monitoraggio
        if (window.jQuery) {
            const originalFind = $.fn.find;
            $.fn.find = function(selector) {
                self.stats.dom.queries++;
                const result = originalFind.call(this, selector);
                
                if (result.length === 0) {
                    self.stats.dom.notFound++;
                    
                    // Log solo selettori widget-related
                    if (selector.includes('credit') || selector.includes('subscriber') || selector.includes('recharge')) {
                        self.trackError({
                            type: 'dom_not_found',
                            message: `DOM element not found: ${selector}`,
                            selector: selector,
                            context: this[0]?.tagName || 'unknown'
                        });
                    }
                }
                
                return result;
            };
        }
    },

    /**
     * Traccia errore
     */
    trackError(errorInfo) {
        const error = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            ...errorInfo,
            context: this.getContext(),
            fixed: false
        };

        this.errorLog.unshift(error);
        this.errorLog = this.errorLog.slice(0, this.config.maxErrors);
        this.stats.totalErrors++;

        if (this.config.debugMode) {
            console.group('🚨 Widget Error Tracked');
            console.error('Error Details:', error);
            console.groupEnd();
        }

        // Tentativo auto-fix
        if (this.config.autoFix) {
            this.attemptAutoFix(error);
        }

        // Aggiorna UI debug
        this.updateDebugInterface();
    },

    /**
     * Traccia metrica performance
     */
    trackPerformance(metric) {
        const perfData = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            ...metric
        };

        this.performanceMetrics.unshift(perfData);
        this.performanceMetrics = this.performanceMetrics.slice(0, this.config.maxErrors);

        // Classifica performance
        if (metric.duration > this.config.performanceThreshold) {
            this.stats.performance.slow++;
            
            if (this.config.debugMode) {
                console.warn(`🐌 Slow operation detected: ${metric.type} took ${metric.duration}ms`);
            }
        } else {
            this.stats.performance.fast++;
        }
    },

    /**
     * Tentativo auto-fix generico
     */
    attemptAutoFix(error) {
        const fixes = {
            'dom_not_found': this.fixDOMNotFound,
            'javascript': this.fixJavaScriptError,
            'ajax': this.fixAjaxError,
            'console': this.fixConsoleError
        };

        const fixFunction = fixes[error.type];
        if (fixFunction) {
            const success = fixFunction.call(this, error);
            if (success) {
                error.fixed = true;
                this.stats.fixedErrors++;
                
                if (this.config.debugMode) {
                    console.info(`✅ Auto-fixed error: ${error.id}`);
                }
            }
        }
    },

    /**
     * Fix per elementi DOM non trovati
     */
    fixDOMNotFound(error) {
        const selector = error.selector;
        
        // Strategie di fix per selettori comuni
        const strategies = [
            // Retry con delay
            () => {
                setTimeout(() => {
                    const element = $(selector);
                    if (element.length > 0) {
                        console.info(`✅ DOM element found after retry: ${selector}`);
                        return true;
                    }
                }, 500);
                return false;
            },
            
            // Fallback selettori alternativi
            () => {
                const fallbacks = {
                    '.credit-value': ['.credit', '[data-credit]', '.stats-value'],
                    '.recharge-button': ['.btn-recharge', '[data-action="recharge"]'],
                    '.subscriber-form': ['.subscriber', '.management-form']
                };
                
                const alternatives = fallbacks[selector];
                if (alternatives) {
                    for (const alt of alternatives) {
                        if ($(alt).length > 0) {
                            console.info(`✅ Found alternative element: ${alt} for ${selector}`);
                            return true;
                        }
                    }
                }
                return false;
            }
        ];

        return strategies.some(strategy => strategy());
    },

    /**
     * Fix per errori JavaScript
     */
    fixJavaScriptError(error) {
        const message = error.message.toLowerCase();
        
        // Fix per errori comuni
        if (message.includes('cannot read properties of undefined')) {
            // Implementa safe property access
            console.warn('Implementing safe property access...');
            return true;
        }
        
        if (message.includes('is not a function')) {
            // Verifica se funzione è disponibile
            const funcName = error.message.match(/(\w+) is not a function/)?.[1];
            if (funcName && window.subscriberManagementWidget) {
                const func = window.subscriberManagementWidget[funcName];
                if (typeof func === 'function') {
                    console.info(`✅ Function ${funcName} is available in widget object`);
                    return true;
                }
            }
        }
        
        return false;
    },

    /**
     * Fix per errori AJAX
     */
    fixAjaxError(error) {
        if (error.status === 0) {
            // Problema di rete - retry
            console.warn('Network issue detected, will retry...');
            return true;
        }
        
        if (error.status === 404) {
            // URL non trovato - log per sviluppatore
            console.error(`Endpoint not found: ${error.url}`);
            return false;
        }
        
        return false;
    },

    /**
     * Auto-fix specifico per errori AJAX
     */
    attemptAjaxAutoFix(xhr, settings, error) {
        if (xhr.status === 0 && this.config.retryAttempts > 0) {
            // Retry per problemi di rete
            setTimeout(() => {
                console.info(`Retrying AJAX request: ${settings.url}`);
                $.ajax(settings);
            }, this.config.retryDelay);
        }
    },

    /**
     * Genera ID unico
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * Ottieni contesto corrente
     */
    getContext() {
        return {
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: Date.now(),
            jquery: window.jQuery ? $.fn.jquery : null,
            widgetLoaded: !!window.subscriberManagementWidget
        };
    },

    /**
     * Crea interfaccia debug
     */
    createDebugInterface() {
        if (document.getElementById('widget-debug-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'widget-debug-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            max-height: 500px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 999999;
            overflow: hidden;
            display: none;
            border: 2px solid #00ff88;
        `;

        panel.innerHTML = `
            <div style="background: rgba(0,0,0,0.2); padding: 15px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <strong style="color: #00ff88;">🔧 Widget Monitor</strong>
                    <div>
                        <button onclick="SubscriberWidgetMonitor.exportDebugData()" 
                                style="background: #00ff88; color: black; border: none; padding: 3px 8px; border-radius: 3px; margin-right: 5px; cursor: pointer;">📊</button>
                        <button onclick="SubscriberWidgetMonitor.clearData()" 
                                style="background: #ff4757; color: white; border: none; padding: 3px 8px; border-radius: 3px; margin-right: 5px; cursor: pointer;">🗑️</button>
                        <button onclick="SubscriberWidgetMonitor.hideDebugInterface()" 
                                style="background: none; color: white; border: none; padding: 3px 8px; cursor: pointer;">×</button>
                    </div>
                </div>
            </div>
            <div id="debug-content" style="padding: 15px; max-height: 400px; overflow-y: auto;"></div>
        `;

        document.body.appendChild(panel);
    },

    /**
     * Mostra interfaccia debug
     */
    showDebugInterface() {
        this.createDebugInterface();
        document.getElementById('widget-debug-panel').style.display = 'block';
        this.updateDebugInterface();
        this.startRealTimeMonitoring();
    },

    /**
     * Nascondi interfaccia debug
     */
    hideDebugInterface() {
        document.getElementById('widget-debug-panel').style.display = 'none';
        this.stopRealTimeMonitoring();
    },

    /**
     * Aggiorna interfaccia debug
     */
    updateDebugInterface() {
        const content = document.getElementById('debug-content');
        if (!content) return;

        const recentErrors = this.errorLog.slice(0, 5);
        const recentPerf = this.performanceMetrics.slice(0, 3);

        content.innerHTML = `
            <div style="margin-bottom: 15px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                    <div style="background: rgba(0,0,0,0.2); padding: 8px; border-radius: 5px; text-align: center;">
                        <div style="color: #ff4757; font-size: 18px; font-weight: bold;">${this.stats.totalErrors}</div>
                        <div style="font-size: 10px;">Total Errors</div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 8px; border-radius: 5px; text-align: center;">
                        <div style="color: #00ff88; font-size: 18px; font-weight: bold;">${this.stats.fixedErrors}</div>
                        <div style="font-size: 10px;">Auto-Fixed</div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; margin-bottom: 10px;">
                    <div style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; text-align: center;">
                        <div style="color: #00ff88; font-weight: bold;">${this.stats.ajax.success}</div>
                        <div style="font-size: 9px;">AJAX ✓</div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; text-align: center;">
                        <div style="color: #ff4757; font-weight: bold;">${this.stats.ajax.error}</div>
                        <div style="font-size: 9px;">AJAX ✗</div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 5px; border-radius: 3px; text-align: center;">
                        <div style="color: #ffa502; font-weight: bold;">${this.stats.dom.notFound}</div>
                        <div style="font-size: 9px;">DOM ✗</div>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 15px;">
                <strong style="color: #ff4757;">🚨 Recent Errors:</strong>
                <div style="max-height: 150px; overflow-y: auto; margin-top: 5px;">
                    ${recentErrors.length === 0 ? 
                        '<div style="color: #00ff88; text-align: center; padding: 10px;">No errors! 🎉</div>' :
                        recentErrors.map(error => `
                            <div style="background: rgba(255,71,87,0.1); border-left: 3px solid ${error.fixed ? '#00ff88' : '#ff4757'}; padding: 8px; margin: 5px 0; border-radius: 3px;">
                                <div style="font-weight: bold; color: ${error.fixed ? '#00ff88' : '#ff4757'};">
                                    ${error.fixed ? '✅' : '❌'} ${error.type.toUpperCase()}
                                </div>
                                <div style="font-size: 10px; margin-top: 3px; word-break: break-all;">
                                    ${error.message.substring(0, 80)}${error.message.length > 80 ? '...' : ''}
                                </div>
                                <div style="font-size: 9px; color: #ccc; margin-top: 2px;">
                                    ${new Date(error.timestamp).toLocaleTimeString()}
                                </div>
                            </div>
                        `).join('')
                    }
                </div>
            </div>

            <div style="margin-bottom: 10px;">
                <strong style="color: #ffa502;">⚡ Performance:</strong>
                <div style="margin-top: 5px;">
                    ${recentPerf.map(perf => `
                        <div style="background: rgba(255,165,2,0.1); padding: 5px; margin: 3px 0; border-radius: 3px;">
                            <div style="font-size: 10px;">
                                <span style="color: #ffa502;">${perf.type}</span>: 
                                <span style="color: ${perf.duration > this.config.performanceThreshold ? '#ff4757' : '#00ff88'};">
                                    ${perf.duration}ms
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div style="text-align: center; margin-top: 15px;">
                <button onclick="SubscriberWidgetMonitor.runDiagnostics()" 
                        style="background: #00ff88; color: black; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                    🔍 Run Diagnostics
                </button>
            </div>
        `;
    },

    /**
     * Avvia monitoraggio real-time
     */
    startRealTimeMonitoring() {
        if (this.monitoringInterval) return;
        
        this.monitoringInterval = setInterval(() => {
            this.updateDebugInterface();
        }, 2000);
    },

    /**
     * Ferma monitoraggio real-time
     */
    stopRealTimeMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    },

    /**
     * Esegui diagnostica completa
     */
    runDiagnostics() {
        console.group('🔍 Widget Diagnostics Report');
        
        // Test jQuery
        console.log('jQuery:', window.jQuery ? `✅ ${$.fn.jquery}` : '❌ Not loaded');
        
        // Test Widget Object
        console.log('Widget Object:', window.subscriberManagementWidget ? '✅ Loaded' : '❌ Not loaded');
        
        // Test DOM Elements
        const criticalElements = [
            '.credit-value',
            '.recharge-button',
            '.subscriber-form',
            '.stats-card'
        ];
        
        console.log('DOM Elements:');
        criticalElements.forEach(selector => {
            const found = $(selector).length;
            console.log(`  ${selector}: ${found > 0 ? '✅' : '❌'} (${found} found)`);
        });
        
        // Test AJAX Endpoint
        console.log('Testing AJAX endpoint...');
        if (window.subscriberManagementAjax) {
            $.ajax({
                url: window.subscriberManagementAjax.ajax_url,
                method: 'POST',
                data: { action: 'test_connection' },
                success: () => console.log('AJAX Endpoint: ✅ Responding'),
                error: () => console.log('AJAX Endpoint: ❌ Not responding')
            });
        }
        
        // Performance Report
        console.log('Performance Report:', this.generatePerformanceReport());
        
        console.groupEnd();
        
        alert('Diagnostics completed! Check console for detailed report.');
    },

    /**
     * Genera report performance
     */
    generatePerformanceReport() {
        const metrics = this.performanceMetrics;
        if (metrics.length === 0) return 'No data';
        
        const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
        const slowOperations = metrics.filter(m => m.duration > this.config.performanceThreshold);
        
        return {
            averageDuration: Math.round(avgDuration),
            slowOperations: slowOperations.length,
            totalOperations: metrics.length,
            performanceScore: slowOperations.length === 0 ? 'Excellent' : 
                             slowOperations.length < 3 ? 'Good' : 'Needs Improvement'
        };
    },

    /**
     * Esporta dati debug
     */
    exportDebugData() {
        const data = {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            errors: this.errorLog,
            performance: this.performanceMetrics,
            performanceReport: this.generatePerformanceReport(),
            context: this.getContext()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `widget-debug-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        console.log('Debug data exported:', data);
    },

    /**
     * Pulisci tutti i dati
     */
    clearData() {
        this.errorLog = [];
        this.performanceMetrics = [];
        this.stats = {
            totalErrors: 0,
            fixedErrors: 0,
            ajax: { success: 0, error: 0, total: 0 },
            dom: { queries: 0, notFound: 0 },
            performance: { slow: 0, fast: 0 }
        };
        
        this.updateDebugInterface();
        console.log('🗑️ All debug data cleared');
    }
};

// ================================
// 🚀 AUTO-INITIALIZATION
// ================================

// Inizializza automaticamente quando DOM è pronto
$(document).ready(function() {
    SubscriberWidgetMonitor.init();
    
    // Hotkey per debug panel: Ctrl+Shift+M
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'M') {
            e.preventDefault();
            const panel = document.getElementById('widget-debug-panel');
            if (panel && panel.style.display !== 'none') {
                SubscriberWidgetMonitor.hideDebugInterface();
            } else {
                SubscriberWidgetMonitor.showDebugInterface();
            }
        }
    });
});

// Esposizione globale per accesso console
window.SubscriberWidgetMonitor = SubscriberWidgetMonitor;

// ================================
// 🎯 UTILITY FUNCTIONS
// ================================

/**
 * Test rapido dello stato del widget
 */
window.testWidget = function() {
    console.group('🧪 Quick Widget Test');
    
    const tests = [
        {
            name: 'jQuery Available',
            test: () => !!window.jQuery,
            result: null
        },
        {
            name: 'Widget Object Loaded',
            test: () => !!window.subscriberManagementWidget,
            result: null
        },
        {
            name: 'Credit Elements Present',
            test: () => $('.credit-value').length > 0,
            result: null
        },
        {
            name: 'AJAX Config Available',
            test: () => !!window.subscriberManagementAjax,
            result: null
        },
        {
            name: 'No Console Errors',
            test: () => SubscriberWidgetMonitor.errorLog.length === 0,
            result: null
        }
    ];
    
    tests.forEach(test => {
        test.result = test.test();
        console.log(`${test.result ? '✅' : '❌'} ${test.name}`);
    });
    
    const passed = tests.filter(t => t.result).length;
    const total = tests.length;
    
    console.log(`\n📊 Overall Score: ${passed}/${total} (${Math.round(passed/total*100)}%)`);
    console.groupEnd();
    
    return { passed, total, score: Math.round(passed/total*100) };
};

console.log('🔧 Advanced Error Monitoring Toolkit loaded! Use Ctrl+Shift+M to open debug panel or testWidget() for quick test.');
