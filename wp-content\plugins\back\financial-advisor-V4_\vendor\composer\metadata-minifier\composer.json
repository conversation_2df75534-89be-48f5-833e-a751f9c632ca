{"name": "composer/metadata-minifier", "description": "Small utility library that handles metadata minification and expansion.", "type": "library", "license": "MIT", "keywords": ["compression", "composer"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "support": {"issues": "https://github.com/composer/metadata-minifier/issues"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.55", "composer/composer": "^2"}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\Test\\MetadataMinifier\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "vendor/bin/phpstan analyse"}}