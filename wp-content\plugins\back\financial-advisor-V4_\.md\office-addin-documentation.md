# Financial Advisor Excel Add-in Documentation

## Overview

The Financial Advisor Excel Add-in allows users to analyze financial data directly from Excel spreadsheets using the same powerful AI analysis capabilities available in the WordPress plugin. The add-in connects to your WordPress site and uses the API settings configured in the plugin's settings page.

## Features

- Extract text from selected cells in Excel
- Choose from predefined queries or create custom analysis questions
- Analyze financial data using OpenRouter AI models
- View analysis results directly in Excel
- Seamless integration with your WordPress site's API settings

## Installation

### Prerequisites

- Microsoft Excel 2016 or later
- A WordPress site with the Financial Advisor Plugin installed and configured
- API keys set up in the Primary API settings tab

### Installation Steps

1. **Download the Add-in Manifest**

   - Go to your WordPress admin panel
   - Navigate to Financial Advisor > Settings
   - Click on the "Office Add-in" tab
   - Click the "Download Add-in Manifest" button

2. **Install in Excel**

   **For Excel Desktop:**
   - Open Excel
   - Go to the Insert tab
   - Click on "Get Add-ins" or "Office Add-ins"
   - Select "Upload My Add-in"
   - Browse to the downloaded manifest file and select it
   - Click "Install"

   **For Excel Online:**
   - Open Excel Online
   - Go to the Insert tab
   - Click on "Office Add-ins"
   - Select "Upload My Add-in"
   - Browse to the downloaded manifest file and select it
   - Click "Install"

## Configuration

The Excel add-in automatically uses the API settings configured in the WordPress plugin:

1. **API Settings**
   - The add-in uses the OpenRouter API Key, Endpoint, and Model from the "Primary API" tab
   - No additional configuration is needed in Excel

2. **Predefined Queries**
   - The add-in automatically loads the predefined queries from your WordPress site
   - Any changes made to predefined queries in WordPress will be reflected in the Excel add-in

## Using the Add-in

1. **Open the Add-in**
   - In Excel, go to the Home tab
   - Click on the "Financial Advisor" button in the ribbon

2. **Extract Text**
   - Select the cells containing the financial data you want to analyze
   - Click the "Extract Selected Cells" button
   - The text will be displayed in the "Extracted Text" section

3. **Analyze Data**
   - Select a predefined query from the dropdown menu, or
   - Enter your own custom analysis question
   - Click the "Analyze" button
   - The analysis results will appear in the "Results" section

4. **Debugging**
   - The "Debug Information" section shows the connection status and selected model
   - Click "Test API Connection" to verify connectivity with your WordPress site

## Customizing the Add-in UI

You can customize the HTML content of the Excel add-in through the WordPress admin panel:

1. Go to Financial Advisor > Settings
2. Click on the "Office Add-in" tab
3. Edit the HTML content in the editor
4. Click "Save Changes"

The changes will be reflected immediately in the Excel add-in.

## Troubleshooting

### Connection Issues

- Ensure your WordPress site is accessible from the internet
- Verify that the API keys are correctly configured in the WordPress plugin
- Check that the selected model is available in your OpenRouter account

### Add-in Not Loading

- Make sure the manifest file is correctly installed
- Check if your Excel version supports Office Add-ins
- Try reinstalling the add-in

### Analysis Not Working

- Verify that the extracted text contains meaningful financial data
- Ensure your OpenRouter API key has sufficient credits
- Try using a different predefined query or model

## Support

For additional support, please contact your WordPress site administrator or visit the Financial Advisor Plugin documentation.
