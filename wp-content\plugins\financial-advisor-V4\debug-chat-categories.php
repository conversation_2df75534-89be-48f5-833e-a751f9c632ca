<?php
/**
 * Debug Script per Chat Widget Categories
 * 
 * Verifica perché le categorie non vengono caricate nel widget chat
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    // Se non siamo in WordPress, includiamo wp-config.php
    $wp_config_path = dirname(dirname(dirname(dirname(dirname(__FILE__))))) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once $wp_config_path;
    } else {
        die('WordPress non trovato. Esegui questo script dal browser.');
    }
}

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Chat Widget Categories</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005177;
        }
        #ajax-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            display: none;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Chat Widget Categories</h1>
        
        <?php
        // Test 1: Verifica Financial Academy Manager
        echo '<div class="test-section">';
        echo '<h2>1. Verifica Financial Academy Manager</h2>';
        
        if (class_exists('Financial_Academy_Manager')) {
            echo '<div class="success">✅ Classe Financial_Academy_Manager trovata</div>';
            
            $manager = new Financial_Academy_Manager();
            if ($manager) {
                echo '<div class="success">✅ Istanza Financial_Academy_Manager creata</div>';
                
                // Test metodo get_category_stats
                if (method_exists($manager, 'get_category_stats')) {
                    echo '<div class="success">✅ Metodo get_category_stats() disponibile</div>';
                    
                    $stats = $manager->get_category_stats();
                    echo '<p><strong>Categorie trovate:</strong> ' . count($stats) . '</p>';
                    
                    if (!empty($stats)) {
                        echo '<pre>' . esc_html(print_r($stats, true)) . '</pre>';
                    } else {
                        echo '<div class="warning">⚠ Nessuna categoria con domande trovata</div>';
                    }
                } else {
                    echo '<div class="error">❌ Metodo get_category_stats() non trovato</div>';
                }
            } else {
                echo '<div class="error">❌ Impossibile creare istanza Financial_Academy_Manager</div>';
            }
        } else {
            echo '<div class="error">❌ Classe Financial_Academy_Manager non trovata</div>';
        }
        echo '</div>';
        
        // Test 2: Verifica Database
        echo '<div class="test-section">';
        echo '<h2>2. Verifica Database</h2>';
        
        global $wpdb;
        $questions_table = $wpdb->prefix . 'financial_academy_questions';
        
        $table_exists = $wpdb->get_var($wpdb->prepare(
            "SHOW TABLES LIKE %s", 
            $questions_table
        ));
        
        if ($table_exists) {
            echo '<div class="success">✅ Tabella ' . esc_html($questions_table) . ' esiste</div>';
            
            $total_questions = $wpdb->get_var("SELECT COUNT(*) FROM {$questions_table}");
            echo '<p><strong>Totale domande:</strong> ' . intval($total_questions) . '</p>';
            
            $active_questions = $wpdb->get_var("SELECT COUNT(*) FROM {$questions_table} WHERE active = 1");
            echo '<p><strong>Domande attive:</strong> ' . intval($active_questions) . '</p>';
            
            // Mostra statistiche per categoria
            $category_stats = $wpdb->get_results(
                "SELECT category, COUNT(*) as count 
                 FROM {$questions_table} 
                 WHERE active = 1 
                 GROUP BY category 
                 ORDER BY count DESC",
                ARRAY_A
            );
            
            if (!empty($category_stats)) {
                echo '<h4>Statistiche per categoria:</h4>';
                echo '<pre>' . esc_html(print_r($category_stats, true)) . '</pre>';
            } else {
                echo '<div class="warning">⚠ Nessuna domanda attiva trovata</div>';
            }
            
        } else {
            echo '<div class="error">❌ Tabella ' . esc_html($questions_table) . ' non esiste</div>';
        }
        echo '</div>';
        
        // Test 3: Verifica AJAX Handlers
        echo '<div class="test-section">';
        echo '<h2>3. Verifica AJAX Handlers</h2>';
        
        // Verifica se gli hook AJAX sono registrati
        global $wp_filter;
        
        $get_category_stats_registered = false;
        $get_academy_questions_registered = false;
        
        if (isset($wp_filter['wp_ajax_get_category_stats'])) {
            $get_category_stats_registered = true;
            echo '<div class="success">✅ Hook wp_ajax_get_category_stats registrato</div>';
        } else {
            echo '<div class="error">❌ Hook wp_ajax_get_category_stats NON registrato</div>';
        }
        
        if (isset($wp_filter['wp_ajax_nopriv_get_category_stats'])) {
            echo '<div class="success">✅ Hook wp_ajax_nopriv_get_category_stats registrato</div>';
        } else {
            echo '<div class="error">❌ Hook wp_ajax_nopriv_get_category_stats NON registrato</div>';
        }
        
        if (isset($wp_filter['wp_ajax_get_academy_questions'])) {
            $get_academy_questions_registered = true;
            echo '<div class="success">✅ Hook wp_ajax_get_academy_questions registrato</div>';
        } else {
            echo '<div class="error">❌ Hook wp_ajax_get_academy_questions NON registrato</div>';
        }
        
        if (isset($wp_filter['wp_ajax_nopriv_get_academy_questions'])) {
            echo '<div class="success">✅ Hook wp_ajax_nopriv_get_academy_questions registrato</div>';
        } else {
            echo '<div class="error">❌ Hook wp_ajax_nopriv_get_academy_questions NON registrato</div>';
        }
        echo '</div>';
        
        // Test 4: Verifica Script Enqueue
        echo '<div class="test-section">';
        echo '<h2>4. Verifica Script Enqueue</h2>';
        
        // Simula l'enqueue degli script del chat widget
        if (class_exists('Chat_Model_Widget')) {
            echo '<div class="success">✅ Classe Chat_Model_Widget trovata</div>';
            
            $widget = new Chat_Model_Widget();
            if (method_exists($widget, 'enqueue_scripts')) {
                echo '<div class="success">✅ Metodo enqueue_scripts() disponibile</div>';
            } else {
                echo '<div class="error">❌ Metodo enqueue_scripts() non trovato</div>';
            }
        } else {
            echo '<div class="error">❌ Classe Chat_Model_Widget non trovata</div>';
        }
        echo '</div>';
        
        // Test 5: Test AJAX Live
        echo '<div class="test-section">';
        echo '<h2>5. Test AJAX Live</h2>';
        echo '<p>Clicca i pulsanti per testare le chiamate AJAX in tempo reale:</p>';
        
        echo '<button class="test-button" onclick="testCategoryStats()">Test get_category_stats</button>';
        echo '<button class="test-button" onclick="testAcademyQuestions()">Test get_academy_questions</button>';
        echo '<button class="test-button" onclick="testWithNonce()">Test con Nonce</button>';
        
        echo '<div id="ajax-results"></div>';
        echo '</div>';
        ?>
        
        <div class="test-section info">
            <h2>📋 Possibili Cause del Problema</h2>
            <ul>
                <li><strong>Script non caricato:</strong> Il file chat-model-widget.js potrebbe non essere enqueued correttamente</li>
                <li><strong>Parametri mancanti:</strong> chatWidgetParams potrebbe non essere localizzato</li>
                <li><strong>AJAX URL errato:</strong> L'URL AJAX potrebbe essere sbagliato</li>
                <li><strong>Nonce mancante:</strong> Il nonce potrebbe non essere passato correttamente</li>
                <li><strong>Database vuoto:</strong> Non ci sono domande attive nel database</li>
                <li><strong>Hook non registrati:</strong> Gli handler AJAX potrebbero non essere registrati</li>
                <li><strong>Errori JavaScript:</strong> Controlla la console del browser per errori</li>
            </ul>
        </div>
    </div>

    <script>
        function showResults(title, data) {
            const resultsDiv = document.getElementById('ajax-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<h3>' + title + '</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
        
        function testCategoryStats() {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'get_category_stats'
                },
                success: function(response) {
                    showResults('Test get_category_stats - SUCCESS', response);
                },
                error: function(xhr, status, error) {
                    showResults('Test get_category_stats - ERROR', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                }
            });
        }
        
        function testAcademyQuestions() {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'get_academy_questions',
                    category: 'general'
                },
                success: function(response) {
                    showResults('Test get_academy_questions - SUCCESS', response);
                },
                error: function(xhr, status, error) {
                    showResults('Test get_academy_questions - ERROR', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                }
            });
        }
        
        function testWithNonce() {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'get_category_stats',
                    nonce: '<?php echo wp_create_nonce('document_viewer_nonce'); ?>'
                },
                success: function(response) {
                    showResults('Test con Nonce - SUCCESS', response);
                },
                error: function(xhr, status, error) {
                    showResults('Test con Nonce - ERROR', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                }
            });
        }
        
        // Test automatico al caricamento della pagina
        $(document).ready(function() {
            console.log('Debug script caricato');
            
            // Verifica se chatWidgetParams è disponibile
            if (typeof chatWidgetParams !== 'undefined') {
                console.log('chatWidgetParams disponibile:', chatWidgetParams);
            } else {
                console.log('chatWidgetParams NON disponibile');
            }
            
            // Verifica se documentViewerParams è disponibile
            if (typeof documentViewerParams !== 'undefined') {
                console.log('documentViewerParams disponibile:', documentViewerParams);
            } else {
                console.log('documentViewerParams NON disponibile');
            }
        });
    </script>
</body>
</html>
