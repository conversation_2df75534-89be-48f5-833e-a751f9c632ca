/**
 * Chat Model Widget - JavaScript functionality
 * 
 * Manages all chat interactions between users and AI models
 */
jQuery(document).ready(function ($) {
    // Store chat messages for the session
    let chatMessages = [];
    let isTyping = false;
    let typingSpeed = 40; // Characters per second for typing effect

    /**
     * Initialize chat widget functionality
     */
    function initChatWidget() {
        // Find all chat widgets in the page
        $('.chat-widget').each(function() {
            const $widget = $(this);
            
            // Get widget configuration from data attributes
            const apiEndpoint = $widget.data('api-endpoint');
            const apiModel = $widget.data('api-model');
            
            // We don't need to get the API key here, it will be handled securely by the server
            
            if (!apiEndpoint || !apiModel) {
                console.error('Chat widget missing configuration data');
                $widget.find('.chat-log').append('<div class="message error-message">Chat configuration incomplete</div>');
                return;
            }
            
            // Setup mega menu functionality
            setupFinancialQuestionsMenu($widget);
            
            // Initialize the input handlers
            initChatInputHandlers($widget);
        });
    }
    
    /**
     * Setup financial questions mega menu functionality
     * @param {jQuery} $widget - The chat widget element
     */
    function setupFinancialQuestionsMenu($widget) {
        const $megaMenu = $widget.find('.academy-mega-menu');
        const $questionsMenu = $widget.find('.financial-questions-menu');
        
        // Load categories immediately since menu is always visible
        loadCategories($widget);
        
        // Back to categories button
        $widget.find('.back-to-categories').on('click', function(e) {
            e.preventDefault();
            $questionsMenu.removeClass('active');
        });
    }
    
    /**
     * Load categories for the mega menu
     * @param {jQuery} $widget - The chat widget element
     */
    function loadCategories($widget) {
        console.log('Chat Widget Debug: loadCategories called for widget:', $widget);

        const $categoriesGrid = $widget.find('.categories-grid');
        console.log('Chat Widget Debug: categories grid found:', $categoriesGrid.length);

        if ($categoriesGrid.length === 0) {
            console.error('Chat Widget Debug: .categories-grid not found in widget');
            return;
        }

        // Show loading state
        $categoriesGrid.html('<div class="loading-categories">' + (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.i18n.loadingCategories : 'Caricamento categorie...') + '</div>');
        
        const ajaxUrl = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.ajaxUrl : (typeof documentViewerParams !== 'undefined' ? documentViewerParams.ajaxUrl : '/wp-admin/admin-ajax.php'));
        const nonce = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.nonce : (typeof documentViewerParams !== 'undefined' ? documentViewerParams.nonce : ''));

        console.log('Chat Widget Debug: AJAX URL:', ajaxUrl);
        console.log('Chat Widget Debug: Nonce:', nonce);

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_category_stats',
                nonce: nonce
            },
            success: function(response) {
                console.log('Chat Widget Debug: AJAX Success Response:', response);

                if (response.success && response.data) {
                    console.log('Chat Widget Debug: Categories data:', response.data);
                    renderCategories($widget, response.data);
                } else {
                    console.error('Chat Widget Debug: Invalid response format or no data');
                    $categoriesGrid.html('<div class="loading-categories error">Errore: ' + (response.data?.message || 'Formato risposta non valido') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Chat Widget Debug: AJAX Error:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });
                $categoriesGrid.html('<div class="loading-categories error">Errore di connessione: ' + error + ' (Status: ' + xhr.status + ')</div>');
            }
        });
    }
    
    /**
     * Render categories in the mega menu
     * @param {jQuery} $widget - The chat widget element  
     * @param {Array} categories - Array of category objects
     */
    function renderCategories($widget, categories) {
        const $categoriesGrid = $widget.find('.categories-grid');
        $categoriesGrid.empty();
        
        // Category icons mapping
        const categoryIcons = {
            'general': '📊',
            'investment': '💰',
            'retirement': '🏦',
            'tax': '💼',
            'balance_sheet': '📈',
            'kpi': '⚡',
            'ratios': '🔢'
        };
        
        categories.forEach(function(category) {
            const icon = categoryIcons[category.key] || '📋';
            const $categoryCard = $(`
                <div class="category-card" data-category="${category.key}">
                    <div class="category-icon">${icon}</div>
                    <h4 class="category-name">${category.name}</h4>
                    <p class="category-count">${category.count} domande</p>
                </div>
            `);
            
            // Handle category click
            $categoryCard.on('click', function() {
                const categoryKey = $(this).data('category');
                const categoryName = $(this).find('.category-name').text();
                loadCategoryQuestions($widget, categoryKey, categoryName);
            });
            
            $categoriesGrid.append($categoryCard);
        });
    }
    
    /**
     * Load questions for a specific category
     * @param {jQuery} $widget - The chat widget element
     * @param {string} categoryKey - The category key
     * @param {string} categoryName - The category display name
     */
    function loadCategoryQuestions($widget, categoryKey, categoryName) {
        const $questionsMenu = $widget.find('.financial-questions-menu');
        const $categoryTitle = $widget.find('.category-title');
        const $questionsList = $widget.find('.questions-list');
        
        // Show questions menu overlay
        $questionsMenu.addClass('active');
        
        // Set category title
        $categoryTitle.text(categoryName);
        
        // Show loading state
        $questionsList.html('<div class="loading-questions">' + (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.i18n.loadingQuestions : 'Caricamento domande...') + '</div>');
        
        $.ajax({
            url: (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.ajaxUrl : (typeof documentViewerParams !== 'undefined' ? documentViewerParams.ajaxUrl : '/wp-admin/admin-ajax.php')),
            type: 'POST',
            data: {
                action: 'get_academy_questions',
                category: categoryKey,
                nonce: (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.nonce : (typeof documentViewerParams !== 'undefined' ? documentViewerParams.nonce : ''))
            },
            success: function(response) {
                if (response.success && response.data && response.data.questions) {
                    renderQuestions($widget, response.data.questions);
                } else {
                    $questionsList.html('<div class="loading-questions">' + (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.i18n.noQuestions : 'Nessuna domanda trovata') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading questions:', error);
                $questionsList.html('<div class="loading-questions">' + (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.i18n.errorLoading : 'Errore nel caricamento delle domande') + '</div>');
            }
        });
    }
    
    /**
     * Render questions in the questions menu
     * @param {jQuery} $widget - The chat widget element
     * @param {Array} questions - Array of question objects
     */
    function renderQuestions($widget, questions) {
        const $questionsList = $widget.find('.questions-list');
        $questionsList.empty();
        
        questions.forEach(function(question) {
            const $questionItem = $(`<div class="question-item">${question.question_text}</div>`);
            
            // Handle question click
            $questionItem.on('click', function() {
                // Close questions menu, return to categories
                $widget.find('.financial-questions-menu').removeClass('active');
                
                // Set the question in the input field
                $widget.find('.chat-input').val(question.question_text);
                
                // Focus on the input field
                $widget.find('.chat-input').focus();
            });
            
            $questionsList.append($questionItem);
        });
    }

    /**
     * Initialize input handlers for chat widget
     * @param {jQuery} $widget - The chat widget element
     */
    function initChatInputHandlers($widget) {
        const $input = $widget.find('.chat-input');
        const $sendButton = $widget.find('.send-btn');
        
        // Handle send button click
        $sendButton.on('click', function() {
            sendChatMessage($widget);
        });
        
        // Handle pressing Enter in the input field
        $input.on('keypress', function(e) {
            if (e.which === 13 && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage($widget);
            }
        });
    }

    /**
     * Send a chat message to the API
     * @param {jQuery} $widget - The chat widget element
     */
    function sendChatMessage($widget) {
        const $input = $widget.find('.chat-input');
        const message = $input.val().trim();
        
        if (!message || isTyping) {
            return;
        }
        
        // Clear input field
        $input.val('');
        
        // Add user message to the chat log
        addMessageToChat($widget, message, 'user');
        
        // Store message in chat history
        chatMessages.push({
            role: 'user',
            content: message
        });
        
        // Show typing indicator directly in the chat history
        addTypingIndicator($widget);
        
        // Send message to the API
        sendToChatAPI($widget, message);
    }
    
    /**
     * Add typing indicator to the chat history
     * @param {jQuery} $widget - The chat widget element
     */
    function addTypingIndicator($widget) {
        const $chatLog = $widget.find('.chat-log');
        
        // Create typing indicator element with AI avatar
        const $typingIndicator = $('<div>').addClass('message ai-message typing-indicator');
        
        // Add avatar for AI
        let avatarHtml = '<div class="message-avatar ai-avatar"></div>';
        
        // Set message content with dots animation
        $typingIndicator.html(avatarHtml + '<div class="message-content"><span>AI is thinking<span class="typing-dots">...</span></span></div>');
        
        // Add the typing indicator to chat log
        $chatLog.append($typingIndicator);
        
        // Scroll to bottom
        $chatLog.scrollTop($chatLog[0].scrollHeight);
        
        // Animate the dots
        animateTypingDots($typingIndicator.find('.typing-dots'));
    }
    
    /**
     * Remove typing indicator from chat history
     * @param {jQuery} $widget - The chat widget element
     */
    function removeTypingIndicator($widget) {
        $widget.find('.typing-indicator').remove();
    }
    
    /**
     * Send message to the chat API
     * @param {jQuery} $widget - The chat widget element
     * @param {string} message - The message to send
     */
    function sendToChatAPI($widget, message) {
        isTyping = true;
        
        // Get the apiEndpoint and apiModel from data attributes
        const apiEndpoint = $widget.data('api-endpoint');
        const apiModel = $widget.data('api-model');
        
        // Using the global documentViewerParams for AJAX URL and nonce
        // This ensures we're using the same settings as the document viewer
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chat_model',
                nonce: documentViewerParams.nonce,
                chat_message: message,
                chat_history: JSON.stringify(chatMessages),
                // Include the model from data attributes if available
                api_model: apiModel
            },
            success: function(response) {
                if (response.success) {
                    // Remove typing indicator
                    removeTypingIndicator($widget);
                    
                    // Create a placeholder for the AI response with the typing animation
                    const aiMessage = response.data.reply;
                    
                    // Create message element with empty content
                    const $chatLog = $widget.find('.chat-log');
                    const $message = $('<div>').addClass('message').addClass('ai-message');
                    
                    // Add avatar for AI
                    let avatarHtml = '<div class="message-avatar ai-avatar"></div>';
                    
                    // Add a container for the message with cursor
                    $message.html(avatarHtml + '<div class="message-content typing-container"><span class="typing-text"></span><span class="typing-cursor">|</span></div>');
                    
                    // Add to chat log
                    $chatLog.append($message);
                    
                    // Scroll to bottom
                    $chatLog.scrollTop($chatLog[0].scrollHeight);
                    
                    // Start typing effect with actual message
                    startTypingEffect($message.find('.typing-text'), aiMessage, function() {
                        // Remove cursor when typing is complete
                        $message.find('.typing-cursor').remove();
                        
                        // Store the AI response in chat history
                        chatMessages.push({
                            role: 'assistant',
                            content: aiMessage
                        });
                        
                        isTyping = false;
                    });
                } else {
                    // Remove typing indicator
                    removeTypingIndicator($widget);
                    
                    // Show error message
                    addMessageToChat($widget, 'Error: ' + (response.data.message || 'Unknown error'), 'error');
                    
                    isTyping = false;
                }
            },
            error: function(xhr, status, error) {
                // Remove typing indicator
                removeTypingIndicator($widget);
                
                // Show error message
                addMessageToChat($widget, 'Connection error: ' + error, 'error');
                
                isTyping = false;
            }
        });
    }
    
    /**
     * Start typing effect animation
     * @param {jQuery} $element - The element to add text to
     * @param {string} text - The text to type
     * @param {Function} callback - Callback function to call when typing is complete
     */
    function startTypingEffect($element, text, callback) {
        // Format the text with proper HTML
        const formattedText = formatChatMessage(text);
        
        // Parse the HTML to preserve formatting
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formattedText;
        const textContent = tempDiv.textContent || tempDiv.innerText || formattedText;
        
        let charIndex = 0;
        let htmlIndex = 0;
        let inTag = false;
        let currentHtml = '';
        
        // Function to type characters
        function typeCharacter() {
            if (htmlIndex >= formattedText.length) {
                // Typing is complete
                if (callback) callback();
                return;
            }
            
            // Process HTML tags - if we encounter a < character, we're entering a tag
            if (formattedText[htmlIndex] === '<') {
                inTag = true;
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Process the tag immediately (no delay)
                return;
            }
            
            // If we encounter a > character, we're exiting a tag
            if (formattedText[htmlIndex] === '>' && inTag) {
                inTag = false;
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Continue immediately after the tag
                return;
            }
            
            // If we're inside a tag, add the character to currentHtml without delay
            if (inTag) {
                currentHtml += formattedText[htmlIndex];
                htmlIndex++;
                typeCharacter(); // Process the next character immediately
                return;
            }
            
            // For regular text (not in tag), add with delay for typing effect
            currentHtml += formattedText[htmlIndex];
            $element.html(currentHtml);
            
            htmlIndex++;
            charIndex++;
            
            // Random typing speed variation for natural effect
            const randomDelay = Math.floor(Math.random() * 10) + 10;
            const delay = 1000 / (typingSpeed + randomDelay);
            
            // Type next character with a bit of randomness in timing
            setTimeout(typeCharacter, delay);
            
            // Ensure scrolling to bottom as text grows
            const $chatLog = $element.closest('.chat-log');
            $chatLog.scrollTop($chatLog[0].scrollHeight);
        }
        
        // Start the typing effect
        typeCharacter();
    }
    
    /**
     * Add a message to the chat log
     * @param {jQuery} $widget - The chat widget element
     * @param {string} message - The message text
     * @param {string} role - The message role (user, ai, system, error)
     */
    function addMessageToChat($widget, message, role) {
        const $chatLog = $widget.find('.chat-log');
        
        // Create message element
        const $message = $('<div>').addClass('message').addClass(role + '-message');
        
        // Add avatar or icon based on role
        let avatarHtml = '';
        if (role === 'user') {
            avatarHtml = '<div class="message-avatar user-avatar"></div>';
        } else if (role === 'ai') {
            avatarHtml = '<div class="message-avatar ai-avatar"></div>';
        }
        
        // If it's not an AI message (or if it's a system/error message), add it immediately
        // AI messages are handled differently with the typing effect
        if (role !== 'ai') {
            $message.html(avatarHtml + '<div class="message-content">' + formatChatMessage(message) + '</div>');
            
            // Add to chat log
            $chatLog.append($message);
            
            // Scroll to bottom
            $chatLog.scrollTop($chatLog[0].scrollHeight);
        }
    }
    
    /**
     * Format chat message with proper HTML
     * @param {string} message - The message to format
     * @returns {string} - Formatted HTML message
     */
    function formatChatMessage(message) {
        // Convert LaTeX formulas to readable format
        message = formatMathFormulas(message);
        
        // Convert URLs to links
        message = message.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
        
        // Remove double ## symbols (heading markers)
        message = message.replace(/##\s*/g, '');
        
        // Remove single # symbols (heading markers)
        message = message.replace(/#\s*/g, '');
        
        // Remove double ** symbols and convert surrounding text to bold
        message = message.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        
        // Remove single * symbols and convert surrounding text to bold
        message = message.replace(/\*([^*]+)\*/g, '<strong>$1</strong>');
        
        // Convert hyphens in list items to checkmarks (✓)
        message = message.replace(/^- (.+)$/gm, '✓ $1');
        message = message.replace(/<br>- (.+?)(?=<br>|$)/g, '<br>✓ $1');
        
        // Convert line breaks to <br>
        message = message.replace(/\n/g, '<br>');
        
        return message;
    }
    
    /**
     * Convert LaTeX mathematical formulas to readable HTML format
     * @param {string} message - The message containing LaTeX formulas
     * @returns {string} - Message with formatted mathematical formulas
     */
    function formatMathFormulas(message) {
        // Convert LaTeX block formulas \[ ... \] to formatted HTML
        message = message.replace(/\\\[\s*(.*?)\s*\\\]/gs, function(match, formula) {
            return '<div class="math-formula">' + formatLaTeXFormula(formula) + '</div>';
        });
        
        // Convert LaTeX inline formulas \( ... \) to formatted HTML
        message = message.replace(/\\\(\s*(.*?)\s*\\\)/g, function(match, formula) {
            return '<span class="math-inline">' + formatLaTeXFormula(formula) + '</span>';
        });
        
        // Convert simple $ ... $ inline math to formatted HTML
        message = message.replace(/\$([^$]+)\$/g, function(match, formula) {
            return '<span class="math-inline">' + formatLaTeXFormula(formula) + '</span>';
        });
        
        // Handle raw LaTeX formulas that contain \frac without delimiters
        // Enhanced pattern to catch more variations
        message = message.replace(/([A-Z]{2,4}\s*=\s*[^<\n]*\\frac\{[^}]+\}\{[^}]+\}[^<\n]*(?:\s*[×x]\s*\d+)?)/gi, function(match, formula) {
            return '<div class="math-formula">' + formatLaTeXFormula(formula) + '</div>';
        });
        
        // Handle formulas that start with text like "Il ROE = \frac{...}"
        message = message.replace(/((?:Il|La|Lo)\s+[A-Z]{2,4}\s*=\s*[^<\n]*\\frac\{[^}]+\}\{[^}]+\}[^<\n]*)/gi, function(match, formula) {
            return '<div class="math-formula">' + formatLaTeXFormula(formula) + '</div>';
        });
        
        // Handle standalone \frac expressions that weren't caught above
        message = message.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, function(match, num, den) {
            return '<span class="math-inline"><span class="fraction"><span class="numerator">' + formatLaTeXFormula(num) + '</span><span class="fraction-line">─</span><span class="denominator">' + formatLaTeXFormula(den) + '</span></span></span>';
        });
        
        return message;
    }
    
    /**
     * Format a single LaTeX formula to readable HTML
     * @param {string} formula - The LaTeX formula
     * @returns {string} - Formatted HTML formula
     */
    function formatLaTeXFormula(formula) {
        // Remove extra whitespace
        formula = formula.trim();
        
        // Handle fractions \frac{numerator}{denominator}
        formula = formula.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, function(match, num, den) {
            return '<span class="fraction"><span class="numerator">' + formatLaTeXFormula(num) + '</span><span class="fraction-line">─</span><span class="denominator">' + formatLaTeXFormula(den) + '</span></span>';
        });
        
        // Handle \text{} commands
        formula = formula.replace(/\\text\{([^}]+)\}/g, '$1');
        
        // Handle \times multiplication symbol
        formula = formula.replace(/\\times/g, '×');
        
        // Handle common financial formulas
        formula = formula.replace(/ROE/g, '<strong>ROE</strong>');
        formula = formula.replace(/ROI/g, '<strong>ROI</strong>');
        formula = formula.replace(/ROA/g, '<strong>ROA</strong>');
        formula = formula.replace(/EBITDA/g, '<strong>EBITDA</strong>');
        formula = formula.replace(/Utile Netto/g, '<em>Utile Netto</em>');
        formula = formula.replace(/Patrimonio Netto/g, '<em>Patrimonio Netto</em>');
        formula = formula.replace(/Capitale Proprio/g, '<em>Capitale Proprio</em>');
        formula = formula.replace(/Ricavi/g, '<em>Ricavi</em>');
        formula = formula.replace(/Attivo/g, '<em>Attivo</em>');
        formula = formula.replace(/Costi/g, '<em>Costi</em>');
        formula = formula.replace(/Investimenti/g, '<em>Investimenti</em>');
        formula = formula.replace(/Capitale Investito/g, '<em>Capitale Investito</em>');
        
        // Handle × symbol
        formula = formula.replace(/×/g, ' × ');
        formula = formula.replace(/\s+×\s+/g, ' × ');
        
        // Handle exponents ^{...}
        formula = formula.replace(/\^{([^}]+)}/g, '<sup>$1</sup>');
        
        // Handle subscripts _{...}
        formula = formula.replace(/_{([^}]+)}/g, '<sub>$1</sub>');
        
        // Handle square roots \sqrt{...}
        formula = formula.replace(/\\sqrt\{([^}]+)\}/g, '√(<span class="sqrt-content">$1</span>)');
        
        return formula;
    }
    
    /**
     * Animate typing dots
     * @param {jQuery} $element - The element containing typing dots
     */
    function animateTypingDots($element) {
        let count = 0;
        const interval = setInterval(function() {
            if (!$element.is(':visible')) {
                clearInterval(interval);
                return;
            }
            
            count = (count + 1) % 4;
            const dots = '.'.repeat(count);
            $element.text(dots);
        }, 300);
    }

    // Debug: Log available parameters
    console.log('Chat Widget Debug: Initializing...');
    console.log('chatWidgetParams available:', typeof chatWidgetParams !== 'undefined');
    console.log('documentViewerParams available:', typeof documentViewerParams !== 'undefined');

    if (typeof chatWidgetParams !== 'undefined') {
        console.log('chatWidgetParams:', chatWidgetParams);
    }

    if (typeof documentViewerParams !== 'undefined') {
        console.log('documentViewerParams:', documentViewerParams);
    }

    // Initialize the chat widget functionality
    initChatWidget();
    
    // Expose formatting functions globally for testing
    window.formatChatMessage = formatChatMessage;
    window.formatMathFormulas = formatMathFormulas;
    window.formatLaTeXFormula = formatLaTeXFormula;
    
    // Add CSS styles for typing effect
    const typingStyles = `
        <style>
            .typing-container {
                position: relative;
                display: inline-block;
            }
            
            .typing-cursor {
                display: inline-block;
                width: 2px;
                margin-left: 2px;
                background-color: #333;
                animation: blink 0.7s infinite;
            }
            
            .typing-indicator {
                opacity: 0.8;
            }
            
            .typing-indicator .message-content {
                display: flex;
                align-items: center;
                font-style: italic;
            }
            
            .typing-dots {
                display: inline-block;
                min-width: 12px;
                text-align: left;
            }
            
            @keyframes blink {
                0%, 100% { opacity: 1; }
                50% { opacity: 0; }
            }
        </style>
    `;
    
    // Add styling if it's not already there
    if (!$('head').find('style:contains(.typing-container)').length) {
        $('head').append(typingStyles);
    }
});