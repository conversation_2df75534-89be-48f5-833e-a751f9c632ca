<?php
/**
 * Document Analyzer Class
 * 
 * Handles text analysis for the Office Add-in integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class Document_Analyzer {
    
    /**
     * Analyze text using the configured AI API
     * 
     * @param string $text The text to analyze
     * @param string $query_text The analysis query
     * @return array Result array with success status and response/error
     */
    public function analyze_text($text, $query_text) {
        try {
            // Sanitize inputs
            $text = sanitize_textarea_field($text);
            $query_text = sanitize_textarea_field($query_text);
            
            if (empty($text) || empty($query_text)) {
                return [
                    'success' => false,
                    'error' => __('Text and query are required', 'document-viewer-plugin')
                ];
            }
            
            // Get API settings
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $api_model = get_option('document_viewer_model', '');
            
            if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
                return [
                    'success' => false,
                    'error' => __('API settings not configured properly', 'document-viewer-plugin')
                ];
            }
            
            // Prepare API request
            $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
            
            $request_body = [
                'model' => $api_model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a financial document analyzer. Analyze the provided text and answer the specific question in a clear, professional manner.'
                    ],
                    [
                        'role' => 'user',
                        'content' => "Text to analyze:\n\n$text\n\nQuestion: $query_text"
                    ]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.7
            ];
            
            $args = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json',
                    'HTTP-Referer' => site_url(),
                    'X-Title' => 'Financial Advisor Excel Add-in'
                ],
                'body' => wp_json_encode($request_body),
                'method' => 'POST',
                'timeout' => 60,
                'sslverify' => true
            ];
            
            // Log the request for debugging
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Excel Add-in API request initiated for model: ' . $api_model, 'office_addin');
            }
            
            // Make API request
            $response = wp_remote_post($api_url, $args);
            
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Excel Add-in API error: ' . $error_message, 'office_addin');
                }
                return [
                    'success' => false,
                    'error' => __('API request failed: ', 'document-viewer-plugin') . $error_message
                ];
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            
            if ($status_code !== 200) {
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Excel Add-in API status error: ' . $status_code . ' - ' . $body, 'office_addin');
                }
                return [
                    'success' => false,
                    'error' => __('API returned error status: ', 'document-viewer-plugin') . $status_code
                ];
            }
            
            $data = json_decode($body, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Excel Add-in JSON decode error: ' . json_last_error_msg(), 'office_addin');
                }
                return [
                    'success' => false,
                    'error' => __('Invalid JSON response from API', 'document-viewer-plugin')
                ];
            }
            
            if (!isset($data['choices'][0]['message']['content'])) {
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Excel Add-in invalid API response structure: ' . print_r($data, true), 'office_addin');
                }
                return [
                    'success' => false,
                    'error' => __('Invalid API response format', 'document-viewer-plugin')
                ];
            }
            
            $analysis_result = $data['choices'][0]['message']['content'];
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Excel Add-in analysis completed successfully', 'office_addin');
            }
            
            return [
                'success' => true,
                'response' => $analysis_result
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Excel Add-in analysis exception: ' . $e->getMessage(), 'office_addin');
            }
            return [
                'success' => false,
                'error' => __('Analysis failed due to an unexpected error', 'document-viewer-plugin')
            ];
        }
    }
    
    /**
     * Validate API configuration
     * 
     * @return array Validation result
     */
    public function validate_api_config() {
        $api_key = get_option('document_viewer_api_key', '');
        $api_endpoint = get_option('document_viewer_api_endpoint', '');
        $api_model = get_option('document_viewer_model', '');
        
        $errors = [];
        
        if (empty($api_key)) {
            $errors[] = __('API key is not configured', 'document-viewer-plugin');
        }
        
        if (empty($api_endpoint)) {
            $errors[] = __('API endpoint is not configured', 'document-viewer-plugin');
        }
        
        if (empty($api_model)) {
            $errors[] = __('API model is not selected', 'document-viewer-plugin');
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Test API connection
     * 
     * @return array Test result
     */
    public function test_api_connection() {
        try {
            $validation = $this->validate_api_config();
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => implode(', ', $validation['errors'])
                ];
            }
            
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            
            // Test with a simple models request
            $test_url = rtrim($api_endpoint, '/') . '/models';
            
            $response = wp_remote_get($test_url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 10,
                'sslverify' => true
            ]);
            
            if (is_wp_error($response)) {
                return [
                    'success' => false,
                    'error' => __('Connection failed: ', 'document-viewer-plugin') . $response->get_error_message()
                ];
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            
            if ($status_code === 200) {
                return [
                    'success' => true,
                    'message' => __('API connection successful', 'document-viewer-plugin')
                ];
            } else {
                return [
                    'success' => false,
                    'error' => __('API returned status: ', 'document-viewer-plugin') . $status_code
                ];
            }
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Excel Add-in connection test exception: ' . $e->getMessage(), 'office_addin');
            }
            return [
                'success' => false,
                'error' => __('Connection test failed', 'document-viewer-plugin')
            ];
        }
    }
}
