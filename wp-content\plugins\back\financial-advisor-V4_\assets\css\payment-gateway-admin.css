/**
 * Payment Gateway Admin CSS
 * Styling for payment gateway configuration pages
 */

.payment-gateway-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-top: 20px;
}

.payment-gateway-container .nav-tab-wrapper {
    margin: 0;
    border-bottom: 1px solid #c3c4c7;
}

.payment-gateway-container .nav-tab {
    background: #f0f0f1;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    margin: 0;
    padding: 12px 16px;
    color: #50575e;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.2s ease;
}

.payment-gateway-container .nav-tab:hover {
    background: #fff;
    color: #135e96;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.payment-gateway-container .nav-tab:focus {
    outline: 2px solid #2271b1;
    outline-offset: -2px;
}

.payment-gateway-container .nav-tab.nav-tab-active {
    background: #fff;
    color: #135e96;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

/* Responsive tabs for mobile */
@media (max-width: 768px) {
    .payment-gateway-container .nav-tab-wrapper {
        display: flex;
        flex-wrap: wrap;
    }

    .payment-gateway-container .nav-tab {
        flex: 1;
        text-align: center;
        min-width: 120px;
        font-size: 12px;
        padding: 10px 8px;
    }
}

.tab-content-wrapper {
    background: #fff;
    border-top: none;
    min-height: 400px;
}

.tab-content {
    display: none;
    padding: 20px;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gateway-config-section {
    max-width: 800px;
}

.gateway-config-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #23282d;
    font-size: 1.3rem;
    font-weight: 600;
}

.gateway-config-section .form-table {
    margin-top: 20px;
}

.gateway-config-section .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    font-weight: 600;
    color: #23282d;
}

.gateway-config-section .form-table td {
    padding: 15px 10px;
}

.gateway-config-section .regular-text {
    width: 300px;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    background: #fff;
    color: #2c3338;
    font-size: 14px;
}

.gateway-config-section .regular-text:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.gateway-config-section select {
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    background: #fff;
    color: #2c3338;
    font-size: 14px;
    min-width: 150px;
}

.gateway-config-section select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.gateway-config-section .description {
    margin-top: 5px;
    color: #646970;
    font-style: italic;
    font-size: 13px;
}

.gateway-config-section .submit {
    padding-top: 20px;
    border-top: 1px solid #c3c4c7;
    margin-top: 20px;
}

.gateway-config-section .button {
    padding: 8px 16px;
    margin-right: 10px;
    border: 1px solid #2271b1;
    border-radius: 3px;
    background: #f6f7f7;
    color: #2271b1;
    text-decoration: none;
    font-size: 13px;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.2s;
}

.gateway-config-section .button:hover {
    background: #2271b1;
    color: #fff;
}

.gateway-config-section .button-primary {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}

.gateway-config-section .button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.gateway-config-section .button:disabled,
.gateway-config-section .button-primary:disabled {
    background: #c3c4c7;
    border-color: #c3c4c7;
    color: #a7aaad;
    cursor: not-allowed;
}

/* Feedback Messages */
#gateway-feedback-message {
    margin-top: 20px;
    padding: 12px;
    border-left: 4px solid;
    border-radius: 0 4px 4px 0;
}

#gateway-feedback-message.notice-success {
    background: #fff;
    border-left-color: #46b450;
    color: #1e7e1e;
}

#gateway-feedback-message.notice-error {
    background: #fff;
    border-left-color: #dc3232;
    color: #d63638;
}

#gateway-feedback-message.notice-warning {
    background: #fff;
    border-left-color: #ffb900;
    color: #8f5700;
}

/* Loading States */
.gateway-config-section .button.loading {
    position: relative;
    color: transparent;
}

.gateway-config-section .button.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: gateway-spinner 1s linear infinite;
}

@keyframes gateway-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Security indicators */
.gateway-config-section .security-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 20px;
    color: #856404;
}

.gateway-config-section .security-note strong {
    color: #6c5400;
}

/* Environment badges */
.environment-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.environment-badge.sandbox,
.environment-badge.test {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.environment-badge.live {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Testing & Debug Tab Styles */
.testing-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.testing-tool-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testing-tool-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.testing-tool-card h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.testing-tool-card p {
    color: #666;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.testing-tool-card .button {
    margin-right: 8px;
    margin-bottom: 8px;
}

.testing-results-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-top: 20px;
}

.testing-output {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* Checklist Tab Styles */
.checklist-intro {
    background: #e8f4f8;
    border-left: 4px solid #2196F3;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 4px 4px 0;
}

.checklist-controls {
    text-align: center;
    margin: 20px 0;
}

.checklist-controls .button {
    margin: 0 5px;
}

.checklist-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #2196F3);
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #666;
    margin: 0;
}

.checklist-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.summary-card h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #666;
}

.summary-count {
    font-size: 24px;
    font-weight: bold;
    margin: 5px 0;
}

.summary-passed .summary-count {
    color: #4CAF50;
}

.summary-failed .summary-count {
    color: #f44336;
}

.summary-warnings .summary-count {
    color: #ff9800;
}

.checklist-details {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.checklist-category {
    border-bottom: 1px solid #e0e0e0;
}

.checklist-category:last-child {
    border-bottom: none;
}

.category-header {
    background: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-header:hover {
    background: #e9ecef;
}

.category-status {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
}

.category-status.passed {
    background: #4CAF50;
}

.category-status.failed {
    background: #f44336;
}

.category-status.warning {
    background: #ff9800;
}

.category-items {
    display: none;
    padding: 0;
}

.category-items.expanded {
    display: block;
}

.checklist-item {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
}

.checklist-item:last-child {
    border-bottom: none;
}

.item-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
}

.item-status.passed {
    background: #4CAF50;
}

.item-status.failed {
    background: #f44336;
}

.item-status.warning {
    background: #ff9800;
}

.item-content {
    flex-grow: 1;
}

.item-name {
    font-weight: 500;
    margin: 0 0 3px 0;
}

.item-message {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* Monitoring Tab Styles */
.monitoring-dashboard {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.monitoring-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.stat-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    opacity: 0.9;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
}

.monitoring-controls {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.monitoring-controls .button {
    margin: 0 5px;
}

.error-logs-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 20px;
}

.error-logs-container h3 {
    margin: 0;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.error-logs-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
}

.no-errors {
    text-align: center;
    color: #4CAF50;
    font-weight: 500;
    margin: 20px 0;
}

.error-log-item {
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #fafafa;
}

.error-log-timestamp {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.error-log-message {
    color: #d32f2f;
    font-weight: 500;
}

.monitoring-live-feed {
    background: #263238;
    color: #b0bec5;
    border-radius: 8px;
    margin-top: 20px;
}

.monitoring-live-feed h3 {
    margin: 0;
    padding: 15px 20px;
    background: #37474f;
    color: #fff;
}

.live-feed-content {
    padding: 15px 20px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

/* Status Indicators */
.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background: #4CAF50;
    box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
}

.status-inactive {
    background: #f44336;
}

.status-warning {
    background: #ff9800;
}

/* Debug Panel Styles */
.debug-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.debug-panel-content {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.debug-panel-header {
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.debug-panel-header h3 {
    margin: 0;
    font-size: 16px;
}

.debug-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.debug-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.debug-panel-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.debug-console-output {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    min-height: 300px;
}

/* Responsive Design for New Features */
@media (max-width: 768px) {
    .testing-tools-grid {
        grid-template-columns: 1fr;
    }

    .monitoring-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .checklist-summary {
        grid-template-columns: repeat(3, 1fr);
    }

    .debug-panel-content {
        width: 95%;
        max-height: 90%;
    }

    .monitoring-controls .button,
    .checklist-controls .button {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    .monitoring-stats {
        grid-template-columns: 1fr;
    }

    .checklist-summary {
        grid-template-columns: 1fr;
    }

    .testing-tool-card {
        padding: 15px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Success/Error Message Styles */
.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 12px 15px;
    margin: 10px 0;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px 15px;
    margin: 10px 0;
}

.warning-message {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px 15px;
    margin: 10px 0;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Enhancement */
.button-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.button-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button-enhanced:active {
    transform: translateY(0);
}

/* Tab Enhancement */
.nav-tab {
    transition: all 0.2s ease;
}

.nav-tab:hover {
    transform: translateY(-1px);
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.button:focus,
.nav-tab:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .testing-tool-card,
    .stat-card,
    .error-logs-container {
        border: 2px solid;
    }

    .status-dot,
    .category-status,
    .item-status {
        border: 1px solid #000;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .payment-gateway-container {
        background: #1e1e1e;
        border-color: #333;
        color: #e0e0e0;
    }

    .testing-tool-card {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }

    .error-logs-container {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }

    .checklist-details {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }
}

/* Testing & Debug Tab */
.testing-sub-tabs {
    margin: 20px 0 0 0;
    border-bottom: 1px solid #c3c4c7;
    padding-bottom: 5px;
}

.testing-sub-tabs .nav-tab {
    position: relative;
    float: left;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    margin-left: 0.5em;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.71428571;
    background: #f0f0f1;
    color: #50575e;
    text-decoration: none;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s ease;
}

.testing-sub-tabs .nav-tab:hover {
    background: #fff;
    color: #135e96;
}

.testing-sub-tabs .nav-tab.nav-tab-active {
    background: #fff;
    color: #135e96;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

.testing-sub-tab-content {
    display: none;
    padding: 20px 0;
    clear: both;
}

.testing-sub-tab-content.active {
    display: block;
}

/* Payment Gateway Tests Styling */
.payment-gateway-test-section {
    margin-bottom: 30px;
    border: 1px solid #e2e4e7;
    border-radius: 5px;
    overflow: hidden;
    background: #fff;
}

.payment-gateway-test-section h3 {
    margin: 0;
    padding: 12px 15px;
    background: #f0f0f1;
    border-bottom: 1px solid #e2e4e7;
    color: #1d2327;
    font-size: 14px;
    font-weight: 600;
}

.payment-gateway-tests {
    width: 100%;
    border-collapse: collapse;
}

.payment-gateway-tests .test-name {
    width: 30%;
    font-weight: 600;
}

.payment-gateway-tests .test-status {
    width: 10%;
    text-align: center;
}

.payment-gateway-tests .test-message {
    width: 60%;
}

.payment-gateway-tests .test-row.passed {
    background-color: #f0f6e6;
}

.payment-gateway-tests .test-row.failed {
    background-color: #fcf0f1;
}

.payment-gateway-tests .test-icon {
    font-size: 18px;
}

.payment-test-summary {
    margin: 20px 0;
    padding: 15px;
    border-radius: 5px;
}

.payment-test-summary .test-actions {
    margin-top: 15px;
}

.payment-test-summary .test-actions .button {
    margin-right: 10px;
}

/* Manual Testing Interface */
.gateway-testing-controls {
    margin-bottom: 20px;
}

.gateway-testing-controls h4 {
    border-bottom: 1px solid #e2e4e7;
    padding-bottom: 5px;
    margin-top: 20px;
    margin-bottom: 10px;
}

.test-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.test-result-container {
    margin-top: 20px;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 5px;
}

.test-result-output {
    min-height: 100px;
    max-height: 400px;
    overflow-y: auto;
}

/* Testing Tools Grid */
.testing-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    grid-gap: 20px;
    margin-top: 20px;
}

.testing-tool-card {
    padding: 20px;
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.testing-tool-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1d2327;
}

.testing-tool-card p {
    margin-bottom: 15px;
    color: #50575e;
}

.testing-tool-card button {
    margin: 5px 5px 0 0;
}

/* Test Results Display */
.all-gateway-results, 
.webhook-test-results,
.error-simulation-results {
    margin-top: 15px;
}

.all-gateway-results h3,
.webhook-test-results h3,
.error-simulation-results h3 {
    margin-top: 0;
    margin-bottom: 15px;
}

.all-gateway-results table,
.webhook-test-results table,
.error-simulation-results table {
    margin-bottom: 15px;
}

.all-gateway-results tr.success,
.webhook-test-results tr.success {
    background-color: #f0f6e6 !important;
}

.all-gateway-results tr.error,
.webhook-test-results tr.error {
    background-color: #fcf0f1 !important;
}

.webhook-test-info,
.error-simulation-info {
    margin-top: 15px;
}

/* System Checklist Tab */
.checklist-intro {
    margin-bottom: 20px;
}

.checklist-controls {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

.checklist-progress {
    margin: 30px 0;
}

.progress-bar {
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    position: relative;
    margin-bottom: 10px;
    overflow: hidden;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #2271b1, #72aee6);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-style: italic;
    color: #50575e;
}

.checklist-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    padding: 15px;
    border-radius: 5px;
    text-align: center;
    background: #fff;
    border: 1px solid #e2e4e7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-card h4 {
    margin: 0 0 10px 0;
    color: #1d2327;
}

.summary-card .summary-count {
    font-size: 24px;
    font-weight: 600;
}

.summary-card.summary-passed {
    border-left: 4px solid #00a32a;
}

.summary-card.summary-failed {
    border-left: 4px solid #d63638;
}

.summary-card.summary-warnings {
    border-left: 4px solid #dba617;
}

/* Error Monitoring Tab */
.monitoring-dashboard {
    margin-top: 20px;
}

.monitoring-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    padding: 15px;
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 5px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stat-card h4 {
    margin: 0 0 10px 0;
    color: #1d2327;
    font-size: 14px;
}

.stat-card .stat-value {
    font-size: 24px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 16px;
}

.status-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background-color: #00a32a;
}

.status-warning {
    background-color: #dba617;
}

.status-error {
    background-color: #d63638;
}

.monitoring-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.error-logs-container {
    padding: 20px;
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 5px;
    margin-bottom: 30px;
}

.error-logs-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e4e7;
}

.error-logs-list {
    max-height: 400px;
    overflow-y: auto;
}

.error-log-entry {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-left: 4px solid #d63638;
    background-color: #fcf0f1;
    border-radius: 0 5px 5px 0;
}

.error-log-entry.warning {
    border-left-color: #dba617;
    background-color: #fcf9e8;
}

.error-log-entry.info {
    border-left-color: #72aee6;
    background-color: #f0f6fc;
}

.error-log-time {
    font-size: 12px;
    color: #656b71;
    margin-bottom: 5px;
}

.error-log-message {
    font-weight: 500;
}

.error-log-details {
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
}

.no-errors {
    text-align: center;
    padding: 20px;
    color: #50575e;
    font-style: italic;
}

/* Debug Panel */
.debug-panel {
    position: fixed;
    bottom: 0;
    right: 20px;
    width: 80%;
    max-width: 800px;
    max-height: 600px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 5px 5px 0 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.debug-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f0f0f1;
    border-bottom: 1px solid #c3c4c7;
    border-radius: 5px 5px 0 0;
}

.debug-panel-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.debug-close-btn {
    background: none;
    border: none;
    padding: 0;
    font-size: 20px;
    line-height: 1;
    color: #82878c;
    cursor: pointer;
}

.debug-panel-body {
    padding: 15px;
    height: 400px;
    overflow-y: auto;
}

.debug-console-output {
    font-family: monospace;
    font-size: 12px;
    line-height: 1.5;
}

.debug-console-output h4 {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    margin-top: 15px;
    margin-bottom: 10px;
    color: #1d2327;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #e2e4e7;
    padding-bottom: 5px;
}

/* Enhanced Test Results */
.enhanced-test-results {
    margin: 15px 0;
}

.test-category {
    margin-bottom: 25px;
}

.test-category h4 {
    margin: 0 0 10px;
    font-size: 16px;
    font-weight: 600;
}

.test-results-table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #ccd0d4;
    background: #fff;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.test-results-table th {
    text-align: left;
    padding: 10px;
    border-bottom: 1px solid #ccd0d4;
    font-weight: 600;
}

.test-results-table td {
    padding: 10px;
    border-bottom: 1px solid #f0f0f1;
    vertical-align: middle;
}

.test-status {
    width: 60px;
    text-align: center;
}

.status-indicator {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 50%;
    font-size: 14px;
}

.status-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

.test-row.test-status-error {
    background-color: rgba(248, 215, 218, 0.05);
}

.test-row.test-status-warning {
    background-color: rgba(255, 243, 205, 0.05);
}

.test-summary {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.test-summary h4 {
    margin-top: 0;
    font-size: 16px;
    font-weight: 600;
}

.test-summary p {
    margin-bottom: 10px;
    font-size: 14px;
}

.test-status-success {
    background-color: rgba(209, 231, 221, 0.3);
    border-left: 4px solid #0f5132;
}

.test-status-warning {
    background-color: rgba(255, 243, 205, 0.3);
    border-left: 4px solid #856404;
}

.test-status-error {
    background-color: rgba(248, 215, 218, 0.3);
    border-left: 4px solid #721c24;
}

.progress-bar-container {
    height: 8px;
    background-color: #f0f0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    transition: width 0.3s ease;
}

.progress-success {
    background-color: #0f5132;
}

.progress-warning {
    background-color: #856404;
}

.progress-error {
    background-color: #721c24;
}

/* Logs Table Styling */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.logs-table th,
.logs-table td {
    padding: 8px 10px;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f1;
}

.logs-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.log-entry:hover {
    background-color: #f8f9fa;
}

.log-level {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.log-level-error {
    background-color: #f8d7da;
    color: #721c24;
}

.log-level-warning {
    background-color: #fff3cd;
    color: #856404;
}

.log-level-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.log-level-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.log-controls {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
}

/* Test Results Styles */
.test-results-container {
    margin: 20px 0;
    background-color: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.test-results-title {
    background-color: #f9f9f9;
    border-bottom: 1px solid #ccd0d4;
    padding: 12px 15px;
    margin: 0;
    font-size: 15px;
    font-weight: 600;
}

.test-results-list {
    padding: 15px;
    margin: 0;
}

.test-results-list .test-item {
    padding: 10px 15px;
    border-left: 4px solid transparent;
    margin-bottom: 8px;
    background-color: #fbfbfb;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.test-results-list .test-item:last-child {
    margin-bottom: 0;
}

.test-results-list .test-item:hover {
    background-color: #f5f5f5;
}

.test-results-list .test-item.test-success {
    border-left-color: #46b450;
}

.test-results-list .test-item.test-error {
    border-left-color: #dc3232;
}

.test-results-list .test-item.test-warning {
    border-left-color: #ffb900;
}

.test-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.test-item-name {
    font-weight: 600;
    color: #23282d;
    font-size: 14px;
}

.test-item-status {
    padding: 3px 8px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 500;
}

.test-item-status.status-pass {
    background-color: #ecf7ed;
    color: #2a6b35;
}

.test-item-status.status-fail {
    background-color: #f9e2e2;
    color: #a72121;
}

.test-item-status.status-warning {
    background-color: #fff8e5;
    color: #95701a;
}

.test-item-message {
    color: #555;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
}

/* Diagnostics styles */
.payment-gateway-diagnostic-report {
    margin: 20px 0;
    background-color: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.diagnostic-report-header {
    background-color: #f9f9f9;
    border-bottom: 1px solid #ccd0d4;
    padding: 15px;
}

.diagnostic-report-header h2 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #23282d;
}

.diagnostic-timestamp {
    color: #888;
    font-size: 12px;
    margin: 0;
}

.diagnostic-section {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.diagnostic-section h3 {
    margin-top: 0;
    font-size: 15px;
    color: #23282d;
    margin-bottom: 15px;
}

.diagnostic-results-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    border: 1px solid #eee;
}

.diagnostic-results-table th,
.diagnostic-results-table td {
    padding: 10px 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.diagnostic-results-table thead th {
    background-color: #f7f7f7;
    font-weight: 600;
    font-size: 13px;
}

.diagnostic-results-table tbody tr:last-child td {
    border-bottom: none;
}

.test-pass {
    background-color: #f7fcf8;
}

.test-fail {
    background-color: #fef7f7;
}

.test-critical.test-fail {
    background-color: #fcf0f0;
}

.test-status {
    white-space: nowrap;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-pass {
    background-color: #46b450;
}

.status-fail {
    background-color: #dc3232;
}

.test-details-row {
    background-color: #fbfbfb !important;
}

.test-details {
    padding: 0 !important;
}

.details-content {
    padding: 15px;
    overflow-x: auto;
}

.details-content pre {
    margin: 0;
    font-size: 12px;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
    white-space: pre-wrap;
}

.diagnostic-report-tabs {
    display: flex;
    border-bottom: 1px solid #ccd0d4;
    background-color: #f9f9f9;
}

.diagnostic-tab {
    padding: 10px 15px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #555;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.diagnostic-tab:hover {
    background-color: #f0f0f0;
    color: #135e96;
}

.diagnostic-tab.active {
    border-bottom-color: #2271b1;
    color: #2271b1;
}

.diagnostic-content {
    display: none;
}

.diagnostic-content.active {
    display: block;
}

.diagnostic-actions {
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    text-align: right;
}

.diagnostic-actions button {
    margin-left: 10px;
}

/* Payment gateway logs */
.payment-gateway-logs {
    margin: 20px 0;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.logs-header h3 {
    margin: 0;
}

.logs-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logs-container {
    background-color: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.log-entry {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry:hover {
    background-color: #f9f9f9;
}

.log-time {
    width: 140px;
    font-size: 12px;
    color: #888;
    flex-shrink: 0;
}

.log-gateway {
    width: 80px;
    flex-shrink: 0;
}

.log-gateway-paypal {
    color: #003087;
    font-weight: 600;
}

.log-gateway-stripe {
    color: #6772e5;
    font-weight: 600;
}

.log-message {
    flex-grow: 1;
    padding-right: 20px;
}

.log-level {
    width: 80px;
    text-align: center;
    flex-shrink: 0;
}

.log-level-info {
    color: #0073aa;
}

.log-level-warning {
    color: #ffb900;
}

.log-level-error {
    color: #dc3232;
}

.logs-actions {
    margin-top: 15px;
    text-align: right;
}

.no-logs-message {
    padding: 30px;
    text-align: center;
    color: #888;
}

/* Log entry expansions */
.log-entry-expandable {
    cursor: pointer;
}

.log-entry-expandable.expanded {
    background-color: #f9f9f9;
}

.log-entry-details {
    display: none;
    padding: 0 15px 15px 155px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
}

.log-entry-expandable.expanded + .log-entry-details {
    display: block;
}

.log-details-content {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
    overflow-x: auto;
}

/* Loading indicator */
.loading-indicator {
    text-align: center;
    padding: 30px;
    color: #888;
}
