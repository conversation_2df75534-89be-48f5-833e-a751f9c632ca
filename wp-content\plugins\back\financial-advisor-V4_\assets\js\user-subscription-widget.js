(function($) {
    'use strict';

    $(document).ready(function() {
        const form = $('#user-subscription-form');
        const messages = userSubscriptionAjax.messages;
        
        // Validazione durante la digitazione
        form.find('input, select').on('input change', function() {
            validateField($(this));
        });
        
        // Password strength meter
        const passwordInput = $('#password');
        passwordInput.on('input', function() {
            checkPasswordStrength($(this).val());
        });
        
        // Validazione della conferma password
        $('#confirm-password').on('input', function() {
            validateConfirmPassword();
        });
        
        // Invio del form
        form.on('submit', function(e) {
            e.preventDefault();
            
            // Valida tutti i campi
            let isValid = true;
            form.find('input, select').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });
            
            // Validazione specifica per la conferma password
            if (!validateConfirmPassword()) {
                isValid = false;
            }
            
            if (isValid) {
                submitForm();
            }
        });
        
        /**
         * Verifica della forza della password
         */
        function checkPasswordStrength(password) {
            const meter = $('.password-strength-meter');
            const strengthText = meter.find('.strength-text');
            
            // Rimuovi le classi precedenti
            meter.removeClass('weak medium strong');
            
            if (!password) {
                strengthText.text('');
                return;
            }
            
            // Regole per la forza della password
            const hasLowerCase = /[a-z]/.test(password);
            const hasUpperCase = /[A-Z]/.test(password);
            const hasNumber = /\d/.test(password);
            const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
            const minLength = password.length >= 8;
            
            const strength = (hasLowerCase ? 1 : 0) + 
                            (hasUpperCase ? 1 : 0) + 
                            (hasNumber ? 1 : 0) + 
                            (hasSpecial ? 1 : 0) + 
                            (minLength ? 1 : 0);
            
            // Aggiorna l'indicatore visivo
            if (strength <= 2) {
                meter.addClass('weak');
                strengthText.text('Debole');
            } else if (strength <= 3) {
                meter.addClass('medium');
                strengthText.text('Media');
            } else {
                meter.addClass('strong');
                strengthText.text('Forte');
            }
            
            return strength;
        }
        
        /**
         * Validazione generica dei campi
         */
        function validateField(field) {
            const id = field.attr('id');
            const value = field.val();
            const errorElement = $('#' + id + '-error');
            
            // Rimuovi classe di errore
            field.removeClass('error');
            errorElement.text('');
            
            // Controlla se è un campo obbligatorio e vuoto
            if (field.prop('required') && !value) {
                field.addClass('error');
                errorElement.text(messages.required);
                return false;
            }
            
            // Validazioni specifiche per tipo di campo
            switch (id) {
                case 'email':
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (value && !emailPattern.test(value)) {
                        field.addClass('error');
                        errorElement.text(messages.email_invalid);
                        return false;
                    }
                    break;
                    
                case 'password':
                    if (value) {
                        if (value.length < 8) {
                            field.addClass('error');
                            errorElement.text(messages.password_short);
                            return false;
                        }
                        
                        const strength = checkPasswordStrength(value);
                        if (strength < 3) {
                            field.addClass('error');
                            errorElement.text(messages.password_strength);
                            return false;
                        }
                    }
                    break;
                    
                case 'phone':
                    if (value) {
                        const phonePattern = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
                        if (!phonePattern.test(value)) {
                            field.addClass('error');
                            errorElement.text('Numero di telefono non valido');
                            return false;
                        }
                    }
                    break;
            }
            
            return true;
        }
        
        /**
         * Validazione della conferma password
         */
        function validateConfirmPassword() {
            const password = $('#password').val();
            const confirmPassword = $('#confirm-password');
            const confirmValue = confirmPassword.val();
            const errorElement = $('#confirm-password-error');
            
            // Rimuovi classe di errore
            confirmPassword.removeClass('error');
            errorElement.text('');
            
            if (!confirmValue) {
                confirmPassword.addClass('error');
                errorElement.text(messages.required);
                return false;
            }
            
            if (confirmValue !== password) {
                confirmPassword.addClass('error');
                errorElement.text(messages.password_match);
                return false;
            }
            
            return true;
        }
        
        /**
         * Invia i dati del form tramite AJAX
         */
        function submitForm() {
            const formData = new FormData();
            
            // Aggiungi tutti i campi del form
            formData.append('action', 'user_subscription_registration');
            formData.append('nonce', userSubscriptionAjax.nonce);
            formData.append('username', $('#username').val());
            formData.append('name', $('#name').val());
            formData.append('surname', $('#surname').val());
            formData.append('phone', $('#phone').val());
            formData.append('email', $('#email').val());
            formData.append('password', $('#password').val());
            formData.append('tipo_subscription', $('#tipo-subscription').val());
            
            // Disabilita il pulsante di invio
            const submitButton = form.find('.submit-button');
            const originalText = submitButton.text();
            submitButton.prop('disabled', true).text('Registrazione in corso...');
            
            // Nascondi messaggi precedenti
            $('.response-message').hide();
            
            $.ajax({
                url: userSubscriptionAjax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    const responseMessage = $('.response-message');
                    
                    if (response.success) {
                        // Reset del form
                        form[0].reset();
                        $('.password-strength-meter').removeClass('weak medium strong').find('.strength-text').text('');
                        
                        // Mostra messaggio di successo
                        responseMessage
                            .removeClass('error')
                            .addClass('success')
                            .text(response.data.message)
                            .show();
                    } else {
                        // Mostra messaggio di errore
                        responseMessage
                            .removeClass('success')
                            .addClass('error')
                            .text(response.data.message)
                            .show();
                    }
                },
                error: function() {
                    // Mostra messaggio di errore generico
                    $('.response-message')
                        .removeClass('success')
                        .addClass('error')
                        .text(messages.error)
                        .show();
                },
                complete: function() {
                    // Riabilita il pulsante di invio
                    submitButton.prop('disabled', false).text(originalText);
                }
            });
        }
    });
})(jQuery);
