<?php
/**
 * Simple PHPUnit Bootstrap file without <PERSON> Monkey
 * 
 * Provides basic WordPress function mocking for testing
 */

// Composer autoloader
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Define WordPress constants
if (!defined('ABSPATH')) {
    define('ABSPATH', '/wordpress/');
}

if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', ABSPATH . 'wp-content');
}

if (!defined('WP_PLUGIN_DIR')) {
    define('WP_PLUGIN_DIR', WP_CONTENT_DIR . '/plugins');
}

if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}

if (!defined('HOUR_IN_SECONDS')) {
    define('HOUR_IN_SECONDS', 3600);
}

if (!defined('DAY_IN_SECONDS')) {
    define('DAY_IN_SECONDS', 86400);
}

if (!defined('WEEK_IN_SECONDS')) {
    define('WEEK_IN_SECONDS', 604800);
}

if (!defined('MONTH_IN_SECONDS')) {
    define('MONTH_IN_SECONDS', 2629746);
}

if (!defined('YEAR_IN_SECONDS')) {
    define('YEAR_IN_SECONDS', 31556952);
}

// WordPress database constants
if (!defined('ARRAY_A')) {
    define('ARRAY_A', 'ARRAY_A');
}

if (!defined('ARRAY_N')) {
    define('ARRAY_N', 'ARRAY_N');
}

if (!defined('OBJECT')) {
    define('OBJECT', 'OBJECT');
}

if (!defined('OBJECT_K')) {
    define('OBJECT_K', 'OBJECT_K');
}

// Mock $_SERVER superglobal
$_SERVER['REMOTE_ADDR'] = '*************';
$_SERVER['HTTP_X_FORWARDED_FOR'] = '';
$_SERVER['HTTP_X_REAL_IP'] = '';
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REQUEST_URI'] = '/wp-admin/admin-ajax.php';

// Simple cache storage
class TestCache {
    private static $cache = [];
    
    public static function get($key, $group = 'default') {
        $cache_key = $group . ':' . $key;
        return isset(self::$cache[$cache_key]) ? self::$cache[$cache_key] : false;
    }
    
    public static function set($key, $data, $group = 'default', $expire = 0) {
        $cache_key = $group . ':' . $key;
        self::$cache[$cache_key] = $data;
        return true;
    }
    
    public static function delete($key, $group = 'default') {
        $cache_key = $group . ':' . $key;
        unset(self::$cache[$cache_key]);
        return true;
    }
    
    public static function flush() {
        self::$cache = [];
        return true;
    }
    
    public static function incr($key, $offset = 1, $group = 'default') {
        $cache_key = $group . ':' . $key;
        if (!isset(self::$cache[$cache_key])) {
            self::$cache[$cache_key] = 0;
        }
        self::$cache[$cache_key] += $offset;
        return self::$cache[$cache_key];
    }
    
    public static function clear() {
        self::$cache = [];
    }
}

// Simple options storage
class TestOptions {
    private static $options = [];
    
    public static function get($option, $default = false) {
        return isset(self::$options[$option]) ? self::$options[$option] : $default;
    }
    
    public static function update($option, $value) {
        self::$options[$option] = $value;
        return true;
    }
    
    public static function delete($option) {
        unset(self::$options[$option]);
        return true;
    }
    
    public static function clear() {
        self::$options = [];
    }
}

// WordPress cache functions
if (!function_exists('wp_cache_get')) {
    function wp_cache_get($key, $group = '') {
        return TestCache::get($key, $group);
    }
}

if (!function_exists('wp_cache_set')) {
    function wp_cache_set($key, $data, $group = '', $expire = 0) {
        return TestCache::set($key, $data, $group, $expire);
    }
}

if (!function_exists('wp_cache_delete')) {
    function wp_cache_delete($key, $group = '') {
        return TestCache::delete($key, $group);
    }
}

if (!function_exists('wp_cache_flush')) {
    function wp_cache_flush() {
        return TestCache::flush();
    }
}

if (!function_exists('wp_cache_add_global_groups')) {
    function wp_cache_add_global_groups($groups) {
        return true;
    }
}

if (!function_exists('wp_cache_incr')) {
    function wp_cache_incr($key, $offset = 1, $group = '') {
        return TestCache::incr($key, $offset, $group);
    }
}

if (!function_exists('wp_cache_flush_group')) {
    function wp_cache_flush_group($group) {
        return true;
    }
}

// WordPress options functions  
if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return TestOptions::get($option, $default);
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value) {
        return TestOptions::update($option, $value);
    }
}

if (!function_exists('delete_option')) {
    function delete_option($option) {
        return TestOptions::delete($option);
    }
}

// WordPress action/hook system mocks
if (!function_exists('add_action')) {
    function add_action($hook, $function_to_add, $priority = 10, $accepted_args = 1) {
        return true;
    }
}

if (!function_exists('remove_action')) {
    function remove_action($hook, $function_to_remove, $priority = 10) {
        return true;
    }
}

if (!function_exists('do_action')) {
    function do_action($hook, ...$args) {
        return true;
    }
}

if (!function_exists('apply_filters')) {
    function apply_filters($hook, $value, ...$args) {
        return $value;
    }
}

// WordPress scheduling functions
if (!function_exists('wp_next_scheduled')) {
    function wp_next_scheduled($hook, $args = []) {
        return false; // Return false to allow scheduling
    }
}

if (!function_exists('wp_schedule_event')) {
    function wp_schedule_event($timestamp, $recurrence, $hook, $args = []) {
        return true;
    }
}

if (!function_exists('wp_unschedule_event')) {
    function wp_unschedule_event($timestamp, $hook, $args = []) {
        return true;
    }
}

// WordPress time functions
if (!function_exists('current_time')) {
    function current_time($type, $gmt = 0) {
        if ($type === 'timestamp') {
            return time();
        }
        return date('Y-m-d H:i:s');
    }
}

// Basic WordPress functions
if (!function_exists('get_current_user_id')) {
    function get_current_user_id() {
        return 1;
    }
}

if (!function_exists('current_user_can')) {
    function current_user_can($capability) {
        return true;
    }
}

if (!function_exists('is_user_logged_in')) {
    function is_user_logged_in() {
        return true;
    }
}

if (!function_exists('wp_verify_nonce')) {
    function wp_verify_nonce($nonce, $action = -1) {
        return true;
    }
}

if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action = -1) {
        return 'test_nonce_12345';
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return $str;
    }
}

if (!function_exists('sanitize_email')) {
    function sanitize_email($email) {
        return $email;
    }
}

if (!function_exists('sanitize_key')) {
    function sanitize_key($key) {
        return $key;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return $text;
    }
}

if (!function_exists('esc_url')) {
    function esc_url($url) {
        return $url;
    }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) {
        return $text;
    }
}

if (!function_exists('wp_unslash')) {
    function wp_unslash($value) {
        return $value;
    }
}

if (!function_exists('wp_json_encode')) {
    function wp_json_encode($data, $options = 0, $depth = 512) {
        return json_encode($data, $options, $depth);
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('_e')) {
    function _e($text, $domain = 'default') {
        echo $text;
    }
}

if (!function_exists('esc_html__')) {
    function esc_html__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_attr__')) {
    function esc_attr__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return '/path/to/plugin/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://localhost/wp-content/plugins/plugin/';
    }
}

if (!function_exists('wp_mail')) {
    function wp_mail($to, $subject, $message, $headers = '', $attachments = []) {
        return true;
    }
}

if (!function_exists('wp_safe_remote_get')) {
    function wp_safe_remote_get($url, $args = []) {
        return ['body' => '{"status":"ok"}'];
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return '{"status":"ok"}';
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('check_ajax_referer')) {
    function check_ajax_referer($action = -1, $query_arg = false, $die = true) {
        return true;
    }
}

// Test cleanup function
register_shutdown_function(function() {
    TestCache::clear();
    TestOptions::clear();
    global $wpdb;
    if (method_exists($wpdb, 'reset')) {
        $wpdb->reset();
    }
});

// Global mock $wpdb object with enhanced functionality
global $wpdb;
$wpdb = new class {
    public $prefix = 'wp_';
    public $posts = 'wp_posts';
    public $options = 'wp_options';
    public $usermeta = 'wp_usermeta';
    public $users = 'wp_users';
    
    // Storage for query tracking
    public $queries = [];
    public $insert_id = 1;
    public $rows_affected = 1;
    public $last_result = [];
    public $last_query = '';
    public $last_error = '';
    
    // Method call tracking
    public $prepare_calls = [];
    public $query_calls = [];
    public $get_results_calls = [];
    public $get_var_calls = [];
    public $insert_calls = [];
      public function prepare($query, ...$args) {
        $this->prepare_calls[] = ['query' => $query, 'args' => $args];
        
        // Handle the case where prepare() is called with an array as second argument
        if (count($args) === 1 && is_array($args[0])) {
            $args = $args[0];
        }
        
        return sprintf($query, ...$args);
    }
    
    public function query($query) {
        $this->query_calls[] = $query;
        $this->last_query = $query;
        $this->queries[] = $query;
        
        // Simulate successful database operations
        if (strpos($query, 'INSERT') !== false) {
            $this->insert_id++;
            $this->rows_affected = 1;
            return 1;
        }
        
        if (strpos($query, 'CREATE TABLE') !== false) {
            $this->rows_affected = 0;
            return true;
        }
        
        $this->rows_affected = 1;
        return 1;
    }
      public function get_results($query, $output = OBJECT) {
        $this->get_results_calls[] = ['query' => $query, 'output' => $output];
        $this->last_query = $query;
        
        // Return mock results based on query type
        if (strpos($query, 'SELECT') !== false) {
            if (strpos($query, 'COUNT') !== false) {
                // For statistics queries - return as requested format
                $results = [
                    ['error_type' => 'API_ERROR', 'count' => '5'],
                    ['error_type' => 'DATABASE_ERROR', 'count' => '3'], 
                    ['error_type' => 'VALIDATION_ERROR', 'count' => '2']
                ];
                
                if ($output === OBJECT) {
                    return array_map(function($row) { return (object) $row; }, $results);
                }
                return $results;
            } else {
                // For regular error retrieval - return as requested format
                $results = [
                    [
                        'id' => '1',
                        'message' => 'Test error message',
                        'type' => 'API_ERROR',
                        'level' => 'ERROR',
                        'context' => '{"user_id":123}',
                        'ip_address' => '*************',
                        'user_agent' => 'Test User Agent',
                        'timestamp' => time(),
                        'created_at' => '2024-01-01 12:00:00'
                    ]
                ];
                
                if ($output === OBJECT) {
                    return array_map(function($row) { return (object) $row; }, $results);
                }
                return $results;
            }
        }
        
        return [];
    }
    
    public function get_var($query, $x = 0, $y = 0) {
        $this->get_var_calls[] = ['query' => $query, 'x' => $x, 'y' => $y];
        $this->last_query = $query;
        
        // Return mock values based on query
        if (strpos($query, 'COUNT') !== false) {
            return '10'; // Mock count
        }
        
        return '1';
    }
    
    public function insert($table, $data, $format = null) {
        $this->insert_calls[] = ['table' => $table, 'data' => $data, 'format' => $format];
        $this->insert_id++;
        $this->rows_affected = 1;
        return 1;
    }
    
    public function get_charset_collate() {
        return 'DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci';
    }
    
    public function esc_like($text) {
        return addcslashes($text, '\\_%');
    }
    
    public function _real_escape($string) {
        return addslashes($string);
    }
    
    // Reset method for tests
    public function reset() {
        $this->queries = [];
        $this->prepare_calls = [];
        $this->query_calls = [];
        $this->get_results_calls = [];
        $this->get_var_calls = [];
        $this->insert_calls = [];
        $this->insert_id = 1;
        $this->rows_affected = 1;
        $this->last_result = [];
        $this->last_query = '';
        $this->last_error = '';
    }
};
