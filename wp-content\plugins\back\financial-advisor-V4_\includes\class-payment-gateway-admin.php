<?php
/**
 * Payment Gateway Admin
 *
 * Admin interface for managing PayPal and Stripe payment gateway configurations
 *
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Ensure WordPress functions are available (only if in WordPress environment)
if (defined('ABSPATH') && !function_exists('add_submenu_page') && file_exists(ABSPATH . 'wp-admin/includes/admin.php')) {
    require_once(ABSPATH . 'wp-admin/includes/admin.php');
}

if (defined('ABSPATH') && !function_exists('wp_remote_post') && file_exists(ABSPATH . WPINC . '/http.php')) {
    require_once(ABSPATH . WPINC . '/http.php');
}

if (!function_exists('dbDelta') && defined('ABSPATH')) {
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
}

// Ensure all required WordPress functions
if (!function_exists('wp_send_json_success')) {
    function wp_send_json_success($data = null, $status_code = null) {
        wp_send_json($data, $status_code);
    }
}

if (!function_exists('wp_send_json_error')) {
    function wp_send_json_error($data = null, $status_code = null) {
        wp_send_json($data, $status_code);
    }
}

if (!function_exists('wp_send_json')) {
    function wp_send_json($response, $status_code = null) {
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
            if ($status_code) {
                http_response_code($status_code);
            }
        }
        echo wp_json_encode($response);
        wp_die();
    }
}

if (!function_exists('wp_json_encode')) {
    function wp_json_encode($data, $options = 0, $depth = 512) {
        return json_encode($data, $options, $depth);
    }
}

// Additional WordPress function stubs for standalone operation
if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('_e')) {
    function _e($text, $domain = 'default') {
        echo __($text, $domain);
    }
}

if (!function_exists('_n')) {
    function _n($single, $plural, $number, $domain = 'default') {
        return $number === 1 ? $single : $plural;
    }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim(stripslashes(strip_tags($str)));
    }
}

if (!function_exists('sanitize_textarea_field')) {
    function sanitize_textarea_field($str) {
        return sanitize_text_field($str);
    }
}

if (!function_exists('current_user_can')) {
    function current_user_can($capability) {
        return true; // For standalone operation, assume admin access
    }
}

if (!function_exists('get_current_user_id')) {
    function get_current_user_id() {
        return 1; // Default admin user ID
    }
}

if (!function_exists('wp_verify_nonce')) {
    function wp_verify_nonce($nonce, $action = -1) {
        return true; // For development, always verify
    }
}

if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action = -1) {
        return 'dev_nonce_' . md5($action . time());
    }
}

if (!function_exists('admin_url')) {
    function admin_url($path = '', $scheme = 'admin') {
        return home_url($path);
    }
}

if (!function_exists('home_url')) {
    function home_url($path = '', $scheme = null) {
        $url = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $url .= $_SERVER['HTTP_HOST'] ?? 'localhost';
        return rtrim($url, '/') . '/' . ltrim($path, '/');
    }
}

if (!function_exists('is_ssl')) {
    function is_ssl() {
        return isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    }
}

if (!function_exists('get_bloginfo')) {
    function get_bloginfo($show = '') {
        if ($show === 'version') {
            return '6.0'; // Default WordPress version
        }
        return 'WordPress Site';
    }
}

if (!function_exists('get_template')) {
    function get_template() {
        return 'default-theme';
    }
}

if (!function_exists('is_admin')) {
    function is_admin() {
        return strpos($_SERVER['REQUEST_URI'] ?? '', '/wp-admin/') !== false;
    }
}

if (!function_exists('wp_die')) {
    function wp_die($message = '', $title = '', $args = array()) {
        if (is_array($message)) {
            $message = implode(', ', $message);
        }
        die($message);
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return home_url('/wp-content/plugins/');
    }
}

if (!function_exists('wp_enqueue_script')) {
    function wp_enqueue_script($handle, $src = '', $deps = array(), $ver = false, $in_footer = false) {
        // Stub function for development
    }
}

if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src = '', $deps = array(), $ver = false, $media = 'all') {
        // Stub function for development
    }
}

if (!function_exists('wp_localize_script')) {
    function wp_localize_script($handle, $object_name, $l10n) {
        // Stub function for development
    }
}

if (!function_exists('wp_nonce_field')) {
    function wp_nonce_field($action = -1, $name = '_wpnonce', $referer = true, $echo = true) {
        $nonce = wp_create_nonce($action);
        $field = '<input type="hidden" id="' . $name . '" name="' . $name . '" value="' . $nonce . '" />';
        if ($echo) {
            echo $field;
        }
        return $field;
    }
}

if (!function_exists('selected')) {
    function selected($selected, $current = true, $echo = true) {
        $result = ($selected == $current) ? ' selected="selected"' : '';
        if ($echo) {
            echo $result;
        }
        return $result;
    }
}

if (!function_exists('checked')) {
    function checked($checked, $current = true, $echo = true) {
        $result = ($checked == $current) ? ' checked="checked"' : '';
        if ($echo) {
            echo $result;
        }
        return $result;
    }
}

if (!function_exists('register_setting')) {
    function register_setting($option_group, $option_name, $args = '') {
        // Stub function for development
    }
}

if (!function_exists('rest_sanitize_boolean')) {
    function rest_sanitize_boolean($value) {
        return (bool) $value;
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return $thing instanceof WP_Error;
    }
}

if (!class_exists('WP_Error')) {
    class WP_Error {
        public $errors = array();
        public $error_data = array();

        public function __construct($code = '', $message = '', $data = '') {
            if (!empty($code)) {
                $this->errors[$code][] = $message;
            }
            if (!empty($data)) {
                $this->error_data[$code] = $data;
            }
        }

        public function get_error_message($code = '') {
            if (empty($code)) {
                $code = $this->get_error_code();
            }
            if (isset($this->errors[$code])) {
                return $this->errors[$code][0];
            }
            return '';
        }

        public function get_error_code() {
            if (empty($this->errors)) {
                return '';
            }
            return array_keys($this->errors)[0];
        }
    }
}

if (!function_exists('wp_remote_post')) {
    function wp_remote_post($url, $args = array()) {
        $ch = curl_init();
        if (!$ch) {
            return new WP_Error('http_request_failed', 'Could not initialize cURL');
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $args['timeout'] ?? 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development

        if (isset($args['headers'])) {
            $headers = array();
            foreach ($args['headers'] as $key => $value) {
                $headers[] = $key . ': ' . $value;
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        if (isset($args['body'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $args['body']);
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($curl_error)) {
            return new WP_Error('http_request_failed', $curl_error ?: 'HTTP request failed');
        }

        return array(
            'body' => $response,
            'response' => array('code' => $http_code),
            'headers' => array(),
            'cookies' => array()
        );
    }
}

if (!function_exists('wp_remote_get')) {
    function wp_remote_get($url, $args = array()) {
        $ch = curl_init();
        if (!$ch) {
            return new WP_Error('http_request_failed', 'Could not initialize cURL');
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $args['timeout'] ?? 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development

        if (isset($args['headers'])) {
            $headers = array();
            foreach ($args['headers'] as $key => $value) {
                $headers[] = $key . ': ' . $value;
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($curl_error)) {
            return new WP_Error('http_request_failed', $curl_error ?: 'HTTP request failed');
        }

        return array(
            'body' => $response,
            'response' => array('code' => $http_code),
            'headers' => array(),
            'cookies' => array()
        );
    }
}

if (!function_exists('add_submenu_page')) {
    function add_submenu_page($parent_slug, $page_title, $menu_title, $capability, $menu_slug, $function = '') {
        // Stub function for development
        return $menu_slug;
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return is_array($response) ? $response['body'] : '';
    }
}

if (!function_exists('wp_remote_response_code')) {
    function wp_remote_response_code($response) {
        return is_array($response) && isset($response['response']['code']) ? $response['response']['code'] : 0;
    }
}

if (!function_exists('dbDelta')) {
    function dbDelta($queries = '', $execute = true) {
        global $wpdb;
        if (!$wpdb) {
            return array();
        }

        if (is_string($queries)) {
            $queries = array($queries);
        }

        $results = array();
        foreach ($queries as $query) {
            if ($execute) {
                $result = $wpdb->query($query);
                $results[] = $result;
            }
        }

        return $results;
    }
}

class Payment_Gateway_Admin {/**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'register_admin_assets'));

        // AJAX handlers for gateway configuration
        add_action('wp_ajax_save_paypal_config', array($this, 'save_paypal_config'));
        add_action('wp_ajax_save_stripe_config', array($this, 'save_stripe_config'));
        add_action('wp_ajax_test_paypal_config', array($this, 'test_paypal_config'));
        add_action('wp_ajax_test_stripe_config', array($this, 'test_stripe_config'));

        // AJAX handlers for debugging and testing - Enhanced
        add_action('wp_ajax_run_gateway_checklist', array($this, 'run_gateway_checklist'));
        add_action('wp_ajax_test_gateway_connection', array($this, 'test_gateway_connection'));
        add_action('wp_ajax_simulate_payment_error', array($this, 'simulate_payment_error'));
        add_action('wp_ajax_get_error_logs', array($this, 'get_error_logs'));
        add_action('wp_ajax_clear_error_logs', array($this, 'clear_error_logs'));
        add_action('wp_ajax_export_debug_report', array($this, 'export_debug_report'));

        // New enhanced AJAX handlers for advanced features
        add_action('wp_ajax_validate_gateway_settings', array($this, 'validate_gateway_settings'));
        add_action('wp_ajax_test_webhook_endpoints', array($this, 'test_webhook_endpoints'));
        add_action('wp_ajax_run_performance_test', array($this, 'run_performance_test'));
        add_action('wp_ajax_stress_test_gateways', array($this, 'stress_test_gateways'));
        add_action('wp_ajax_get_system_status', array($this, 'get_system_status'));
        add_action('wp_ajax_auto_fix_errors', array($this, 'auto_fix_errors'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Initialize error monitoring
        add_action('init', array($this, 'init_error_monitoring'));

        // Initialize AJAX handlers
        $this->init_ajax_handlers();    }
    
    /**
     * Initialize AJAX handlers for payment gateway functionality
     */
    public function init_ajax_handlers() {
        // Register AJAX actions for payment gateway testing and logs
        add_action('wp_ajax_run_gateway_tests', array($this, 'run_gateway_tests'));
        add_action('wp_ajax_get_payment_logs', array($this, 'get_payment_logs'));
        add_action('wp_ajax_clear_payment_logs', array($this, 'clear_payment_logs'));
    }

    /**
     * Add admin menu items
     */
    public function add_admin_menu() {
        // Get main menu slug from Financial_Advisor_Menu_Manager
        $main_menu_slug = 'document-viewer-settings'; // Default value

        // Try to get the actual slug from the Menu Manager if available
        if (class_exists('Financial_Advisor_Menu_Manager')) {
            $menu_manager = Financial_Advisor_Menu_Manager::get_instance();
            if (property_exists($menu_manager, 'main_menu_slug')) {
                $main_menu_slug = $menu_manager->main_menu_slug;
            }
        }
          // Add submenu page for payment gateways
        add_submenu_page(
            $main_menu_slug,
            __('Payment Gateways', 'document-viewer-plugin'),
            __('Payment Gateways', 'document-viewer-plugin'),
            'manage_options',
            'payment-gateways',
            array($this, 'render_payment_gateways_page')
        );        // Note: Gateway Testing is now integrated as sub-tabs within the main Payment Gateway page
    }    /**
     * Register admin assets
     */    public function register_admin_assets($hook) {
        // Only load on our admin pages
        if (!isset($_GET['page']) || $_GET['page'] !== 'payment-gateways') {
            return;
        }

        wp_enqueue_style(
            'payment-gateway-admin-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/payment-gateway-admin.css',
            array(),
            '1.0.0'
        );

        // Enqueue error monitoring styles
        wp_enqueue_style(
            'error-monitoring-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/error-monitoring-styles.css',
            array(),
            '1.0.0'
        );
        
        // Enqueue enhanced payment gateway testing styles
        wp_enqueue_style(
            'enhanced-payment-gateway-testing-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/enhanced-payment-gateway-testing.css',
            array(),
            '1.0.0'
        );

        // Optional: Enqueue Chart.js for visualizations
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js',
            array(),
            '3.7.1',
            true
        );

        wp_enqueue_script(
            'payment-gateway-admin-js',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/payment-gateway-admin.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // Enqueue enhanced payment gateway testing script
        wp_enqueue_script(
            'enhanced-payment-gateway-testing-js',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-payment-gateway-testing.js',
            array('jquery', 'chartjs'),
            '1.0.0',
            true
        );

        // Enqueue the subscriber management widget for debugging features
        wp_enqueue_script(
            'subscriber-management-widget',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/subscriber-management-widget.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('payment-gateway-admin-js', 'paymentGatewayAjax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payment_gateway_nonce'),
            'messages' => array(
                'save_success' => __('Configuration saved successfully!', 'document-viewer-plugin'),
                'save_error' => __('Error saving configuration.', 'document-viewer-plugin'),
                'test_success' => __('Connection test successful!', 'document-viewer-plugin'),
                'test_error' => __('Connection test failed.', 'document-viewer-plugin'),
                'confirm_delete' => __('Are you sure you want to delete this configuration?', 'document-viewer-plugin'),
                'checklist_running' => __('Running payment gateway checklist...', 'document-viewer-plugin'),
                'checklist_success' => __('Checklist completed successfully!', 'document-viewer-plugin'),
                'checklist_error' => __('Checklist encountered errors.', 'document-viewer-plugin'),
                'test_running' => __('Running connection test...', 'document-viewer-plugin'),
                'export_success' => __('Debug report exported successfully!', 'document-viewer-plugin'),
                'logs_cleared' => __('Error logs cleared successfully!', 'document-viewer-plugin')
            )
        ));
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // PayPal settings
        register_setting('payment_gateway_settings', 'paypal_client_id', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'paypal_client_secret', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'paypal_environment', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'paypal_webhook_id', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'paypal_is_active', 'rest_sanitize_boolean');

        // Stripe settings
        register_setting('payment_gateway_settings', 'stripe_public_key', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'stripe_secret_key', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'stripe_environment', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'stripe_webhook_secret', 'sanitize_text_field');
        register_setting('payment_gateway_settings', 'stripe_is_active', 'rest_sanitize_boolean');
    }

    /**
     * Render payment gateways admin page
     */
    public function render_payment_gateways_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Get current configurations
        $paypal_config = $this->get_paypal_config();
        $stripe_config = $this->get_stripe_config();
        ?>        <div class="wrap">
            <h1><?php _e('Payment Gateway Configuration', 'document-viewer-plugin'); ?></h1>

            <div class="payment-gateway-container">
                <div class="nav-tab-wrapper" role="tablist" aria-label="<?php _e('Payment Gateway Configuration Tabs', 'document-viewer-plugin'); ?>">
                    <button type="button" data-tab="paypal-tab" class="nav-tab nav-tab-active" role="tab" aria-controls="paypal-tab" aria-selected="true" tabindex="0"><?php _e('PayPal', 'document-viewer-plugin'); ?></button>
                    <button type="button" data-tab="stripe-tab" class="nav-tab" role="tab" aria-controls="stripe-tab" aria-selected="false" tabindex="-1"><?php _e('Stripe', 'document-viewer-plugin'); ?></button>
                    <button type="button" data-tab="testing-tab" class="nav-tab" role="tab" aria-controls="testing-tab" aria-selected="false" tabindex="-1"><?php _e('Testing & Debug', 'document-viewer-plugin'); ?></button>
                    <button type="button" data-tab="checklist-tab" class="nav-tab" role="tab" aria-controls="checklist-tab" aria-selected="false" tabindex="-1"><?php _e('System Checklist', 'document-viewer-plugin'); ?></button>
                    <button type="button" data-tab="monitoring-tab" class="nav-tab" role="tab" aria-controls="monitoring-tab" aria-selected="false" tabindex="-1"><?php _e('Error Monitoring', 'document-viewer-plugin'); ?></button>
                </div>

                <div class="tab-content-wrapper">

                <!-- PayPal Configuration Tab -->
                <div id="paypal-tab" class="tab-content active" role="tabpanel" aria-labelledby="paypal-tab-button">
                    <div class="gateway-config-section">
                        <h2><?php _e('PayPal Configuration', 'document-viewer-plugin'); ?></h2>
                        <form id="paypal-config-form" method="post">
                            <?php wp_nonce_field('payment_gateway_nonce', 'paypal_nonce'); ?>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Environment', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <select name="paypal_environment" id="paypal_environment">
                                            <option value="sandbox" <?php selected($paypal_config['environment'], 'sandbox'); ?>><?php _e('Sandbox', 'document-viewer-plugin'); ?></option>
                                            <option value="live" <?php selected($paypal_config['environment'], 'live'); ?>><?php _e('Live', 'document-viewer-plugin'); ?></option>
                                        </select>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Client ID', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="text" name="paypal_client_id" id="paypal_client_id"
                                               value="<?php echo esc_attr($paypal_config['client_id']); ?>"
                                               class="regular-text" />
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Client Secret', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="password" name="paypal_client_secret" id="paypal_client_secret"
                                               value="<?php echo esc_attr($paypal_config['client_secret']); ?>"
                                               class="regular-text" />
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Webhook ID', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="text" name="paypal_webhook_id" id="paypal_webhook_id"
                                               value="<?php echo esc_attr($paypal_config['webhook_id']); ?>"
                                               class="regular-text" />
                                        <p class="description"><?php _e('Optional: Used for webhook verification', 'document-viewer-plugin'); ?></p>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Active', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="paypal_is_active" id="paypal_is_active"
                                                   value="1" <?php checked($paypal_config['is_active'], 1); ?> />
                                            <?php _e('Enable PayPal payments', 'document-viewer-plugin'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>

                            <p class="submit">
                                <button type="button" id="test-paypal-config" class="button"><?php _e('Test Connection', 'document-viewer-plugin'); ?></button>
                                <button type="submit" id="save-paypal-config" class="button-primary"><?php _e('Save PayPal Configuration', 'document-viewer-plugin'); ?></button>
                            </p>
                        </form>
                    </div>
                </div>

                <!-- Stripe Configuration Tab -->
                <div id="stripe-tab" class="tab-content" role="tabpanel" aria-labelledby="stripe-tab-button">
                    <div class="gateway-config-section">
                        <h2><?php _e('Stripe Configuration', 'document-viewer-plugin'); ?></h2>
                        <form id="stripe-config-form" method="post">
                            <?php wp_nonce_field('payment_gateway_nonce', 'stripe_nonce'); ?>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Environment', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <select name="stripe_environment" id="stripe_environment">
                                            <option value="test" <?php selected($stripe_config['environment'], 'test'); ?>><?php _e('Test', 'document-viewer-plugin'); ?></option>
                                            <option value="live" <?php selected($stripe_config['environment'], 'live'); ?>><?php _e('Live', 'document-viewer-plugin'); ?></option>
                                        </select>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Publishable Key', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="text" name="stripe_public_key" id="stripe_public_key"
                                               value="<?php echo esc_attr($stripe_config['public_key']); ?>"
                                               class="regular-text" />
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Secret Key', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="password" name="stripe_secret_key" id="stripe_secret_key"
                                               value="<?php echo esc_attr($stripe_config['secret_key']); ?>"
                                               class="regular-text" />
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Webhook Endpoint Secret', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <input type="password" name="stripe_webhook_secret" id="stripe_webhook_secret"
                                               value="<?php echo esc_attr($stripe_config['webhook_endpoint_secret']); ?>"
                                               class="regular-text" />
                                        <p class="description"><?php _e('Optional: Used for webhook signature verification', 'document-viewer-plugin'); ?></p>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row"><?php _e('Active', 'document-viewer-plugin'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="stripe_is_active" id="stripe_is_active"
                                                   value="1" <?php checked($stripe_config['is_active'], 1); ?> />
                                            <?php _e('Enable Stripe payments', 'document-viewer-plugin'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                              <p class="submit">
                                <button type="button" id="test-stripe-config" class="button"><?php _e('Test Connection', 'document-viewer-plugin'); ?></button>
                                <button type="submit" id="save-stripe-config" class="button-primary"><?php _e('Save Stripe Configuration', 'document-viewer-plugin'); ?></button>
                            </p>
                        </form>
                    </div>
                </div>                <!-- Testing & Debug Tab -->
                <div id="testing-tab" class="tab-content" role="tabpanel" aria-labelledby="testing-tab-button">
                    <div class="gateway-config-section">
                        <h2><?php _e('Enhanced Payment Gateway Testing & Debugging', 'document-viewer-plugin'); ?></h2>
                        
                        <div class="notice notice-info">
                            <p><strong><?php _e('Advanced Diagnostic Tool:', 'document-viewer-plugin'); ?></strong> <?php _e('This interface provides comprehensive testing of all payment gateway backend functions with real-time feedback and monitoring.', 'document-viewer-plugin'); ?></p>
                        </div>

                        <!-- Sub-Tab Navigation for Testing & Debug -->
                        <div class="testing-sub-tabs">
                            <div class="nav-tab-wrapper">
                                <button type="button" data-testing-tab="automatic-tests" class="nav-tab nav-tab-active" role="tab" aria-controls="automatic-tests" aria-selected="true"><?php _e('Test Automatici', 'document-viewer-plugin'); ?></button>
                                <button type="button" data-testing-tab="manual-tests" class="nav-tab" role="tab" aria-controls="manual-tests" aria-selected="false"><?php _e('Test Manuali', 'document-viewer-plugin'); ?></button>
                                <button type="button" data-testing-tab="monitoring" class="nav-tab" role="tab" aria-controls="monitoring" aria-selected="false"><?php _e('Monitoraggio', 'document-viewer-plugin'); ?></button>
                                <button type="button" data-testing-tab="logs-errors" class="nav-tab" role="tab" aria-controls="logs-errors" aria-selected="false"><?php _e('Log & Errori', 'document-viewer-plugin'); ?></button>
                            </div>

                            <!-- Test Automatici Sub-Tab -->
                            <div id="automatic-tests" class="testing-sub-tab-content active">
                                <h3><?php _e('🔍 Enhanced System Diagnostics', 'document-viewer-plugin'); ?></h3>
                                <div id="automatic-test-results" class="automatic-test-results">                                    <?php
                                    // Run enhanced tests using our test class
                                    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
                                    $test_runner = new Payment_Gateway_Test();
                                    $test_results = $test_runner->run_enhanced_payment_gateway_tests();
                                    $test_runner->display_enhanced_test_results($test_results);
                                    ?>
                                    <div class="automatic-test-actions">
                                        <button type="button" id="refresh-automatic-tests" class="button button-primary"><?php _e('🔄 Refresh Tests', 'document-viewer-plugin'); ?></button>
                                        <button type="button" id="export-test-results" class="button"><?php _e('📄 Export Results', 'document-viewer-plugin'); ?></button>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Manuali Sub-Tab -->
                            <div id="manual-tests" class="testing-sub-tab-content">
                                <div class="postbox">
                                    <div class="postbox-header">
                                        <h3 class="hndle"><?php _e('🧪 Live Payment Gateway Test Interface', 'document-viewer-plugin'); ?></h3>
                                    </div>
                                    <div class="inside">
                                        <div id="gateway-test-container">                                            <!-- Manual testing interface -->
                                            <div class="enhanced-test-interface">
                                                <div class="gateway-testing-controls">
                                                    <h4><?php _e('Test PayPal API', 'document-viewer-plugin'); ?></h4>
                                                    <div class="test-action-buttons">
                                                        <button type="button" id="test-paypal-auth" class="button button-secondary" data-gateway="paypal" data-test="auth">
                                                            <?php _e('Test Authentication', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-paypal-webhook" class="button button-secondary" data-gateway="paypal" data-test="webhook">
                                                            <?php _e('Test Webhook', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-paypal-payment" class="button button-secondary" data-gateway="paypal" data-test="payment">
                                                            <?php _e('Test Payment', 'document-viewer-plugin'); ?>
                                                        </button>
                                                    </div>
                                                    
                                                    <h4><?php _e('Test Stripe API', 'document-viewer-plugin'); ?></h4>
                                                    <div class="test-action-buttons">
                                                        <button type="button" id="test-stripe-auth" class="button button-secondary" data-gateway="stripe" data-test="auth">
                                                            <?php _e('Test Authentication', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-stripe-webhook" class="button button-secondary" data-gateway="stripe" data-test="webhook">
                                                            <?php _e('Test Webhook', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-stripe-payment" class="button button-secondary" data-gateway="stripe" data-test="payment">
                                                            <?php _e('Test Payment', 'document-viewer-plugin'); ?>
                                                        </button>
                                                    </div>
                                                    
                                                    <h4><?php _e('Advanced Testing', 'document-viewer-plugin'); ?></h4>
                                                    <div class="test-action-buttons">
                                                        <button type="button" id="test-error-handling" class="button button-secondary">
                                                            <?php _e('Test Error Handling', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-subscription" class="button button-secondary">
                                                            <?php _e('Test Subscription', 'document-viewer-plugin'); ?>
                                                        </button>
                                                        <button type="button" id="test-refund" class="button button-secondary">
                                                            <?php _e('Test Refund', 'document-viewer-plugin'); ?>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <div class="test-result-container">
                                                    <h4><?php _e('Test Results', 'document-viewer-plugin'); ?></h4>
                                                    <div id="test-result-output" class="test-result-output">
                                                        <div class="notice notice-info">
                                                            <p><?php _e('Select a test to run from the options above.', 'document-viewer-plugin'); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                                <div class="testing-tools-grid">
                                                    <div class="testing-tool-card">
                                                        <h3><?php _e('🔧 Connection Testing', 'document-viewer-plugin'); ?></h3>
                                                        <p><?php _e('Test live connections to payment gateways and verify API responses.', 'document-viewer-plugin'); ?></p>
                                                        <button type="button" id="test-all-gateways" class="button button-primary"><?php _e('Test All Gateways', 'document-viewer-plugin'); ?></button>
                                                        <button type="button" id="test-webhook-endpoints" class="button"><?php _e('Test Webhooks', 'document_viewer-plugin'); ?></button>
                                                    </div>

                                                    <div class="testing-tool-card">
                                                        <h3><?php _e('🐛 Error Simulation', 'document-viewer-plugin'); ?></h3>
                                                        <p><?php _e('Simulate various payment errors to test error handling mechanisms.', 'document-viewer-plugin'); ?></p>
                                                        <button type="button" id="simulate-timeout" class="button"><?php _e('Simulate Timeout', 'document_viewer-plugin'); ?></button>
                                                        <button type="button" id="simulate-api-error" class="button"><?php _e('Simulate API Error', 'document_viewer-plugin'); ?></button>
                                                        <button type="button" id="simulate-network-error" class="button"><?php _e('Simulate Network Error', 'document_viewer-plugin'); ?></button>
                                                    </div>

                                                    <div class="testing-tool-card">
                                                        <h3><?php _e('📊 Performance Testing', 'document-viewer-plugin'); ?></h3>
                                                        <p><?php _e('Monitor response times and system performance under load.', 'document-viewer-plugin'); ?></p>
                                                        <button type="button" id="run-performance-test" class="button"><?php _e('Run Performance Test', 'document-viewer-plugin'); ?></button>
                                                        <button type="button" id="stress-test-gateways" class="button"><?php _e('Stress Test', 'document_viewer-plugin'); ?></button>
                                                    </div>

                                                    <div class="testing-tool-card">
                                                        <h3><?php _e('📋 Debug Console', 'document-viewer-plugin'); ?></h3>
                                                        <p><?php _e('Access advanced debugging tools and real-time monitoring.', 'document-viewer-plugin'); ?></p>
                                                        <button type="button" id="open-debug-console" class="button button-secondary"><?php _e('Open Debug Panel', 'document_viewer-plugin'); ?></button>
                                                        <button type="button" id="export-debug-report" class="button"><?php _e('Export Report', 'document-viewer-plugin'); ?></button>
                                                    </div>                                                </div>
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <div id="testing-results" class="testing-results-panel" style="display: none;">
                                    <h3><?php _e('Test Results', 'document-viewer-plugin'); ?></h3>
                                    <div id="testing-output" class="testing-output"></div>
                                </div>
                            </div>

                            <!-- Monitoraggio Sub-Tab -->
                            <div id="monitoring" class="testing-sub-tab-content">
                                <div class="postbox">
                                    <div class="postbox-header">
                                        <h3 class="hndle"><?php _e('📊 Real-time System Monitor', 'document-viewer-plugin'); ?></h3>
                                    </div>
                                    <div class="inside">
                                        <div id="system-monitor">
                                            <div class="monitor-grid">
                                                <div class="monitor-item">
                                                    <strong><?php _e('AJAX Status:', 'document-viewer-plugin'); ?></strong> <span id="ajax-status"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>
                                                </div>
                                                <div class="monitor-item">
                                                    <strong><?php _e('Widget Status:', 'document-viewer-plugin'); ?></strong> <span id="widget-status"><?php _e('Checking...', 'document-viewer-plugin'); ?></span>
                                                </div>
                                                <div class="monitor-item">
                                                    <strong><?php _e('Database Status:', 'document-viewer-plugin'); ?></strong> <span id="db-status"><?php _e('Verifying...', 'document-viewer-plugin'); ?></span>
                                                </div>
                                                <div class="monitor-item">
                                                    <strong><?php _e('Gateway Connection:', 'document-viewer-plugin'); ?></strong> <span id="gateway-status"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>
                                                </div>
                                            </div>
                                            <div class="monitor-actions">
                                                <button id="refresh-monitor" class="button"><?php _e('🔄 Refresh Status', 'document_viewer-plugin'); ?></button>
                                                <button id="start-monitoring" class="button button-primary"><?php _e('▶️ Start Monitoring', 'document_viewer-plugin'); ?></button>
                                                <button id="stop-monitoring" class="button"><?php _e('⏹️ Stop Monitoring', 'document_viewer-plugin'); ?></button>
                                            </div>
                                            <div id="monitoring-log" class="monitoring-log">
                                                <h4><?php _e('Monitoring Log:', 'document-viewer-plugin'); ?></h4>
                                                <div id="monitoring-output" class="monitoring-output"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Log & Errori Sub-Tab -->
                            <div id="logs-errors" class="testing-sub-tab-content">
                                <div class="postbox">
                                    <div class="postbox-header">
                                        <h3 class="hndle"><?php _e('📋 Log & Errori', 'document-viewer-plugin'); ?></h3>
                                    </div>
                                    <div class="inside">
                                        <div class="log-controls">
                                            <button type="button" id="refresh-logs" class="button"><?php _e('🔄 Refresh Logs', 'document-viewer-plugin'); ?></button>
                                            <button type="button" id="clear-logs" class="button"><?php _e('🗑️ Clear Logs', 'document-viewer-plugin'); ?></button>
                                            <button type="button" id="export-logs" class="button button-primary"><?php _e('📄 Export Logs', 'document-viewer-plugin'); ?></button>
                                        </div>
                                        <div id="test-feedback-area-log" class="test-feedback-container">
                                            <div class="notice notice-info">
                                                <p><?php _e('Qui verranno mostrati i log e gli errori generati durante i test manuali e automatici.', 'document-viewer-plugin'); ?></p>
                                            </div>
                                            <div id="error-log-display" class="error-log-display">
                                                <!-- Log entries will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Checklist Tab -->
                <div id="checklist-tab" class="tab-content" role="tabpanel" aria-labelledby="checklist-tab-button">
                    <div class="gateway-config-section">
                        <h2><?php _e('Payment Gateway System Checklist', 'document-viewer-plugin'); ?></h2>

                        <div class="checklist-intro">
                            <p><?php _e('This comprehensive checklist verifies all critical components of your payment gateway integration. Run this before going live or when troubleshooting issues.', 'document-viewer-plugin'); ?></p>
                        </div>

                        <div class="checklist-controls">
                            <button type="button" id="run-full-checklist" class="button button-primary button-large"><?php _e('🚀 Run Complete Checklist', 'document-viewer-plugin'); ?></button>
                            <button type="button" id="run-quick-check" class="button"><?php _e('⚡ Quick Health Check', 'document_viewer-plugin'); ?></button>
                            <button type="button" id="export-checklist-report" class="button"><?php _e('📄 Export Report', 'document-viewer-plugin'); ?></button>
                        </div>

                        <div id="checklist-progress" class="checklist-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <p class="progress-text"><?php _e('Initializing checklist...', 'document-viewer-plugin'); ?></p>
                        </div>

                        <div id="checklist-results" class="checklist-results" style="display: none;">
                            <div class="checklist-summary">
                                <div class="summary-card summary-passed">
                                    <h4><?php _e('Passed', 'document-viewer-plugin'); ?></h4>
                                    <div class="summary-count">0</div>
                                </div>
                                <div class="summary-card summary-failed">
                                    <h4><?php _e('Failed', 'document-viewer-plugin'); ?></h4>
                                    <div class="summary-count">0</div>
                                </div>
                                <div class="summary-card summary-warnings">
                                    <h4><?php _e('Warnings', 'document-viewer-plugin'); ?></h4>
                                    <div class="summary-count">0</div>
                                </div>
                            </div>

                            <div id="checklist-details" class="checklist-details"></div>
                        </div>
                    </div>
                </div>

                <!-- Error Monitoring Tab -->
                <div id="monitoring-tab" class="tab-content" role="tabpanel" aria-labelledby="monitoring-tab-button">
                    <div class="gateway-config-section">
                        <h2><?php _e('Advanced Error Monitoring', 'document-viewer-plugin'); ?></h2>

                        <div class="monitoring-dashboard">
                            <div class="monitoring-stats">
                                <div class="stat-card">
                                    <h4><?php _e('Total Errors', 'document-viewer-plugin'); ?></h4>
                                    <div class="stat-value" id="total-errors">0</div>
                                </div>
                                <div class="stat-card">
                                    <h4><?php _e('Last 24h', 'document_viewer-plugin'); ?></h4>
                                    <div class="stat-value" id="recent-errors">0</div>
                                </div>
                                <div class="stat-card">
                                    <h4><?php _e('Success Rate', 'document-viewer-plugin'); ?></h4>
                                    <div class="stat-value" id="success-rate">100%</div>
                                </div>
                                <div class="stat-card">
                                    <h4><?php _e('System Status', 'document-viewer-plugin'); ?></h4>
                                    <div class="stat-value status-indicator" id="system-status">
                                        <span class="status-dot status-active"></span>
                                        <?php _e('Online', 'document-viewer-plugin'); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="monitoring-controls">
                                <button type="button" id="refresh-monitoring" class="button"><?php _e('🔄 Refresh Stats', 'document_viewer-plugin'); ?></button>
                                <button type="button" id="clear-error-logs" class="button"><?php _e('🗑️ Clear Logs', 'document_viewer-plugin'); ?></button>
                                <button type="button" id="enable-auto-fix" class="button button-secondary"><?php _e('🔧 Enable Auto-Fix', 'document_viewer-plugin'); ?></button>
                                <button type="button" id="start-monitoring" class="button button-primary"><?php _e('▶️ Start Monitoring', 'document_viewer-plugin'); ?></button>
                            </div>

                            <div id="error-logs-container" class="error-logs-container">
                                <h3><?php _e('Recent Error Logs', 'document-viewer-plugin'); ?></h3>
                                <div id="error-logs-list" class="error-logs-list">
                                    <p class="no-errors"><?php _e('No errors recorded. System running smoothly! ✅', 'document-viewer-plugin'); ?></p>
                                </div>
                            </div>

                            <div id="monitoring-live-feed" class="monitoring-live-feed" style="display: none;">
                                <h3><?php _e('Live Error Feed', 'document-viewer-plugin'); ?></h3>
                                <div id="live-feed-content" class="live-feed-content"></div>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- Close tab-content-wrapper -->
            </div> <!-- Close payment-gateway-container -->

            <div id="gateway-feedback-message" class="notice" style="display: none;"></div>

            <!-- Debug Panel (Hidden by default) -->
            <div id="debug-panel" class="debug-panel" style="display: none;">
                <div class="debug-panel-content">
                    <div class="debug-panel-header">
                        <h3><?php _e('Payment Gateway Debug Console', 'document-viewer-plugin'); ?></h3>
                        <button type="button" id="close-debug-panel" class="debug-close-btn">×</button>
                    </div>
                    <div class="debug-panel-body">
                        <div id="debug-console-output" class="debug-console-output"></div>
                    </div>
                </div>
            </div>
        </div>        <style>
        /* Testing Sub-tabs Styling */
        .testing-sub-tabs {
            margin: 20px 0 0 0;
            border-bottom: 1px solid #ccd0d4;
        }

        .testing-sub-tabs .nav-tab {
            position: relative;
            float: left;
            border: 1px solid #ccd0d4;
            border-bottom: none;
            margin-left: 0.5em;
            padding: 5px 10px;
            font-size: 12px;
            line-height: 1.71428571;
            background: #f1f1f1;
            color: #555;
            text-decoration: none;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .testing-sub-tabs .nav-tab:hover {
            background: #fff;
            color: #135e96;
        }

        .testing-sub-tabs .nav-tab.nav-tab-active {
            background: #fff;
            color: #135e96;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }

        .testing-sub-tab-content {
            display: none !important;
            padding: 20px 0;
            clear: both;
        }

        .testing-sub-tab-content.active {
            display: block !important;
        }

        .automatic-test-actions {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            text-align: center;
        }

        .automatic-test-actions .button {
            margin: 0 5px;
        }

        /* Ensure tab content is properly hidden/shown */
        .payment-gateway-container .tab-content {
            display: none !important;
            padding: 20px;
        }

        .payment-gateway-container .tab-content.active {
            display: block !important;
        }

        .payment-gateway-container .nav-tab {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .payment-gateway-container .nav-tab:hover {
            background: #fff;
            color: #135e96;
        }

        .payment-gateway-container .nav-tab.nav-tab-active {
            background: #fff;
            color: #135e96;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }
        </style>

        <script>
        // Initialize debugging features when page loads
        jQuery(document).ready(function($) {
            console.log('🚀 Payment Gateway Admin Page Loaded');

            // Initialize tab functionality
            initPaymentGatewayTabs();

            // Initialize the Payment Gateway Checker and Error Monitor
            if (typeof window.PaymentGatewayChecker !== 'undefined') {
                console.log('✅ Payment Gateway Checker loaded successfully');
            }
            if (typeof window.AdvancedErrorMonitor !== 'undefined') {
                console.log('✅ Advanced Error Monitor loaded successfully');
                window.AdvancedErrorMonitor.init();
            }

            // Add keyboard shortcuts
            $(document).on('keydown', function(e) {
                if (e.ctrlKey && e.shiftKey && e.keyCode === 77) { // Ctrl+Shift+M
                    e.preventDefault();
                    if (typeof window.AdvancedErrorMonitor !== 'undefined') {
                        window.AdvancedErrorMonitor.showDebugPanel();
                    }
                }
            });
        });

        function initPaymentGatewayTabs() {
            console.log('🔧 Initializing Payment Gateway Tabs');

            // Tab click handlers
            jQuery('.nav-tab').on('click', function(e) {
                e.preventDefault();
                console.log('Tab clicked:', jQuery(this).data('tab'));

                const tabId = jQuery(this).data('tab');
                if (tabId) {
                    switchPaymentGatewayTab(tabId);
                }
            });

            // Initialize ARIA attributes
            jQuery('.tab-content').each(function() {
                const isActive = jQuery(this).hasClass('active');
                jQuery(this).attr('aria-hidden', !isActive);
            });

            jQuery('.nav-tab').each(function() {
                const isActive = jQuery(this).hasClass('nav-tab-active');
                jQuery(this).attr('aria-selected', isActive)
                           .attr('tabindex', isActive ? '0' : '-1');
            });            console.log('✅ Payment Gateway Tabs Initialized');

            // Initialize Testing Sub-tabs
            initTestingSubTabs();
        }

        function initTestingSubTabs() {
            console.log('🔧 Initializing Testing Sub-tabs');

            // Sub-tab click handlers
            jQuery('.testing-sub-tabs .nav-tab').on('click', function(e) {
                e.preventDefault();
                const subTabId = jQuery(this).data('testing-tab');
                if (subTabId) {
                    switchTestingSubTab(subTabId);
                }
            });

            // Initialize first sub-tab as active
            const firstSubTab = jQuery('.testing-sub-tabs .nav-tab').first();
            if (firstSubTab.length) {
                firstSubTab.addClass('nav-tab-active');
                const firstTabId = firstSubTab.data('testing-tab');
                if (firstTabId) {
                    jQuery('#' + firstTabId).addClass('active');
                }
            }            console.log('✅ Testing Sub-tabs Initialized');

            // Initialize Testing Action Handlers
            initTestingActionHandlers();
        }

        function initTestingActionHandlers() {
            console.log('🔧 Initializing Testing Action Handlers');

            // Refresh automatic tests
            jQuery('#refresh-automatic-tests').on('click', function() {
                console.log('🔄 Refreshing automatic tests...');
                location.reload(); // Simple refresh for now
            });

            // Export test results
            jQuery('#export-test-results').on('click', function() {
                console.log('📄 Exporting test results...');
                exportTestResults();
            });

            console.log('✅ Testing Action Handlers Initialized');
        }

        function exportTestResults() {
            // Get test results content
            const testResults = jQuery('#automatic-test-results').text() || 'No test results available';
            const timestamp = new Date().toISOString();
            
            const reportContent = `Payment Gateway Test Results
Generated: ${timestamp}

${testResults}
`;

            // Create and download file
            const blob = new Blob([reportContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `payment-gateway-test-results-${timestamp.split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            console.log('✅ Test results exported successfully');
        }

        function switchTestingSubTab(subTabId) {
            console.log('🔄 Switching to testing sub-tab:', subTabId);

            // Update sub-tab navigation
            jQuery('.testing-sub-tabs .nav-tab').removeClass('nav-tab-active');
            jQuery('[data-testing-tab="' + subTabId + '"]').addClass('nav-tab-active');

            // Update sub-tab content
            jQuery('.testing-sub-tab-content').removeClass('active');
            jQuery('#' + subTabId).addClass('active');

            console.log('✅ Testing sub-tab switched to:', subTabId);
        }

        function switchPaymentGatewayTab(tabId) {
            console.log('🔄 Switching to tab:', tabId);

            // Update nav tabs
            jQuery('.nav-tab').removeClass('nav-tab-active')
                              .attr('aria-selected', 'false')
                              .attr('tabindex', '-1');

            const activeTab = jQuery('[data-tab="' + tabId + '"]');
            activeTab.addClass('nav-tab-active')
                     .attr('aria-selected', 'true')
                     .attr('tabindex', '0');

            // Update content
            jQuery('.tab-content').removeClass('active').attr('aria-hidden', 'true');
            jQuery('#' + tabId).addClass('active').attr('aria-hidden', 'false');

            console.log('✅ Tab switched successfully to:', tabId);
        }

        // Initialize AJAX handlers
        function initAjaxHandlers() {
            console.log('🔧 Initializing AJAX Handlers');

            // Save PayPal settings
            jQuery(document).on('submit', '#paypal-config-form', function(e) {
                e.preventDefault();
                console.log('💾 Saving PayPal configuration...');

                const formData = jQuery(this).serialize();
                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: formData + '&action=save_paypal_config',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error saving configuration. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Save Stripe settings
            jQuery(document).on('submit', '#stripe-config-form', function(e) {
                e.preventDefault();
                console.log('💾 Saving Stripe configuration...');

                const formData = jQuery(this).serialize();
                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: formData + '&action=save_stripe_config',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error saving configuration. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Test PayPal configuration
            jQuery(document).on('click', '#test-paypal-config', function() {
                console.log('🔍 Testing PayPal configuration...');

                const clientId = jQuery('#paypal_client_id').val();
                const clientSecret = jQuery('#paypal_client_secret').val();
                const environment = jQuery('#paypal_environment').val();
                const nonce = jQuery('#paypal_nonce').val();

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'test_paypal_config',
                        client_id: clientId,
                        client_secret: clientSecret,
                        environment: environment,
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error testing configuration. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Test Stripe configuration
            jQuery(document).on('click', '#test-stripe-config', function() {
                console.log('🔍 Testing Stripe configuration...');

                const secretKey = jQuery('#stripe_secret_key').val();
                const environment = jQuery('#stripe_environment').val();
                const nonce = jQuery('#stripe_nonce').val();

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'test_stripe_config',
                        secret_key: secretKey,
                        environment: environment,
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error testing configuration. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Run gateway checklist
            jQuery(document).on('click', '#run-full-checklist', function() {
                console.log('✅ Running full gateway checklist...');

                const nonce = jQuery(this).data('nonce');

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'run_gateway_checklist',
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                            // Update checklist results
                            updateChecklistResults(response.data.results);
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error running checklist. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Test gateway connection
            jQuery(document).on('click', '#test-gateway-connection', function() {
                console.log('🔗 Testing gateway connection...');

                const gateway = jQuery('#gateway-select').val();
                const nonce = jQuery(this).data('nonce');

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'test_gateway_connection',
                        gateway: gateway,
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error testing connection. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Simulate payment error
            jQuery(document).on('click', '.simulate-error-button', function() {
                const errorType = jQuery(this).data('error-type');
                const nonce = jQuery('#payment_gateway_nonce').val();

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'simulate_payment_error',
                        error_type: errorType,
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error simulating error. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Refresh logs
            jQuery(document).on('click', '#refresh-logs', function() {
                console.log('🔄 Refreshing logs...');
                loadPaymentLogs();
            });

            // Clear logs
            jQuery(document).on('click', '#clear-logs', function() {
                console.log('🗑️ Clearing logs...');

                if (!confirm('<?php _e('Are you sure you want to clear the logs?', 'document-viewer-plugin'); ?>')) {
                    return;
                }

                const nonce = jQuery('#payment_gateway_nonce').val();

                jQuery.ajax({
                    url: paymentGatewayAjax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'clear_payment_logs',
                        nonce: nonce
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.data.message, 'success');
                            loadPaymentLogs(); // Refresh logs
                        } else {
                            showAlert(response.data.message, 'error');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        showAlert('<?php _e('Error clearing logs. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                    }
                });
            });

            // Export logs
            jQuery(document).on('click', '#export-logs', function() {
                console.log('📥 Exporting logs...');
                exportLogs();
            });
        }

        // Load payment logs
        function loadPaymentLogs() {
            console.log('📂 Loading payment logs...');

            const nonce = jQuery('#payment_gateway_nonce').val();
            const gateway = jQuery('#gateway-filter').val();
            const logLevel = jQuery('#log-level-filter').val();
            const limit = jQuery('#log-limit').val();

            jQuery.ajax({
                url: paymentGatewayAjax.ajax_url,
                method: 'POST',
                data: {
                    action: 'get_payment_logs',
                    nonce: nonce,
                    gateway: gateway,
                    log_level: logLevel,
                    limit: limit
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const logs = response.data.logs;
                        const logList = jQuery('#error-log-display');
                        logList.empty();

                        if (logs.length === 0) {
                            logList.append('<p><?php _e('No logs found.', 'document-viewer-plugin'); ?></p>');
                        } else {
                            logs.forEach(function(log) {
                                const logItem = `<div class="log-item">
                                    <div class="log-header">
                                        <span class="log-gateway">${log.gateway}</span> - 
                                        <span class="log-type">${log.error_type}</span> - 
                                        <span class="log-time">${log.timestamp}</span>
                                    </div>
                                    <div class="log-message">${log.error_message}</div>
                                    <div class="log-actions">
                                        <button class="button button-secondary view-log-details" data-id="${log.id}"><?php _e('View Details', 'document-viewer-plugin'); ?></button>
                                    </div>
                                </div>`;
                                logList.append(logItem);
                            });
                        }
                    } else {
                        jQuery('#error-log-display').html('<p class="error-message">' + response.data.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    jQuery('#error-log-display').html('<p class="error-message"><?php _e('Error loading logs. Please try again.', 'document-viewer-plugin'); ?></p>');
                }
            });
        }

        // Export logs to file
        function exportLogs() {
            console.log('📤 Exporting logs to file...');

            const nonce = jQuery('#payment_gateway_nonce').val();
            const gateway = jQuery('#gateway-filter').val();
            const logLevel = jQuery('#log-level-filter').val();
            const limit = jQuery('#log-limit').val();

            jQuery.ajax({
                url: paymentGatewayAjax.ajax_url,
                method: 'POST',
                data: {
                    action: 'export_payment_logs',
                    nonce: nonce,
                    gateway: gateway,
                    log_level: logLevel,
                    limit: limit
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const fileUrl = response.data.file_url;
                        window.open(fileUrl, '_blank');
                        showAlert('<?php _e('Logs exported successfully.', 'document-viewer-plugin'); ?>', 'success');
                    } else {
                        showAlert(response.data.message, 'error');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    showAlert('<?php _e('Error exporting logs. Please try again.', 'document-viewer-plugin'); ?>', 'error');
                }
            });
        }

        // Show alert message
        function showAlert(message, type) {
            const alertBox = jQuery('<div class="notice"></div>');
            alertBox.addClass(type === 'success' ? 'notice-success' : 'notice-error');
            alertBox.html(message);
            jQuery('.wrap').prepend(alertBox);

            setTimeout(function() {
                alertBox.fadeOut(300, function() {
                    jQuery(this).remove();
                });            }, 5000);
        }
        <?php
    }
    
    /**
     * Run enhanced payment gateway tests
     *
     * @return array Test results for all gateway systems
     */
    public function run_enhanced_payment_gateway_tests() {
        $test_results = array();
        
        // System Environment Tests
        $test_results['system'] = array(
            'title' => __('System Environment', 'document-viewer-plugin'),
            'tests' => array(
                'php_version' => array(
                    'name' => __('PHP Version', 'document-viewer-plugin'),
                    'result' => version_compare(PHP_VERSION, '7.0.0', '>='),
                    'message' => sprintf(__('PHP %s - Recommended: 7.0 or higher', 'document-viewer-plugin'), PHP_VERSION),
                    'critical' => true,
                ),
                'curl' => array(
                    'name' => __('cURL Extension', 'document-viewer-plugin'),
                    'result' => function_exists('curl_version'),
                    'message' => function_exists('curl_version') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for API connections', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'json' => array(
                    'name' => __('JSON Extension', 'document-viewer-plugin'),
                    'result' => function_exists('json_encode') && function_exists('json_decode'),
                    'message' => function_exists('json_encode') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for API data processing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'ssl' => array(
                    'name' => __('SSL Support', 'document-viewer-plugin'),
                    'result' => extension_loaded('openssl'),
                    'message' => extension_loaded('openssl') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for secure connections', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'memory_limit' => array(
                    'name' => __('Memory Limit', 'document-viewer-plugin'),
                    'result' => $this->check_memory_limit(),
                    'message' => sprintf(__('Current memory limit: %s - Recommended: 128M or higher', 'document-viewer-plugin'), 
                        ini_get('memory_limit')),
                    'critical' => false,
                )
            )
        );
        
        // PayPal Configuration Tests
        $paypal_config = $this->get_paypal_config();
        $test_results['paypal'] = array(
            'title' => __('PayPal Configuration', 'document-viewer-plugin'),
            'tests' => array(
                'client_id' => array(
                    'name' => __('Client ID', 'document-viewer-plugin'),
                    'result' => !empty($paypal_config['client_id']),
                    'message' => !empty($paypal_config['client_id']) 
                        ? __('Configured', 'document-viewer-plugin') 
                        : __('Missing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'client_secret' => array(
                    'name' => __('Client Secret', 'document-viewer-plugin'),
                    'result' => !empty($paypal_config['client_secret']),
                    'message' => !empty($paypal_config['client_secret']) 
                        ? __('Configured', 'document-viewer-plugin') 
                        : __('Missing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'webhook_id' => array(
                    'name' => __('Webhook ID', 'document-viewer-plugin'),
                    'result' => !empty($paypal_config['webhook_id']),
                    'message' => !empty($paypal_config['webhook_id']) 
                        ? __('Configured', 'document_viewer-plugin') 
                        : __('Not configured (optional but recommended)', 'document-viewer-plugin'),
                    'critical' => false,
                ),
                'environment' => array(
                    'name' => __('Environment', 'document-viewer-plugin'),
                    'result' => true,
                    'message' => sprintf(__('Set to %s', 'document-viewer-plugin'), 
                        ucfirst($paypal_config['environment'] ?? 'sandbox')),
                    'critical' => false,
                ),
                'is_active' => array(
                    'name' => __('Status', 'document-viewer-plugin'),
                    'result' => isset($paypal_config['is_active']) && $paypal_config['is_active'],
                    'message' => (isset($paypal_config['is_active']) && $paypal_config['is_active']) 
                        ? __('Active', 'document-viewer-plugin') 
                        : __('Inactive', 'document-viewer-plugin'),
                    'critical' => false,
                )
            )
        );
        
        // Stripe Configuration Tests  
        $stripe_config = $this->get_stripe_config();
        $test_results['stripe'] = array(
            'title' => __('Stripe Configuration', 'document-viewer-plugin'),
            'tests' => array(
                'public_key' => array(
                    'name' => __('Public Key', 'document-viewer-plugin'),
                    'result' => !empty($stripe_config['public_key']),
                    'message' => !empty($stripe_config['public_key']) 
                        ? __('Configured', 'document_viewer-plugin') 
                        : __('Missing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'secret_key' => array(
                    'name' => __('Secret Key', 'document-viewer-plugin'),
                    'result' => !empty($stripe_config['secret_key']),
                    'message' => !empty($stripe_config['secret_key']) 
                        ? __('Configured', 'document_viewer-plugin') 
                        : __('Missing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'webhook_secret' => array(
                    'name' => __('Webhook Secret', 'document-viewer-plugin'),
                    'result' => !empty($stripe_config['webhook_secret']),
                    'message' => !empty($stripe_config['webhook_secret']) 
                        ? __('Configured', 'document_viewer-plugin') 
                        : __('Not configured (optional but recommended)', 'document-viewer-plugin'),
                    'critical' => false,
                ),
                'environment' => array(
                    'name' => __('Environment', 'document-viewer-plugin'),
                    'result' => true,
                    'message' => sprintf(__('Set to %s', 'document-viewer-plugin'), 
                        ucfirst($stripe_config['environment'] ?? 'test')),
                    'critical' => false,
                ),
                'is_active' => array(
                    'name' => __('Status', 'document-viewer-plugin'),
                    'result' => isset($stripe_config['is_active']) && $stripe_config['is_active'],
                    'message' => (isset($stripe_config['is_active']) && $stripe_config['is_active']) 
                        ? __('Active', 'document-viewer-plugin') 
                        : __('Inactive', 'document-viewer-plugin'),
                    'critical' => false,
                )
            )
        );
        
        // Network Connectivity Tests
        $test_results['network'] = array(
            'title' => __('Network Connectivity', 'document-viewer-plugin'),
            'tests' => array(
                'paypal_api' => array(
                    'name' => __('PayPal API', 'document-viewer-plugin'),
                    'result' => $this->check_api_connectivity('https://api.paypal.com/v1/oauth2/token'),
                    'message' => $this->check_api_connectivity('https://api.paypal.com/v1/oauth2/token') 
                        ? __('Accessible', 'document-viewer-plugin') 
                        : __('Not accessible', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'paypal_sandbox' => array(
                    'name' => __('PayPal Sandbox API', 'document-viewer-plugin'),
                    'result' => $this->check_api_connectivity('https://api.sandbox.paypal.com/v1/oauth2/token'),
                    'message' => $this->check_api_connectivity('https://api.sandbox.paypal.com/v1/oauth2/token') 
                        ? __('Accessible', 'document-viewer-plugin') 
                        : __('Not accessible', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'stripe_api' => array(
                    'name' => __('Stripe API', 'document-viewer-plugin'),
                    'result' => $this->check_api_connectivity('https://api.stripe.com/v1/'),
                    'message' => $this->check_api_connectivity('https://api.stripe.com/v1/') 
                        ? __('Accessible', 'document-viewer-plugin') 
                        : __('Not accessible', 'document-viewer-plugin'),
                    'critical' => true,
                )
            )
               );
        
        // Database Tests
        global $wpdb;
        $test_results['database'] = array(
            'title' => __('Database Configuration', 'document-viewer-plugin'),
            'tests' => array(
                'paypal_table' => array(
                    'name' => __('PayPal Config Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}paypal_config'") === $wpdb->prefix . 'paypal_config',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}paypal_config'") === $wpdb->prefix . 'paypal_config'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'stripe_table' => array(
                    'name' => __('Stripe Config Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}stripe_config'") === $wpdb->prefix . 'stripe_config',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}stripe_config'") === $wpdb->prefix . 'stripe_config'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'logs_table' => array(
                    'name' => __('Payment Gateway Logs Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}payment_gateway_logs'") === $wpdb->prefix . 'payment_gateway_logs',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}payment_gateway_logs'") === $wpdb->prefix . 'payment_gateway_logs'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => false,
                )
            )
        );
        
        return $test_results;
    }
    
    /**
     * Display enhanced test results in a formatted way
     *
     * @param array $test_results The results from run_enhanced_payment_gateway_tests
     */
    public function display_enhanced_test_results($test_results) {
        if (empty($test_results) || !is_array($test_results)) {
            echo '<div class="notice notice-error"><p>' . __('No test results available.', 'document-viewer-plugin') . '</p></div>';
            return;
        }
        
        echo '<div class="enhanced-test-results">';
        
        foreach ($test_results as $category => $data) {
            echo '<div class="test-category">';
            echo '<h4>' . esc_html($data['title']) . '</h4>';
            echo '<table class="widefat test-results-table">';
            echo '<thead><tr>
                <th>' . __('Test', 'document-viewer-plugin') . '</th>
                <th>' . __('Status', 'document-viewer-plugin') . '</th>
                <th>' . __('Details', 'document-viewer-plugin') . '</th>
            </tr></thead><tbody>';
            
            foreach ($data['tests'] as $test_id => $test) {
                $status_class = $test['result'] ? 'success' : ($test['critical'] ? 'error' : 'warning');
                $status_icon = $test['result'] ? '✅' : ($test['critical'] ? '❌' : '⚠️');
                
                echo '<tr class="test-row test-status-' . esc_attr($status_class) . '">';
                echo '<td class="test-name">' . esc_html($test['name']) . '</td>';
                echo '<td class="test-status"><span class="status-indicator status-' . esc_attr($status_class) . '">' . $status_icon . '</span></td>';
                echo '<td class="test-message">' . esc_html($test['message']) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
            echo '</div>';
        }
        
        // Generate summary
        $total_tests = $passed_tests = $failed_critical = $warnings = 0;
        
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test) {
                $total_tests++;
                if ($test['result']) {
                    $passed_tests++;
                } else {
                    if ($test['critical']) {
                        $failed_critical++;
                    } else {
                        $warnings++;
                    }
                }
            }
        }
        
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100) : 0;
        $summary_class = $failed_critical > 0 ? 'error' : ($warnings > 0 ? 'warning' : 'success');
        
        echo '<div class="test-summary test-status-' . esc_attr($summary_class) . '">';
        echo '<h4>' . __('Test Summary', 'document-viewer-plugin') . '</h4>';
        echo '<p>' . sprintf(
            __('Passed: %1$d of %2$d tests (%3$d%%) | Critical issues: %4$d | Warnings: %5$d', 'document-viewer-plugin'),
            $passed_tests, $total_tests, $success_rate, $failed_critical, $warnings
        ) . '</p>';
        echo '<div class="progress-bar-container">';
        echo '<div class="progress-bar progress-' . esc_attr($summary_class) . '" style="width: ' . esc_attr($success_rate) . '%"></div>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>'; // End .enhanced-test-results
        
        // Log test results to the database
        $this->log_test_results($test_results);
    }
    
    /**
     * Log test results to database
     * 
     * @param array $test_results Test results to log
     */
    private function log_test_results($test_results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return;
        }
        
        // Only log if there are issues
        $has_issues = false;
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test) {
                if (!$test['result']) {
                    $has_issues = true;
                    break 2;
                }
            }
        }
        
        if (!$has_issues) {
            return;
        }
        
        // Create a summary of failed tests
        $failed_tests = array();
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test_id => $test) {
                if (!$test['result']) {
                    $failed_tests[] = $category . ': ' . $test['name'] . ' - ' . $test['message'];
                }
            }
        }
        
        $error_message = 'Automated test failures detected: ' . count($failed_tests) . ' issues found';
        $error_data = json_encode([
            'failed_tests' => $failed_tests,
            'timestamp' => current_time('mysql'),
            'test_type' => 'automated'
        ]);
        
        $wpdb->insert(
            $table_name,
            array(
                'gateway' => 'system',
                'error_type' => 'test_failure',
                'error_message' => $error_message,
                'error_data' => $error_data,
                'log_level' => 'warning',
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
    
    /**
     * Check if memory limit meets requirements
     *
     * @return bool True if memory limit is 128M or higher
     */
    private function check_memory_limit() {
        $memory_limit = ini_get('memory_limit');
        
        // Convert memory limit to bytes
        $memory_limit = $this->convert_to_bytes($memory_limit);
        
        // 128MB in bytes
        $min_memory = 134217728;
        
        return ($memory_limit >= $min_memory);
    }
    
    /**
     * Convert PHP memory limit value to bytes
     *
     * @param string $memory_value Memory value (e.g. '128M')
     * @return int Value in bytes
     */
    private function convert_to_bytes($memory_value) {
        if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
            if ($matches[2] == 'G') {
                $memory_limit = $matches[1] * 1024 * 1024 * 1024;
            } else if ($matches[2] == 'M') {
                $memory_limit = $matches[1] * 1024 * 1024;
            } else if ($matches[2] == 'K') {
                $memory_limit = $matches[1] * 1024;
            }
        }
        
        return $memory_limit;
    }
    
    /**
     * Check API connectivity
     *
     * @param string $url URL to check
     * @return bool True if URL is accessible
     */
    private function check_api_connectivity($url) {
        // If wp_remote_get is available, use it
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($url, array(
                'timeout' => 5,
                'sslverify' => false
            ));
            
            return !is_wp_error($response) && wp_remote_retrieve_response_code($response) < 500;
        }
        
        // Fall back to cURL
        if (function_exists('curl_init')) {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $http_code < 500;
        }
        
        // If no methods available, assume failure
        return false;
    }

    /**
     * Get payment gateway logs via AJAX
     */
    public function get_payment_logs() {
        check_ajax_referer('payment_gateway_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
            return;
        }
        
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        
        // Get filters
        $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : null;
        $log_level = isset($_POST['log_level']) ? sanitize_text_field($_POST['log_level']) : null;
        $limit = isset($_POST['limit']) ? absint($_POST['limit']) : 50;
        
        // Get logs
        $logs = $test_runner->get_payment_logs($limit, $gateway, $log_level);
        
        // Format logs for display
        $formatted_logs = array();
        foreach ($logs as $log) {
            $formatted_logs[] = array(
                'id' => $log->id,
                'gateway' => ucfirst($log->gateway),
                'error_type' => ucfirst(str_replace('_', ' ', $log->error_type)),
                'error_message' => $log->error_message,
                'log_level' => $log->log_level,
                'timestamp' => $log->created_at,
                'user_id' => $log->user_id,
                'has_details' => !empty($log->error_data)
            );
        }
        
        wp_send_json_success(array(
            'logs' => $formatted_logs,
            'count' => count($logs)
        ));
    }
      /**
     * Clear payment gateway logs via AJAX
     */
    public function clear_payment_logs() {
        check_ajax_referer('payment_gateway_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
            return;
        }
        
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        
        if ($test_runner->clear_payment_logs()) {
            wp_send_json_success(array('message' => __('Logs cleared successfully', 'document-viewer-plugin')));
        } else {
            wp_send_json_error(array('message' => __('Failed to clear logs', 'document-viewer-plugin')));
        }
    }
    
    /**
     * AJAX handler for running payment gateway tests
     */
    public function run_gateway_tests() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'payment_gateway_admin_nonce')) {
            wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
            return;
        }
        
        // Check for user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have sufficient permissions to run gateway tests.', 'document-viewer-plugin')));
            return;
        }
        
        require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
        $test_runner = new Payment_Gateway_Test();
        
        // Run the tests
        $test_results = $test_runner->run_enhanced_payment_gateway_tests();
        
        if ($test_results) {
            wp_send_json_success(array(
                'message' => __('Tests completed successfully', 'document-viewer-plugin'),
                'results' => $test_results
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to run gateway tests', 'document-viewer-plugin')));
        }
    }

    /**
     * Get PayPal configuration
     * 
     * @return array PayPal configuration settings
     */
    public function get_paypal_config() {
        global $wpdb;
        
        // Default config using wp_options
        $config = array(
            'client_id' => get_option('paypal_client_id', ''),
            'client_secret' => get_option('paypal_client_secret', ''),
            'environment' => get_option('paypal_environment', 'sandbox'),
            'webhook_id' => get_option('paypal_webhook_id', ''),
            'is_active' => (bool)get_option('paypal_is_active', false)
        );
        
        // Try to get config from database table if it exists
        $table_name = $wpdb->prefix . 'paypal_config';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            if ($row) {
                // Override with table-based config
                $config['client_id'] = $row['client_id'];
                $config['client_secret'] = $row['client_secret'];
                $config['environment'] = $row['environment'];
                $config['webhook_id'] = $row['webhook_id'];
                $config['is_active'] = (bool)$row['is_active'];
            }
        }
        
        return $config;
    }
    
    /**
     * Get Stripe configuration
     * 
     * @return array Stripe configuration settings
     */
    public function get_stripe_config() {
        global $wpdb;
        
        // Default config using wp_options
        $config = array(
            'public_key' => get_option('stripe_public_key', ''),
            'secret_key' => get_option('stripe_secret_key', ''),
            'environment' => get_option('stripe_environment', 'test'),
            'webhook_endpoint_secret' => get_option('stripe_webhook_secret', ''),
            'is_active' => (bool)get_option('stripe_is_active', false)
        );
        
        // Try to get config from database table if it exists
        $table_name = $wpdb->prefix . 'stripe_config';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            if ($row) {
                // Override with table-based config
                $config['public_key'] = $row['public_key'];
                $config['secret_key'] = $row['secret_key'];
                $config['environment'] = $row['environment'];
                $config['webhook_endpoint_secret'] = $row['webhook_endpoint_secret'];
                $config['is_active'] = (bool)$row['is_active'];
            }
        }
        
        return $config;
    }
}
