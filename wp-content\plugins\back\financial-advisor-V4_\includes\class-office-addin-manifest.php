<?php
/**
 * Office Add-in Manifest Generator
 *
 * Questa classe gestisce la generazione e il download del manifest XML per l'Office Add-in
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Office_Addin_Manifest {
    /**
     * Istanza singleton
     * @var Office_Addin_Manifest
     */
    private static $instance = null;

    /**
     * Constructor
     */
    private function __construct() {
        // Registra l'azione AJAX per il download del manifest
        add_action('wp_ajax_download_office_addin_manifest', array($this, 'download_manifest'));
        add_action('wp_ajax_nopriv_download_office_addin_manifest', array($this, 'download_manifest'));
    }

    /**
     * Ottieni l'istanza singleton della classe
     *
     * @return Office_Addin_Manifest
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Genera e scarica il manifest XML
     */
    public function download_manifest() {
        // Genera il manifest XML
        $manifest_xml = $this->generate_manifest_xml();

        // Imposta gli header per il download
        header('Content-Type: application/xml');
        header('Content-Disposition: attachment; filename="financial-advisor-manifest.xml"');
        header('Content-Length: ' . strlen($manifest_xml));

        // Invia il manifest
        echo $manifest_xml;
        exit;
    }

    /**
     * Genera il manifest XML per l'Office Add-in
     *
     * @return string Il manifest XML
     */
    private function generate_manifest_xml() {
        // Ottieni l'URL del sito
        $site_url = get_site_url();
        $addin_url = trailingslashit($site_url) . 'office-addin/';

        // Genera un ID univoco per l'add-in
        $addin_id = md5($site_url . 'financial-advisor-excel-addin');

        // Crea il manifest XML
        $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<OfficeApp
    xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
    xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
    xsi:type="TaskPaneApp">

    <!-- Informazioni di base -->
    <Id>' . $addin_id . '</Id>
    <Version>*******</Version>
    <ProviderName>Financial Advisor</ProviderName>
    <DefaultLocale>en-US</DefaultLocale>
    <DisplayName DefaultValue="Financial Advisor" />
    <Description DefaultValue="Analyze financial data using AI directly from Excel" />
    <IconUrl DefaultValue="' . $site_url . '/wp-content/plugins/financial-advisor-V4/assets/images/icon-32.png" />
    <HighResolutionIconUrl DefaultValue="' . $site_url . '/wp-content/plugins/financial-advisor-V4/assets/images/icon-64.png" />
    <SupportUrl DefaultValue="' . $site_url . '" />

    <!-- Impostazioni host -->
    <Hosts>
        <Host Name="Workbook" />
    </Hosts>

    <!-- Impostazioni di default -->
    <DefaultSettings>
        <SourceLocation DefaultValue="' . $addin_url . '" />
    </DefaultSettings>

    <!-- Permessi richiesti -->
    <Permissions>ReadWriteDocument</Permissions>

    <!-- Versione per Office 2016 e successivi -->
    <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
        <Hosts>
            <Host xsi:type="Workbook">
                <DesktopFormFactor>
                    <GetStarted>
                        <Title resid="GetStarted.Title"/>
                        <Description resid="GetStarted.Description"/>
                        <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
                    </GetStarted>
                    <FunctionFile resid="Commands.Url"/>
                    <ExtensionPoint xsi:type="PrimaryCommandSurface">
                        <OfficeTab id="TabHome">
                            <Group id="CommandsGroup">
                                <Label resid="CommandsGroup.Label"/>
                                <Icon>
                                    <bt:Image size="16" resid="Icon.16x16"/>
                                    <bt:Image size="32" resid="Icon.32x32"/>
                                    <bt:Image size="80" resid="Icon.80x80"/>
                                </Icon>
                                <Control xsi:type="Button" id="TaskpaneButton">
                                    <Label resid="TaskpaneButton.Label"/>
                                    <Supertip>
                                        <Title resid="TaskpaneButton.Label"/>
                                        <Description resid="TaskpaneButton.Tooltip"/>
                                    </Supertip>
                                    <Icon>
                                        <bt:Image size="16" resid="Icon.16x16"/>
                                        <bt:Image size="32" resid="Icon.32x32"/>
                                        <bt:Image size="80" resid="Icon.80x80"/>
                                    </Icon>
                                    <Action xsi:type="ShowTaskpane">
                                        <TaskpaneId>ButtonId1</TaskpaneId>
                                        <SourceLocation resid="Taskpane.Url"/>
                                    </Action>
                                </Control>
                            </Group>
                        </OfficeTab>
                    </ExtensionPoint>
                </DesktopFormFactor>
            </Host>
        </Hosts>
        <Resources>
            <bt:Images>
                <bt:Image id="Icon.16x16" DefaultValue="' . $site_url . '/wp-content/plugins/financial-advisor-V4/assets/images/icon-16.png"/>
                <bt:Image id="Icon.32x32" DefaultValue="' . $site_url . '/wp-content/plugins/financial-advisor-V4/assets/images/icon-32.png"/>
                <bt:Image id="Icon.80x80" DefaultValue="' . $site_url . '/wp-content/plugins/financial-advisor-V4/assets/images/icon-80.png"/>
            </bt:Images>
            <bt:Urls>
                <bt:Url id="Commands.Url" DefaultValue="' . $addin_url . '"/>
                <bt:Url id="Taskpane.Url" DefaultValue="' . $addin_url . '"/>
                <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="' . $site_url . '"/>
            </bt:Urls>
            <bt:ShortStrings>
                <bt:String id="GetStarted.Title" DefaultValue="Get started with Financial Advisor"/>
                <bt:String id="CommandsGroup.Label" DefaultValue="Financial Advisor"/>
                <bt:String id="TaskpaneButton.Label" DefaultValue="Financial Advisor"/>
            </bt:ShortStrings>
            <bt:LongStrings>
                <bt:String id="GetStarted.Description" DefaultValue="Financial Advisor is now loaded. Go to the HOME tab and click the Financial Advisor button to get started. The add-in provides tools to extract and analyze financial data from your Excel spreadsheets."/>
                <bt:String id="TaskpaneButton.Tooltip" DefaultValue="Click to open Financial Advisor. Extract text from selected cells and analyze it using AI."/>
            </bt:LongStrings>
        </Resources>
    </VersionOverrides>
</OfficeApp>';

        return $manifest;
    }
}

/**
 * Funzione per accedere all'istanza del Manifest Generator
 *
 * @return Office_Addin_Manifest
 */
function office_addin_manifest() {
    return Office_Addin_Manifest::get_instance();
}

// Inizializza il manifest generator
office_addin_manifest();
