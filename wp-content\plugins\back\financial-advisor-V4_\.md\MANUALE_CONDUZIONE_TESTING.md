# MANUALE PER LA CONDUZIONE DELLA SEZIONE TEST
## Office Add-in WordPress Backend Testing Guide

### INDICE
1. [Panoramica del Sistema di Testing](#panoramica)
2. [Configurazione dell'Ambiente](#configurazione)
3. [Struttura dei Test](#struttura)
4. [Esecuzione dei Test](#esecuzione)
5. [Interpretazione dei Risultati](#interpretazione)
6. [Risoluzione dei Problemi](#troubleshooting)
7. [Manutenzione e Aggiornamento](#manutenzione)
8. [Best Practices](#best-practices)

---

## 1. PANORAMICA DEL SISTEMA DI TESTING {#panoramica}

### 1.1 Architettura di Testing
Il sistema di testing è progettato per validare tre componenti core:

- **CacheManager**: Gestione cache per performance ottimali
- **ErrorReporter**: Sistema di reporting errori e logging
- **PerformanceMonitor**: Monitoraggio prestazioni e metriche

### 1.2 Framework Utilizzato
- **PHPUnit 9.6.23**: Framework principale per unit testing
- **Testing Infrastructure Semplificata**: Senza dipendenze Brain Monkey
- **WordPress Mock System**: Sistema di mock per funzioni WordPress

### 1.3 Statistiche Attuali
```
TOTALE TEST: 54 test con 288 assertions
├── CacheManager: 15 test (14 passing, 1 skipped) - 132 assertions
├── ErrorReporter: 15 test (15 passing) - 59 assertions  
└── PerformanceMonitor: 24 test (24 passing) - 97 assertions

TASSO DI SUCCESSO: 98.1% (53/54 test passing)
```

---

## 2. CONFIGURAZIONE DELL'AMBIENTE {#configurazione}

### 2.1 Prerequisiti
```powershell
# Verifica versione PHP (richiesta: PHP 8.0+)
php --version

# Verifica Composer
composer --version

# Verifica PHPUnit
php .\vendor\bin\phpunit --version
```

### 2.2 File di Configurazione

#### phpunit-simple.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="tests/bootstrap-simple.php"
         colors="true"
         verbose="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
    </testsuites>
</phpunit>
```

#### tests/bootstrap-simple.php
File bootstrap con mock WordPress completo:
- Costanti database WordPress
- Mock oggetto $wpdb 
- Funzioni WordPress simulate
- Sistema di cache e opzioni test

### 2.3 Dipendenze
```json
"require-dev": {
    "phpunit/phpunit": "^9.6"
}
```

---

## 3. STRUTTURA DEI TEST {#struttura}

### 3.1 File di Test Operativi

#### 📁 tests/Unit/
```
CacheManagerCorrectedTest.php     ✅ OPERATIVO
ErrorReporterFixedTest.php        ✅ OPERATIVO  
PerformanceMonitorFixedTest.php   ✅ OPERATIVO
```

#### 📁 File Legacy (Non Utilizzare)
```
CacheManagerTest.php              ❌ OBSOLETO
ErrorReporterTest.php             ❌ OBSOLETO
PerformanceMonitorTest.php        ❌ OBSOLETO
PerformanceMonitorCorrectedTest.php ❌ ERRORI API
```

### 3.2 Struttura Tipo di un Test

```php
namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class ComponenteTest extends TestCase
{
    private $componente;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Pulizia dati test
        \TestOptions::clear();
        \TestCache::clear();
        
        // Include classe da testare
        require_once __DIR__ . '/../../includes/class-nome-componente.php';
        
        // Inizializza componente
        $this->componente = new Nome_Componente();
    }

    protected function tearDown(): void
    {
        // Pulizia post-test
        \TestOptions::clear();
        \TestCache::clear();
        
        parent::tearDown();
    }

    public function testFunzionalitaBase(): void
    {
        // Arrange
        $input = 'dati_test';
        
        // Act
        $result = $this->componente->metodo($input);
        
        // Assert
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
    }
}
```

---

## 4. ESECUZIONE DEI TEST {#esecuzione}

### 4.1 Comandi Base

#### Esecuzione Singolo Componente
```powershell
# Cambio directory
cd "c:\Users\<USER>\Desktop\financial-advisor-V4"

# CacheManager
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\CacheManagerCorrectedTest.php --verbose

# ErrorReporter  
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\ErrorReporterFixedTest.php --verbose

# PerformanceMonitor
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\PerformanceMonitorFixedTest.php --verbose
```

#### Esecuzione Tutti i Test Operativi
```powershell
# Metodo sequenziale (raccomandato)
echo "=== CACHE MANAGER TESTS ==="
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\CacheManagerCorrectedTest.php

echo "`n=== ERROR REPORTER TESTS ==="
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\ErrorReporterFixedTest.php

echo "`n=== PERFORMANCE MONITOR TESTS ==="
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\PerformanceMonitorFixedTest.php
```

### 4.2 Opzioni PHPUnit Utili

```powershell
# Test con output dettagliato
--verbose

# Test con colori per leggibilità
--colors=always

# Test con copertura (se disponibile)
--coverage-text

# Stop al primo errore
--stop-on-failure

# Test specifico per nome
--filter testNomeSpecifico

# Debug output
--debug
```

### 4.3 Script di Automazione

#### run-all-tests.ps1
```powershell
#!/usr/bin/env pwsh

Write-Host "=== OFFICE ADD-IN TESTING SUITE ===" -ForegroundColor Green
Write-Host "Inizio esecuzione test suite completa..." -ForegroundColor Yellow

$testDir = "c:\Users\<USER>\Desktop\financial-advisor-V4"
Set-Location $testDir

$tests = @(
    @{Name="CacheManager"; File="tests\Unit\CacheManagerCorrectedTest.php"},
    @{Name="ErrorReporter"; File="tests\Unit\ErrorReporterFixedTest.php"},
    @{Name="PerformanceMonitor"; File="tests\Unit\PerformanceMonitorFixedTest.php"}
)

$totalTests = 0
$totalAssertions = 0
$failedTests = 0

foreach ($test in $tests) {
    Write-Host "`n=== $($test.Name) TESTS ===" -ForegroundColor Cyan
    
    $result = php .\vendor\bin\phpunit --configuration phpunit-simple.xml $test.File 2>&1
    
    Write-Host $result
    
    # Parsing risultati (semplificato)
    if ($result -match "OK") {
        Write-Host "✅ $($test.Name): SUCCESSO" -ForegroundColor Green
    } else {
        Write-Host "❌ $($test.Name): PROBLEMI RILEVATI" -ForegroundColor Red
        $failedTests++
    }
}

Write-Host "`n=== RIEPILOGO FINALE ===" -ForegroundColor Green
Write-Host "Test suite completata!"
Write-Host "Controllare i dettagli sopra per eventuali problemi."
```

---

## 5. INTERPRETAZIONE DEI RISULTATI {#interpretazione}

### 5.1 Output di Successo

```
PHPUnit 9.6.23 by Sebastian Bergmann and contributors.
Runtime:       PHP 8.4.5
Configuration: phpunit-simple.xml

........................                                          24 / 24 (100%)

Time: 00:00.377, Memory: 8.00 MB

OK (24 tests, 97 assertions)
```

**Interpretazione:**
- ✅ Tutti i 24 test sono passati
- ✅ 97 assertions verificate con successo
- ✅ Tempo di esecuzione: 377ms (buono)
- ✅ Memoria utilizzata: 8MB (ottimale)

### 5.2 Output con Errori

```
FAILURES!
Tests: 25, Assertions: 5, Errors: 20, Failures: 5.
```

**Interpretazione:**
- ❌ 20 errori: Metodi non esistenti o problemi gravi
- ❌ 5 fallimenti: Assertions non verificate
- ⚠️ Solo 5 assertions eseguite su test che dovrebbero averne molte di più

### 5.3 Output con Test Saltati

```
OK, but incomplete, skipped, or risky tests!
Tests: 15, Assertions: 132, Skipped: 1.

There was 1 skipped test:
1) Tests\Unit\CacheManagerCorrectedTest::testCacheStatistics
   Cache statistics method not available
```

**Interpretazione:**
- ✅ Test generalmente riusciti
- ⚠️ 1 test saltato per metodo non disponibile (accettabile)
- ✅ 132 assertions completate con successo

### 5.4 Codici di Stato Test

| Simbolo | Significato | Azione |
|---------|-------------|---------|
| `.` | Test passato | ✅ Nessuna azione |
| `F` | Test fallito | ❌ Investigare assertion fallita |
| `E` | Errore nel test | 🔥 Problema grave - fix immediato |
| `S` | Test saltato | ⚠️ Verificare se accettabile |
| `R` | Test rischioso | ⚠️ Output inaspettato |

---

## 6. RISOLUZIONE DEI PROBLEMI {#troubleshooting}

### 6.1 Errori Comuni

#### Errore: "Call to undefined method"
```
Error: Call to undefined method Office_Addin_Performance_Monitor::get_timer_duration()
```

**Causa**: Test utilizza API inesistente
**Soluzione**: 
1. Verificare API reale con `semantic_search`
2. Aggiornare test per usare metodi corretti
3. Utilizzare file test corretti (`*FixedTest.php`)

#### Errore: "Class not found"
```
Error: Class 'Office_Addin_Cache_Manager' not found
```

**Causa**: Classe non inclusa correttamente
**Soluzione**:
```php
// Nel setUp() del test
require_once __DIR__ . '/../../includes/class-office-addin-cache-manager.php';
```

#### Errore: "Failed to open stream"
```
Warning: include_once(path/to/file): failed to open stream: No such file or directory
```

**Causa**: Path del file errato
**Soluzione**: Verificare path relativi dal file di test

### 6.2 Problemi di Mock WordPress

#### $wpdb non disponibile
```php
// Nel bootstrap-simple.php, assicurarsi che sia presente:
global $wpdb;
$wpdb = new class {
    public $prefix = 'wp_';
    
    public function prepare($query, ...$args) {
        return sprintf($query, ...$args);
    }
    
    public function get_results($query, $output = OBJECT) {
        if ($output === ARRAY_A) {
            return [['id' => 1, 'name' => 'test']];
        }
        return [(object)['id' => 1, 'name' => 'test']];
    }
    
    // Altri metodi necessari...
};
```

### 6.3 Debug dei Test

#### Abilitare Debug Output
```php
public function testDebugExample(): void
{
    error_log("DEBUG: Starting test");
    
    $result = $this->component->method();
    
    error_log("DEBUG: Result = " . print_r($result, true));
    
    $this->assertTrue($result);
}
```

#### Verificare Stato Mock
```php
public function testMockState(): void
{
    // Verificare opzioni test
    $options = \TestOptions::getAll();
    var_dump($options);
    
    // Verificare cache test
    $cache = \TestCache::getAll();
    var_dump($cache);
}
```

---

## 7. MANUTENZIONE E AGGIORNAMENTO {#manutenzione}

### 7.1 Aggiunta Nuovi Test

#### Procedura Standard
1. **Analisi API**: Utilizzare `semantic_search` per comprendere metodi disponibili
2. **Creazione Test**: Seguire pattern esistenti
3. **Validazione**: Eseguire test per verificare funzionamento
4. **Documentazione**: Aggiornare questo manuale

#### Template Nuovo Test
```php
public function testNuovaFunzionalita(): void
{
    // Arrange - Preparazione dati
    $input_data = ['key' => 'value'];
    
    // Act - Esecuzione metodo
    $result = $this->component->nuovo_metodo($input_data);
    
    // Assert - Verifiche
    $this->assertIsArray($result);
    $this->assertArrayHasKey('expected_key', $result);
    $this->assertEquals('expected_value', $result['expected_key']);
}
```

### 7.2 Aggiornamento File Mock

#### Aggiunta Nuove Funzioni WordPress
```php
// Nel bootstrap-simple.php
if (!function_exists('nuova_funzione_wp')) {
    function nuova_funzione_wp($param) {
        return 'mock_result';
    }
}
```

#### Aggiornamento Mock $wpdb
```php
public function nuovo_metodo_wpdb($param) {
    // Simulazione comportamento reale
    return ['mock' => 'data'];
}
```

### 7.3 Migrazione Test Legacy

#### Identificazione Test da Aggiornare
```powershell
# Cercare metodi inesistenti
grep -r "get_timer_duration\|record_memory_usage\|log_slow_query" tests/Unit/
```

#### Processo di Conversione
1. **Backup**: Salvare versione originale
2. **Analisi API**: Determinare metodi corretti
3. **Riscrittura**: Aggiornare assertions e chiamate
4. **Test**: Validare funzionamento
5. **Cleanup**: Rimuovere versione obsoleta

---

## 8. BEST PRACTICES {#best-practices}

### 8.1 Scrittura Test Efficaci

#### Principi AAA (Arrange-Act-Assert)
```php
public function testEsempioAAA(): void
{
    // Arrange - Preparazione
    $cache_key = 'test_key';
    $cache_value = ['data' => 'test'];
    
    // Act - Azione
    $this->cache_manager->set($cache_key, $cache_value);
    $result = $this->cache_manager->get($cache_key);
    
    // Assert - Verifica
    $this->assertEquals($cache_value, $result);
}
```

#### Test Atomici e Indipendenti
```php
protected function setUp(): void
{
    parent::setUp();
    
    // Pulizia stato per ogni test
    \TestOptions::clear();
    \TestCache::clear();
    
    // Inizializzazione fresh
    $this->component = new Component();
}
```

### 8.2 Naming Convention

#### Nomi Test Descrittivi
```php
// ❌ Non descrittivo
public function testGet(): void

// ✅ Descrittivo
public function testGetCacheReturnsStoredValue(): void
public function testGetCacheWithExpiredKeyReturnsFalse(): void
public function testGetCacheWithNonexistentKeyReturnsFalse(): void
```

#### Organizzazione Logica
```php
// Raggruppare test correlati
public function testSetCacheStoresValue(): void { }
public function testSetCacheWithTTL(): void { }
public function testSetCacheOverwritesExisting(): void { }

public function testGetCacheReturnsValue(): void { }
public function testGetCacheWithExpiredKey(): void { }
public function testGetCacheWithInvalidKey(): void { }
```

### 8.3 Gestione Dati Test

#### Utilizzo Fixture
```php
private function getCacheTestData(): array
{
    return [
        'simple_string' => 'test_value',
        'array_data' => ['key1' => 'value1', 'key2' => 'value2'],
        'object_data' => (object)['prop' => 'value'],
        'numeric_data' => 42,
        'boolean_data' => true,
        'null_data' => null
    ];
}
```

#### Dati Provider per Test Parametrizzati
```php
/**
 * @dataProvider cacheDataProvider
 */
public function testCacheDataTypes($key, $value, $expected): void
{
    $this->cache_manager->set($key, $value);
    $result = $this->cache_manager->get($key);
    
    $this->assertEquals($expected, $result);
}

public function cacheDataProvider(): array
{
    return [
        'string' => ['test_key', 'test_value', 'test_value'],
        'integer' => ['int_key', 42, 42],
        'array' => ['array_key', ['a' => 1], ['a' => 1]],
        'boolean' => ['bool_key', true, true],
    ];
}
```

### 8.4 Performance Testing

#### Monitoraggio Tempo Esecuzione
```php
public function testPerformanceTimeout(): void
{
    $start = microtime(true);
    
    // Operazione da testare
    $result = $this->component->heavy_operation();
    
    $duration = microtime(true) - $start;
    
    $this->assertLessThan(0.1, $duration, 'Operation took too long');
    $this->assertNotNull($result);
}
```

#### Test Memoria
```php
public function testMemoryUsage(): void
{
    $initial_memory = memory_get_usage(true);
    
    // Operazione che potrebbe usare memoria
    $this->component->memory_intensive_operation();
    
    $final_memory = memory_get_usage(true);
    $memory_diff = $final_memory - $initial_memory;
    
    $this->assertLessThan(1024 * 1024, $memory_diff, 'Memory usage too high'); // 1MB
}
```

---

## CONCLUSIONE

Questo manuale fornisce una guida completa per la conduzione efficace della sezione test del sistema Office Add-in WordPress Backend. 

### Punti Chiave da Ricordare:
1. **Utilizzare solo i file test corretti** (`*CorrectedTest.php`, `*FixedTest.php`)
2. **Eseguire test regolarmente** durante lo sviluppo
3. **Interpretare correttamente i risultati** per identificare problemi reali
4. **Mantenere i test aggiornati** con le modifiche del codice
5. **Seguire best practices** per test maintainabili e affidabili

### Supporto e Assistenza:
- Consultare i file di log per dettagli errori
- Utilizzare output verbose per debugging
- Seguire i pattern esistenti per nuovi test
- Mantenere documentazione aggiornata

**Il sistema di testing attuale con 98.1% di successo (53/54 test) fornisce una solida base per garantire la qualità e affidabilità del sistema Office Add-in.**
