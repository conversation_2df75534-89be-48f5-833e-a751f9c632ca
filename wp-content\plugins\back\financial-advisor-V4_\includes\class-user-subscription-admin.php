<?php
/**
 * User Subscription Admin
 * 
 * Admin interfaces for managing users, subscription types, and viewing reports
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class User_Subscription_Admin {
    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'register_admin_assets'));

        // AJAX handlers for admin functions
        add_action('wp_ajax_get_user_subscription_stats', array($this, 'get_user_subscription_stats_ajax'));
    }    /**
     * Add admin menu items
     */
    public function add_admin_menu() {
        // Get main menu slug from Financial_Advisor_Menu_Manager
        $main_menu_slug = 'document-viewer-settings'; // Default value
        
        // Try to get the actual slug from the Menu Manager if available
        if (class_exists('Financial_Advisor_Menu_Manager')) {
            $menu_manager = financial_advisor_menu_manager();
            if (property_exists($menu_manager, 'main_menu_slug')) {
                $main_menu_slug = $menu_manager->main_menu_slug;
            }
        }
          // Add submenu pages to Financial Advisor main menu
        add_submenu_page(
            $main_menu_slug,
            __('User Subscriptions', 'financial-advisor'),
            __('User Subscriptions', 'financial-advisor'),
            'read', // Lower the capability requirement so we can use our custom access control
            'user-subscriptions',
            array($this, 'render_users_page')
        );
        
        add_submenu_page(
            $main_menu_slug,
            __('Subscription Types', 'financial-advisor'),
            __('Subscription Types', 'financial-advisor'),
            'manage_options',
            'subscription-types',
            array($this, 'render_subscription_types_page')
        );
        
        add_submenu_page(
            $main_menu_slug,            __('Subscription Reports', 'financial-advisor'),
            __('Subscription Reports', 'financial-advisor'),
            'read', // Lower the capability requirement so we can use our custom access control
            'subscription-reports',
            array($this, 'render_reports_page')
        );
    }    /**
     * Register admin scripts and styles
     */
    public function register_admin_assets($hook) {
        // Only load on our admin pages - now they are under Financial Advisor menu
        $current_page = isset($_GET['page']) ? $_GET['page'] : '';
        if ($current_page !== 'user-subscriptions' && 
            $current_page !== 'subscription-types' && 
            $current_page !== 'subscription-reports') {
            return;
        }

        // Register and enqueue new admin style CSS
        wp_register_style(
            'financial-advisor-admin-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-style.css',
            array(),
            '1.0.1'
        );
        wp_enqueue_style('financial-advisor-admin-css');

        // Register and enqueue original CSS (keeping for compatibility)
        wp_register_style(
            'user-subscriptions-admin-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/user-subscriptions-admin.css',
            array('financial-advisor-admin-css'),
            '1.0.0'
        );
        wp_enqueue_style('user-subscriptions-admin-css');
        
        // Registra e carica CSS specifico per il fix dei dialog
        wp_register_style(
            'dialog-fix-css',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/dialog-fix.css',
            array('wp-jquery-ui-dialog'),
            '1.0.2'
        );
        wp_enqueue_style('dialog-fix-css');
        
        // Register and enqueue Chart.js - using specific version for stability
        wp_register_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array('jquery'),
            '3.9.1',
            true
        );
        wp_enqueue_script('chart-js');
          // Add Chart.js fallback in case CDN fails
        add_action('admin_footer', function() {
            $current_page = isset($_GET['page']) ? $_GET['page'] : '';
            if ($current_page === 'subscription-reports') {
                echo '<script>
                    // Verify Chart.js loaded correctly
                    jQuery(document).ready(function($) {
                        if (typeof Chart === "undefined") {
                            console.error("Chart.js failed to load from CDN. Trying alternative CDN.");
                            const script = document.createElement("script");
                            // Try loading from a different CDN as backup
                            script.src = "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js";
                            script.onload = function() {
                                console.log("Successfully loaded Chart.js from backup CDN");
                                if (typeof loadStats === "function") {
                                    loadStats();
                                }
                            };
                            script.onerror = function() {
                                // Last resort - try a different version
                                console.error("Failed to load Chart.js from both CDNs. Trying one more source.");
                                const lastScript = document.createElement("script");
                                lastScript.src = "https://unpkg.com/chart.js@3.9.1/dist/chart.min.js";
                                lastScript.onload = function() {
                                    if (typeof loadStats === "function") {
                                        loadStats();
                                    }
                                };
                                document.head.appendChild(lastScript);
                            };
                            document.head.appendChild(script);
                        }
                    });
                </script>';
            }
        });

        // Carica jQuery UI in modo ordinato per evitare conflitti
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-tabs');
        wp_enqueue_script('jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-datepicker');
        wp_enqueue_script('jquery-ui-button');
        
        // Carica CSS per jQuery UI
        wp_enqueue_style('wp-jquery-ui-dialog');
        
        // Aggiungi stili CSS inline per risolvere problemi con i dialog
        $dialog_fix_css = '
            /* Dialog Fix CSS */
            .ui-dialog {
                z-index: 100001 !important;
                background-color: #fff !important;
                border: 1px solid #ddd !important;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3) !important;
                padding: 0 !important;
            }
            .ui-dialog .ui-dialog-titlebar {
                background: #f1f1f1 !important;
                border-bottom: 1px solid #ddd !important;
                height: auto !important;
                font-size: 14px !important;
                font-weight: bold !important;
                padding: 12px 15px !important;
            }
            .ui-dialog .ui-dialog-content {
                padding: 15px !important;
            }
            .ui-dialog .ui-dialog-buttonpane {
                border-top: 1px solid #ddd !important;
                padding: 10px 15px !important;
                margin-top: 0 !important;
                background: #f5f5f5 !important;
            }
            .ui-dialog .ui-dialog-buttonpane button {
                margin: 0 0 0 10px !important;
            }
            .ui-widget-overlay {
                z-index: 100000 !important;
                background: rgba(0, 0, 0, 0.5) !important;
                opacity: 1 !important;
            }
            #subscription-type-modal {
                min-width: 350px !important;
                max-width: 500px !important;
                padding: 0;
                display: none;
            }
            #subscription-type-form .form-field {
                margin-bottom: 15px;
            }
            #subscription-type-form .form-field label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            #subscription-type-form .form-field input[type="text"],
            #subscription-type-form .form-field input[type="url"] {
                width: 100%;
                padding: 8px;
                box-sizing: border-box;
            }
            .form-errors {
                color: #d63638 !important;
                margin-top: 10px;
                padding: 8px;
                background-color: #ffebec;
                border-left: 4px solid #d63638;
            }
            .ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
                float: right;
                margin-right: 0;
            }
        ';
        
        wp_add_inline_style('wp-jquery-ui-dialog', $dialog_fix_css);
        
        // Register and enqueue JS with updated version to prevent caching issues
        wp_register_script(
            'user-subscriptions-admin-js',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/user-subscriptions-admin.js',
            array('jquery', 'chart-js', 'jquery-ui-dialog', 'jquery-ui-datepicker', 'jquery-ui-tabs'),
            '1.1.2', // Bump version to prevent caching issues
            true
        );
        
        // Register and enqueue the tabs navigation script - load in head with high priority
        wp_register_script(
            'user-subscription-tabs-js',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/user-subscription-tabs.js',
            array('jquery'),
            '1.0.2', // Increment version to prevent caching
            false // Load in head instead of footer
        );
        wp_enqueue_script('user-subscription-tabs-js');
        
        // Ensure our script runs after DOM is ready, but before other scripts
        add_action('admin_head', function() {
            $current_page = isset($_GET['page']) ? $_GET['page'] : '';
            if (in_array($current_page, ['user-subscriptions', 'subscription-types', 'subscription-reports'])) {
                echo '<script>
                    // Ensure jQuery is available and initialize tabs
                    if (typeof jQuery !== "undefined") {
                        jQuery(document).ready(function($) {
                            console.log("Direct tab initialization");
                            $(".user-subscriptions-admin .nav-tab-wrapper .nav-tab").on("click", function(e) {
                                var href = $(this).attr("href");
                                if (href) {
                                    console.log("Direct navigation to: " + href);
                                    window.location.href = href;
                                    e.preventDefault();
                                }
                            });
                        });
                    }
                </script>';
            }
        });

        // Localize script with AJAX object
        wp_localize_script(
            'user-subscriptions-admin-js',
            'userSubscriptionsAdmin',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce'),
                'i18n' => array(
                    'confirm_delete' => __('Are you sure you want to delete this item? This action cannot be undone.', 'financial-advisor'),
                    'delete_success' => __('Item deleted successfully.', 'financial-advisor'),
                    'save_success' => __('Changes saved successfully.', 'financial-advisor'),
                    'error' => __('An error occurred. Please try again.', 'financial-advisor'),
                )
            )
        );
        
        wp_enqueue_script('user-subscriptions-admin-js');

        // Registra e carica lo script di miglioramento UI
        wp_register_script(
            'financial-advisor-admin-enhanced-js',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/admin-enhanced.js',
            array('jquery', 'jquery-ui-dialog'),
            '1.0.0',
            true
        );
        wp_enqueue_script('financial-advisor-admin-enhanced-js');
        
        // I sistemi di dialog (fix, fallback, debug) sono ora integrati direttamente in user-subscriptions-admin.js
        
        // Aggiungi uno script inline per assicurarsi che jQuery e jQuery UI funzionino correttamente
        wp_add_inline_script('jquery-ui-dialog', '
            // Assicura che jQuery UI utilizzi la jQuery giusta
            if (typeof jQuery !== "undefined") {
                var $ = jQuery;
                console.log("jQuery versione: " + $.fn.jquery);
                console.log("jQuery UI disponibile: " + (typeof $.ui !== "undefined"));
                
                // Patch per garantire che il dialog funzioni correttamente
                $(document).ready(function() {
                    if (typeof $.fn.dialog === "function") {
                        console.log("jQuery UI Dialog disponibile. Versione:", $.ui.version);
                        
                        // Forzatura CSS per garantire che i dialog funzionino
                        if (!$("#jquery-ui-style-fix").length) {
                            $("head").append(
                                "<style id=\"jquery-ui-style-fix\">" +
                                ".ui-dialog { display: block !important; z-index: 100001 !important; }" +
                                ".ui-widget-overlay { display: block !important; z-index: 100000 !important; }" +
                                "#wpwrap { z-index: auto !important; }" +
                                "#wpbody-content { position: static !important; }" +
                                "#wpadminbar { z-index: 99999 !important; }" +
                                "</style>"
                            );
                        }
                        
                        // Funzione sicura per l\'apertura dei dialog
                        window.safeDialog = function(element, options) {
                            try {
                                if (!$(element).length) {
                                    console.error("Dialog element non trovato:", element);
                                    return false;
                                }
                                $(element).dialog(options);
                                return true;
                            } catch (e) {
                                console.error("Errore apertura dialog:", e);
                                return false;
                            }
                        };
                    }
                });
            }
        ');
    }
    
    /**
     * Render the Users Management page
     */
    public function render_users_page() {
        // Check if user has admin access
        if (!fa_user_can_access('admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'financial-advisor'));
        }
        
        // Get subscription types for dropdown
        $subscription_manager = dv_user_subscription_manager();
        $subscription_types = $subscription_manager->get_subscription_types();        ?>
        <div class="wrap user-subscriptions-admin financial-advisor-admin">
            <div class="page-header">
                <h1><?php _e('User Subscription Management', 'financial-advisor'); ?></h1>
            </div>
              <div class="nav-tab-wrapper">
                <a href="admin.php?page=user-subscriptions" class="nav-tab nav-tab-active"><?php _e('Manage Users', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-types" class="nav-tab"><?php _e('Subscription Types', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-reports" class="nav-tab"><?php _e('Reports & Statistics', 'financial-advisor'); ?></a>
            </div>
            <div class="content-container">
            
            <!-- Users Table Header + Add New Button -->
            <div class="tablenav top">
                <div class="alignleft actions">
                    <button type="button" id="add-new-user" class="button button-primary">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Add New User', 'financial-advisor'); ?>
                    </button>
                </div>
                <div class="alignright">
                    <input type="text" id="search-users" placeholder="<?php _e('Search users...', 'financial-advisor'); ?>" class="search-box">
                </div>
                <br class="clear">            </div>
            
            <!-- Users Table -->
            <div class="users-table-container">
                <table class="wp-list-table widefat fixed striped users-table">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-id"><?php _e('ID', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-username"><?php _e('Username', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-name"><?php _e('Name', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-surname"><?php _e('Surname', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-email"><?php _e('Email', 'financial-advisor'); ?></th>                        <th scope="col" class="manage-column column-phone"><?php _e('Phone', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-subscription"><?php _e('Subscription', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-credit"><?php _e('Credit', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-analysis-count"><?php _e('Analysis', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-tokens"><?php _e('Tokens', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-actual-cost"><?php _e('Actual Cost', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-total-cost"><?php _e('Total Cost', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-last-update"><?php _e('Last Update', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-actions"><?php _e('Actions', 'financial-advisor'); ?></th>
                    </tr>
                </thead>
                <tbody id="users-list">
                    <tr>
                        <td colspan="13" class="loading-data">
                            <span class="spinner is-active"></span> <?php _e('Loading users...', 'financial-advisor'); ?>
                        </td>                    </tr>
                </tbody>
            </table>
            </div>
            
            <!-- Add/Edit User Dialog -->
            <div id="user-modal" title="<?php _e('User Information', 'financial-advisor'); ?>" style="display:none;">
                <form id="user-form">
                    <input type="hidden" id="user-id" name="user_id" value="0">
                    <div class="form-field">
                        <label for="username"><?php _e('Username', 'financial-advisor'); ?> *</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-field">
                        <label for="name"><?php _e('Name', 'financial-advisor'); ?> *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-field">
                        <label for="surname"><?php _e('Surname', 'financial-advisor'); ?> *</label>
                        <input type="text" id="surname" name="surname" required>
                    </div>
                    <div class="form-field">
                        <label for="email"><?php _e('Email', 'financial-advisor'); ?> *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-field">
                        <label for="phone"><?php _e('Phone', 'financial-advisor'); ?></label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-field">
                        <label for="password"><?php _e('Password', 'financial-advisor'); ?></label>
                        <input type="password" id="password" name="password" 
                               placeholder="<?php _e('Leave blank to keep current password', 'financial-advisor'); ?>">
                    </div>
                    <div class="form-field">
                        <label for="tipo_subscription"><?php _e('Subscription Type', 'financial-advisor'); ?> *</label>
                        <select id="tipo_subscription" name="tipo_subscription" required>
                            <option value=""><?php _e('Select a subscription type', 'financial-advisor'); ?></option>
                            <?php foreach ($subscription_types as $type): ?>
                            <option value="<?php echo esc_attr($type['type_sub']); ?>"><?php echo esc_html($type['type_sub']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>                    <div class="form-field">
                        <label for="credit"><?php _e('Credit', 'financial-advisor'); ?></label>
                        <input type="number" id="credit" name="credit" step="0.01" min="0">
                    </div>
                </form>
                <div class="form-errors" style="color: red;"></div>
            </div>
            
            </div> <!-- end .content-container -->
        </div>
        <?php
    }

    /**
     * Render the Subscription Types page
     */
    public function render_subscription_types_page() {
        // Check if user has admin access
        if (!fa_user_can_access('admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'financial-advisor'));
        }        ?>
        <div class="wrap user-subscriptions-admin financial-advisor-admin">
            <div class="page-header">
                <h1><?php _e('Subscription Types Management', 'financial-advisor'); ?></h1>
            </div>
              <div class="nav-tab-wrapper">
                <a href="admin.php?page=user-subscriptions" class="nav-tab"><?php _e('Manage Users', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-types" class="nav-tab nav-tab-active"><?php _e('Subscription Types', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-reports" class="nav-tab"><?php _e('Reports & Statistics', 'financial-advisor'); ?></a>
            </div>
            <div class="content-container">
            
            <!-- Subscription Types Table Header + Add New Button -->
            <div class="tablenav top">
                <div class="alignleft actions">
                    <button type="button" id="add-new-subscription-type" class="button button-primary">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Add New Subscription Type', 'financial-advisor'); ?>
                    </button>
                </div>
                <br class="clear">
            </div>
            
            <!-- Subscription Types Table -->
            <table class="wp-list-table widefat fixed striped subscription-types-table">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-id"><?php _e('ID', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-type"><?php _e('Type Name', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-redirect"><?php _e('Redirect URL', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-cost-per-token"><?php _e('Cost per Token', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-created"><?php _e('Created At', 'financial-advisor'); ?></th>
                        <th scope="col" class="manage-column column-actions"><?php _e('Actions', 'financial-advisor'); ?></th>
                    </tr>
                </thead>
                <tbody id="subscription-types-list">
                    <tr>
                        <td colspan="5" class="loading-data">
                            <span class="spinner is-active"></span> <?php _e('Loading subscription types...', 'financial-advisor'); ?>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- Add/Edit Subscription Type Dialog -->
            <div id="subscription-type-modal" title="<?php _e('Subscription Type Information', 'financial-advisor'); ?>" style="display:none;">
                <form id="subscription-type-form">
                    <input type="hidden" id="type-id" name="type_id" value="0">
                    <div class="form-field">
                        <label for="type_sub"><?php _e('Type Name', 'financial-advisor'); ?> *</label>
                        <input type="text" id="type_sub" name="type_sub" required>
                    </div>
                    <div class="form-field">
                        <label for="link_redirect"><?php _e('Redirect URL', 'financial-advisor'); ?></label>
                        <input type="url" id="link_redirect" name="link_redirect">                        <p class="description">
                            <?php _e('URL where users will be redirected after login when using this subscription type.', 'financial-advisor'); ?>
                        </p>
                    </div>
                    <div class="form-field">
                        <label for="cost_per_token"><?php _e('Cost per Token', 'financial-advisor'); ?></label>
                        <input type="number" id="cost_per_token" name="cost_per_token" step="0.000001" min="0" value="0.001">
                        <p class="description">
                            <?php _e('The cost per token for this subscription type.', 'financial-advisor'); ?>
                        </p>
                    </div>
                </form>
                <div class="form-errors" style="color: red;"></div>
            </div>
            
            <!-- Non è necessario uno script inline qui, 
                 la funzionalità è gestita da user-subscriptions-admin.js -->
            
            </div> <!-- end .content-container -->
        </div>
        <?php
    }

    /**
     * Render the Reports & Statistics page
     */
    public function render_reports_page() {
        // Check if user has admin access
        if (!fa_user_can_access('admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'financial-advisor'));
        }        ?>
        <div class="wrap user-subscriptions-admin financial-advisor-admin">
            <div class="page-header">
                <h1><?php _e('User Subscription Reports & Statistics', 'financial-advisor'); ?></h1>
            </div>
              <div class="nav-tab-wrapper">
                <a href="admin.php?page=user-subscriptions" class="nav-tab"><?php _e('Manage Users', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-types" class="nav-tab"><?php _e('Subscription Types', 'financial-advisor'); ?></a>
                <a href="admin.php?page=subscription-reports" class="nav-tab nav-tab-active"><?php _e('Reports & Statistics', 'financial-advisor'); ?></a>
            </div>
            <div class="content-container">
              <!-- Filter Options -->
            <div class="filter-container">
                <div class="filter-row">
                    <div class="filter-item user-filter">
                        <label for="user-filter"><?php _e('Filter by User:', 'financial-advisor'); ?></label>
                        <select id="user-filter">
                            <option value="all"><?php _e('All Users', 'financial-advisor'); ?></option>
                            <?php
                            // Placeholder per le opzioni che verranno caricate via JavaScript
                            ?>
                        </select>
                    </div>
                    <div class="filter-item date-range">
                        <button type="button" id="refresh-stats" class="button">
                            <span class="dashicons dashicons-update"></span> <?php _e('Refresh Data', 'financial-advisor'); ?>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="stats-container">
                <div class="stats-row">
                    <div class="stats-card">
                        <h3><?php _e('Total Users', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-users">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Total Subscription Types', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-subscription-types">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Total Credits', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-credits">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                </div>
                
                <!-- Seconda riga con nuovi indicatori -->
                <div class="stats-row">
                    <div class="stats-card">
                        <h3><?php _e('Total Analyses', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-analyses">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Total Tokens Used', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-tokens">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Total Cost', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="total-cost">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                </div>
                
                <!-- New numerical cards by subscription type row -->
                <div class="stats-row">
                    <div class="stats-card">
                        <h3><?php _e('Average Cost Per User', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="avg-cost-per-user">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Average Analyses Per User', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="avg-analyses-per-user">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3><?php _e('Average Tokens Per Analysis', 'financial-advisor'); ?></h3>
                        <div class="stats-value" id="avg-tokens-per-analysis">
                            <span class="spinner is-active"></span>
                        </div>
                    </div>
                </div>
                  <!-- Riga dei grafici a torta -->                <div class="stats-charts-row">
                    <div class="stats-chart-card">
                        <h3><?php _e('Users by Subscription Type', 'financial-advisor'); ?></h3>
                        <div class="chart-container">
                            <canvas id="users-by-subscription-chart" class="pie-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="stats-chart-card">
                        <h3><?php _e('Analyses by Subscription Type', 'financial-advisor'); ?></h3>
                        <div class="chart-container">
                            <canvas id="analyses-by-subscription-chart" class="pie-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="stats-chart-card">
                        <h3><?php _e('Cost by Subscription Type', 'financial-advisor'); ?></h3>
                        <div class="chart-container">
                            <canvas id="cost-by-subscription-chart" class="pie-chart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div><div class="stats-row">
                    <div class="stats-table-container">
                        <h3><?php _e('Top Users by Credit', 'financial-advisor'); ?></h3>
                        <div class="table-responsive">
                            <table class="wp-list-table widefat fixed striped subscription-reports-table">
                                <thead>
                                    <tr>
                                        <th class="column-username"><?php _e('Username', 'financial-advisor'); ?></th>
                                        <th class="column-name"><?php _e('Name', 'financial-advisor'); ?></th>
                                        <th class="column-subscription"><?php _e('Subscription', 'financial-advisor'); ?></th>
                                        <th class="column-credit"><?php _e('Credit', 'financial-advisor'); ?></th>
                                        <th class="column-analysis-count"><?php _e('Analysis', 'financial-advisor'); ?></th>
                                        <th class="column-tokens"><?php _e('Tokens', 'financial-advisor'); ?></th>
                                        <th class="column-actual-cost"><?php _e('Actual Cost', 'financial-advisor'); ?></th>
                                        <th class="column-total-cost"><?php _e('Total Cost', 'financial-advisor'); ?></th>
                                        <th class="column-last-update"><?php _e('Last Update', 'financial-advisor'); ?></th>
                                    </tr>
                                </thead>
                                <tbody id="top-users-by-credit">
                                    <tr>
                                        <td colspan="9">
                                            <span class="spinner is-active"></span> <?php _e('Loading data...', 'financial-advisor'); ?>
                                        </td>
                                    </tr>                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                </div> <!-- end .content-container -->
            </div>
        </div>
        <?php
    }

    /**
     * Get user subscription statistics
     */    public function get_user_subscription_stats_ajax() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'financial-advisor')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'financial-advisor')]);
        }

        $subscription_manager = dv_user_subscription_manager();
        
        // Get all users
        $users = $subscription_manager->get_user_subscriptions();
        
        // Get all subscription types
        $subscription_types = $subscription_manager->get_subscription_types();        // Ottieni il filtro utente se presente
        $user_filter = isset($_POST['user_filter']) ? sanitize_text_field($_POST['user_filter']) : 'all';

        // Calculate statistics
        $total_users = count($users);
        $total_subscription_types = count($subscription_types);
        $total_credits = 0;
        $total_analyses = 0;
        $total_tokens = 0;
        $total_cost = 0;
        $users_by_subscription = [];
        $analyses_by_subscription = [];
        $cost_by_subscription = [];
        $top_users_by_credit = [];
        $users_list = [];
        
        // Initialize subscription-based stats
        foreach ($subscription_types as $type) {
            $users_by_subscription[$type['type_sub']] = 0;
            $analyses_by_subscription[$type['type_sub']] = 0;
            $cost_by_subscription[$type['type_sub']] = 0;
        }
        
        // Process user data
        foreach ($users as $user) {
            // Aggiungi alla lista utenti per il dropdown
            $users_list[] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'name' => $user['name'] . ' ' . $user['surname']
            ];

            // Se c'è un filtro utente, includi solo l'utente selezionato nei calcoli
            if ($user_filter !== 'all' && $user['id'] != $user_filter) {
                continue;
            }
            
            $total_credits += floatval($user['credit']);
            $total_analyses += isset($user['analysis_count']) ? intval($user['analysis_count']) : 0;
            $total_tokens += isset($user['tokens_used']) ? intval($user['tokens_used']) : 0;
            $total_cost += isset($user['tot_cost']) ? floatval($user['tot_cost']) : 0.00;
              // Count users by subscription type
            if (isset($users_by_subscription[$user['tipo_subscription']])) {
                $users_by_subscription[$user['tipo_subscription']]++;
                
                // Count analyses by subscription type
                $user_analyses = isset($user['analysis_count']) ? intval($user['analysis_count']) : 0;
                $analyses_by_subscription[$user['tipo_subscription']] += $user_analyses;
                
                // Count cost by subscription type
                $user_cost = isset($user['tot_cost']) ? floatval($user['tot_cost']) : 0;
                $cost_by_subscription[$user['tipo_subscription']] += $user_cost;
                
            } else {
                $users_by_subscription[$user['tipo_subscription']] = 1;
                $analyses_by_subscription[$user['tipo_subscription']] = isset($user['analysis_count']) ? intval($user['analysis_count']) : 0;
                $cost_by_subscription[$user['tipo_subscription']] = isset($user['tot_cost']) ? floatval($user['tot_cost']) : 0;
            }
              // Add to top users by credit
            $top_users_by_credit[] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'name' => $user['name'] . ' ' . $user['surname'],
                'subscription' => $user['tipo_subscription'],
                'credit' => floatval($user['credit']),
                'analysis_count' => isset($user['analysis_count']) ? intval($user['analysis_count']) : 0,
                'tokens_used' => isset($user['tokens_used']) ? intval($user['tokens_used']) : 0,
                'actual_cost' => isset($user['actual_cost']) ? floatval($user['actual_cost']) : 0.00,
                'tot_cost' => isset($user['tot_cost']) ? floatval($user['tot_cost']) : 0.00,
                'last_update' => isset($user['updated_at']) ? $user['updated_at'] : ''
            ];
        }
        
        // Sort top users by credit (descending)
        usort($top_users_by_credit, function($a, $b) {
            return $b['credit'] <=> $a['credit'];
        });
        
        // Take only top 10
        $top_users_by_credit = array_slice($top_users_by_credit, 0, 10);        wp_send_json_success([
            'total_users' => $total_users,
            'total_subscription_types' => $total_subscription_types,
            'total_credits' => $total_credits,
            'total_analyses' => $total_analyses,
            'total_tokens' => $total_tokens,
            'total_cost' => $total_cost,
            'users_by_subscription' => $users_by_subscription,
            'analyses_by_subscription' => $analyses_by_subscription,
            'cost_by_subscription' => $cost_by_subscription,
            'top_users_by_credit' => $top_users_by_credit,
            'users_list' => $users_list,
            'filtered_user' => $user_filter
        ]);
    }
}

/**
 * Initialize the User Subscription Admin
 */
function dv_user_subscription_admin() {
    static $instance = null;
    if ($instance === null) {
        $instance = new User_Subscription_Admin();
    }
    return $instance;
}

// Create an instance
dv_user_subscription_admin();
