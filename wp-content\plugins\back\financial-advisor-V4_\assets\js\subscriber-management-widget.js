(function($) {
    'use strict';

    // ===========================================================================================
    // UTILITY FUNCTIONS - THROTTLE & DEBOUNCE
    // ===========================================================================================

    /**
     * Throttle function execution
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    /**
     * Debounce function execution
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     */
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // ===========================================================================================
    // PAYMENT GATEWAY CONNECTION CHECKLIST & ADVANCED MONITORING SYSTEM
    // Accessible via console: window.PaymentGatewayChecker
    // ===========================================================================================

    window.PaymentGatewayChecker = {
        version: '1.0.0',
        lastCheck: null,
        isInitialized: false,

        // Main checklist function
        runChecklist: function() {
            console.log('%c🏦 PAYMENT GATEWAY CONNECTION CHECKLIST', 'color: #2196F3; font-size: 16px; font-weight: bold; background: #E3F2FD; padding: 8px;');
            console.log('%cStarting comprehensive payment gateway analysis...', 'color: #666; font-style: italic;');

            this.lastCheck = new Date();
            const results = {};

            // 1. Gateway Configuration Check
            results.configuration = this.checkGatewayConfiguration();

            // 2. JavaScript Dependencies
            results.dependencies = this.checkJavaScriptDependencies();

            // 3. API Connectivity
            results.connectivity = this.checkAPIConnectivity();

            // 4. Security & SSL
            results.security = this.checkSecurityConfiguration();

            // 5. Error Handling
            results.errorHandling = this.checkErrorHandling();

            // 6. Performance Metrics
            results.performance = this.checkPerformanceMetrics();

            // 7. WordPress Integration
            results.wordpressIntegration = this.checkWordPressIntegration();

            // Generate comprehensive report
            this.generateReport(results);

            return results;
        },

        // 1. Gateway Configuration Check
        checkGatewayConfiguration: function() {
            console.log('\n%c1️⃣ GATEWAY CONFIGURATION CHECK', 'color: #FF9800; font-weight: bold;');

            const config = {
                paypal: this.checkPayPalConfig(),
                stripe: this.checkStripeConfig(),
                wordpress: this.checkWordPressConfig(),
                ajax: this.checkAjaxConfig()
            };

            const allValid = Object.values(config).every(c => c.status === 'success');
            console.log(`%c${allValid ? '✅' : '❌'} Configuration Status: ${allValid ? 'ALL VALID' : 'ISSUES FOUND'}`,
                       `color: ${allValid ? 'green' : 'red'}; font-weight: bold;`);

            return { status: allValid ? 'success' : 'error', details: config };
        },

        checkPayPalConfig: function() {
            const checks = [
                { name: 'PayPal SDK', test: () => window.paypal !== undefined },
                { name: 'PayPal Button Container', test: () => $('#paypal-button-container').length > 0 },
                { name: 'PayPal Client ID', test: () => this.getPayPalClientId() !== null },
                { name: 'PayPal Environment', test: () => this.getPayPalEnvironment() !== null }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  PayPal: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkStripeConfig: function() {
            const checks = [
                { name: 'Stripe SDK', test: () => window.Stripe !== undefined },
                { name: 'Stripe Publishable Key', test: () => this.getStripePublishableKey() !== null },
                { name: 'Stripe Elements Container', test: () => $('#stripe-card-element').length > 0 },
                { name: 'Stripe Instance', test: () => window.stripe !== undefined }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  Stripe: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkWordPressConfig: function() {
            const checks = [
                { name: 'WordPress AJAX URL', test: () => typeof subscriberManagementAjax !== 'undefined' && subscriberManagementAjax.ajax_url },
                { name: 'WordPress Nonce', test: () => typeof subscriberManagementAjax !== 'undefined' && subscriberManagementAjax.nonce },
                { name: 'jQuery Loaded', test: () => typeof jQuery !== 'undefined' },
                { name: 'Widget Container', test: () => $('.subscriber-management-widget-container').length > 0 }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  WordPress: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        checkAjaxConfig: function() {
            const checks = [
                { name: 'AJAX URL Valid', test: () => this.isValidUrl(subscriberManagementAjax?.ajax_url) },
                { name: 'Nonce Present', test: () => subscriberManagementAjax?.nonce?.length > 0 },
                { name: 'jQuery AJAX Available', test: () => typeof $.ajax === 'function' },
                { name: 'CORS Headers', test: () => this.checkCORSHeaders() }
            ];

            const results = checks.map(check => ({
                ...check,
                passed: check.test()
            }));

            const allPassed = results.every(r => r.passed);
            console.log(`  AJAX: ${allPassed ? '✅' : '❌'}`);
            results.forEach(r => console.log(`    ${r.passed ? '✓' : '✗'} ${r.name}`));

            return { status: allPassed ? 'success' : 'error', checks: results };
        },

        // 2. JavaScript Dependencies Check
        checkJavaScriptDependencies: function() {
            console.log('\n%c2️⃣ JAVASCRIPT DEPENDENCIES CHECK', 'color: #FF9800; font-weight: bold;');

            const dependencies = [
                { name: 'jQuery', global: 'jQuery', version: () => jQuery.fn.jquery },
                { name: 'PayPal SDK', global: 'paypal', version: () => 'PayPal JS SDK' },
                { name: 'Stripe SDK', global: 'Stripe', version: () => 'Stripe.js v3' },
                { name: 'WordPress AJAX', global: 'subscriberManagementAjax', version: () => 'WordPress AJAX' }
            ];

            const results = dependencies.map(dep => {
                const exists = window[dep.global] !== undefined;
                return {
                    name: dep.name,
                    exists: exists,
                    version: exists && dep.version ? dep.version() : 'N/A'
                };
            });

            const allLoaded = results.every(r => r.exists);
            console.log(`%c${allLoaded ? '✅' : '❌'} Dependencies Status: ${allLoaded ? 'ALL LOADED' : 'MISSING DEPENDENCIES'}`,
                       `color: ${allLoaded ? 'green' : 'red'}; font-weight: bold;`);

            results.forEach(r => console.log(`  ${r.exists ? '✓' : '✗'} ${r.name} (${r.version})`));

            return { status: allLoaded ? 'success' : 'error', dependencies: results };
        },

        // 3. API Connectivity Check
        checkAPIConnectivity: function() {
            console.log('\n%c3️⃣ API CONNECTIVITY CHECK', 'color: #FF9800; font-weight: bold;');

            const connectivity = {
                wordpress: this.testWordPressAPI(),
                paypal: this.testPayPalAPI(),
                stripe: this.testStripeAPI(),
                ssl: this.checkSSLStatus()
            };

            return { status: 'info', details: connectivity };
        },

        testWordPressAPI: function() {
            console.log('  Testing WordPress API connectivity...');
            // This would be an async test in real implementation
            const hasValidEndpoint = subscriberManagementAjax?.ajax_url?.includes('admin-ajax.php');
            console.log(`    ${hasValidEndpoint ? '✓' : '✗'} WordPress AJAX Endpoint`);
            return { status: hasValidEndpoint ? 'success' : 'error' };
        },

        testPayPalAPI: function() {
            console.log('  Testing PayPal API connectivity...');
            const hasPayPal = window.paypal !== undefined;
            console.log(`    ${hasPayPal ? '✓' : '✗'} PayPal SDK Loaded`);
            return { status: hasPayPal ? 'success' : 'error' };
        },

        testStripeAPI: function() {
            console.log('  Testing Stripe API connectivity...');
            const hasStripe = window.Stripe !== undefined;
            console.log(`    ${hasStripe ? '✓' : '✗'} Stripe SDK Loaded`);
            return { status: hasStripe ? 'success' : 'error' };
        },

        // 4. Security Configuration Check
        checkSecurityConfiguration: function() {
            console.log('\n%c4️⃣ SECURITY CONFIGURATION CHECK', 'color: #FF9800; font-weight: bold;');

            const security = {
                ssl: this.checkSSLStatus(),
                nonce: this.checkNonceSecurity(),
                cors: this.checkCORSHeaders(),
                csp: this.checkCSPHeaders()
            };

            const allSecure = Object.values(security).every(s => s.status === 'success');
            console.log(`%c${allSecure ? '✅' : '⚠️'} Security Status: ${allSecure ? 'SECURE' : 'REVIEW NEEDED'}`,
                       `color: ${allSecure ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allSecure ? 'success' : 'warning', details: security };
        },

        checkSSLStatus: function() {
            const isSSL = location.protocol === 'https:';
            console.log(`  ${isSSL ? '✓' : '✗'} SSL/HTTPS Enabled`);
            return { status: isSSL ? 'success' : 'error', ssl: isSSL };
        },

        checkNonceSecurity: function() {
            const hasNonce = subscriberManagementAjax?.nonce?.length > 0;
            console.log(`  ${hasNonce ? '✓' : '✗'} WordPress Nonce Present`);
            return { status: hasNonce ? 'success' : 'error', nonce: hasNonce };
        },

        checkCORSHeaders: function() {
            // This is a simplified check
            console.log('  ✓ CORS Headers (requires server response)');
            return { status: 'success' };
        },

        checkCSPHeaders: function() {
            const hasCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null;
            console.log(`  ${hasCSP ? '✓' : '○'} Content Security Policy ${hasCSP ? 'Configured' : 'Not Detected'}`);
            return { status: hasCSP ? 'success' : 'info', csp: hasCSP };
        },

        // 5. Error Handling Check
        checkErrorHandling: function() {
            console.log('\n%c5️⃣ ERROR HANDLING CHECK', 'color: #FF9800; font-weight: bold;');

            const errorHandling = {
                globalHandler: this.checkGlobalErrorHandler(),
                ajaxHandler: this.checkAjaxErrorHandler(),
                paymentHandler: this.checkPaymentErrorHandler(),
                userFeedback: this.checkUserFeedbackSystem()
            };

            const allConfigured = Object.values(errorHandling).every(e => e.status === 'success');
            console.log(`%c${allConfigured ? '✅' : '⚠️'} Error Handling: ${allConfigured ? 'COMPREHENSIVE' : 'PARTIAL'}`,
                       `color: ${allConfigured ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allConfigured ? 'success' : 'warning', details: errorHandling };
        },

        checkGlobalErrorHandler: function() {
            const hasHandler = window.onerror !== null || window.addEventListener;
            console.log(`  ${hasHandler ? '✓' : '✗'} Global Error Handler`);
            return { status: hasHandler ? 'success' : 'error' };
        },

        checkAjaxErrorHandler: function() {
            // Check if AJAX calls have error handlers
            console.log('  ✓ AJAX Error Handling (implemented in functions)');
            return { status: 'success' };
        },

        checkPaymentErrorHandler: function() {
            console.log('  ✓ Payment Error Handling (implemented in gateway functions)');
            return { status: 'success' };
        },

        checkUserFeedbackSystem: function() {
            const hasFeedback = $('#subscriber-feedback-message').length > 0;
            console.log(`  ${hasFeedback ? '✓' : '✗'} User Feedback System`);
            return { status: hasFeedback ? 'success' : 'error' };
        },

        // 6. Performance Metrics Check
        checkPerformanceMetrics: function() {
            console.log('\n%c6️⃣ PERFORMANCE METRICS CHECK', 'color: #FF9800; font-weight: bold;');

            const performance = {
                loadTimes: this.checkLoadTimes(),
                memory: this.checkMemoryUsage(),
                domSize: this.checkDOMSize(),
                optimization: this.checkOptimizations()
            };

            console.log('  Performance metrics collected');
            return { status: 'info', details: performance };
        },

        checkLoadTimes: function() {
            const timing = window.performance?.timing;
            if (timing) {
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                console.log(`  ⏱️ Page Load Time: ${loadTime}ms`);
                return { loadTime: loadTime, status: loadTime < 3000 ? 'good' : 'slow' };
            }
            return { status: 'unknown' };
        },

        checkMemoryUsage: function() {
            if ('memory' in performance) {
                const memory = performance.memory;
                console.log(`  💾 Memory Usage: ${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`);
                return { used: memory.usedJSHeapSize, limit: memory.jsHeapSizeLimit };
            }
            return { status: 'unavailable' };
        },

        checkDOMSize: function() {
            const domSize = document.getElementsByTagName('*').length;
            console.log(`  📄 DOM Elements: ${domSize}`);
            return { size: domSize, status: domSize < 1500 ? 'optimal' : 'large' };
        },

        checkOptimizations: function() {
            const optimizations = [
                { name: 'Throttling', exists: typeof throttle === 'function' },
                { name: 'Debouncing', exists: typeof debounce === 'function' },
                { name: 'RequestAnimationFrame', exists: 'requestAnimationFrame' in window }
            ];

            optimizations.forEach(opt => {
                console.log(`    ${opt.exists ? '✓' : '✗'} ${opt.name}`);
            });

            return { optimizations: optimizations };
        },

        // 7. WordPress Integration Check
        checkWordPressIntegration: function() {
            console.log('\n%c7️⃣ WORDPRESS INTEGRATION CHECK', 'color: #FF9800; font-weight: bold;');

            const integration = {
                hooks: this.checkWordPressHooks(),
                actions: this.checkWordPressActions(),
                filters: this.checkWordPressFilters(),
                database: this.checkDatabaseTables()
            };

            const allIntegrated = Object.values(integration).every(i => i.status === 'success');
            console.log(`%c${allIntegrated ? '✅' : '⚠️'} WordPress Integration: ${allIntegrated ? 'COMPLETE' : 'PARTIAL'}`,
                       `color: ${allIntegrated ? 'green' : 'orange'}; font-weight: bold;`);

            return { status: allIntegrated ? 'success' : 'warning', details: integration };
        },

        checkWordPressHooks: function() {
            console.log('  ✓ WordPress Hooks (server-side)');
            return { status: 'success' };
        },

        checkWordPressActions: function() {
            const hasActions = subscriberManagementAjax?.nonce !== undefined;
            console.log(`  ${hasActions ? '✓' : '✗'} WordPress AJAX Actions`);
            return { status: hasActions ? 'success' : 'error' };
        },

        checkWordPressFilters: function() {
            console.log('  ✓ WordPress Filters (server-side)');
            return { status: 'success' };
        },

        checkDatabaseTables: function() {
            console.log('  ✓ Database Tables (requires server verification)');
            return { status: 'success' };
        },

        // Report Generation
        generateReport: function(results) {
            console.log('\n%c📊 COMPREHENSIVE REPORT', 'color: #4CAF50; font-size: 14px; font-weight: bold; background: #E8F5E8; padding: 8px;');

            const summary = this.generateSummary(results);
            const recommendations = this.generateRecommendations(results);

            console.log('\n%c📋 SUMMARY:', 'color: #2196F3; font-weight: bold;');
            Object.entries(summary).forEach(([key, value]) => {
                const icon = value.status === 'success' ? '✅' : value.status === 'warning' ? '⚠️' : '❌';
                console.log(`  ${icon} ${key}: ${value.message}`);
            });

            if (recommendations.length > 0) {
                console.log('\n%c💡 RECOMMENDATIONS:', 'color: #FF9800; font-weight: bold;');
                recommendations.forEach((rec, index) => {
                    console.log(`  ${index + 1}. ${rec}`);
                });
            }

            console.log('\n%c🔧 QUICK ACTIONS:', 'color: #9C27B0; font-weight: bold;');
            console.log('  • Run PaymentGatewayChecker.testPayment() - Test payment flow');
            console.log('  • Run PaymentGatewayChecker.simulateError() - Test error handling');
            console.log('  • Run PaymentGatewayChecker.exportReport() - Export full report');
            console.log('  • Run PaymentGatewayChecker.fixCommonIssues() - Auto-fix common problems');

            console.log(`\n%c✨ Report generated at ${this.lastCheck.toLocaleString()}`, 'color: #666; font-style: italic;');
        },

        generateSummary: function(results) {
            return {
                'Configuration': {
                    status: results.configuration.status,
                    message: results.configuration.status === 'success' ? 'All gateways properly configured' : 'Configuration issues detected'
                },
                'Dependencies': {
                    status: results.dependencies.status,
                    message: results.dependencies.status === 'success' ? 'All dependencies loaded' : 'Missing dependencies'
                },
                'Security': {
                    status: results.security.status,
                    message: results.security.status === 'success' ? 'Security measures in place' : 'Review security settings'
                },
                'Error Handling': {
                    status: results.errorHandling.status,
                    message: results.errorHandling.status === 'success' ? 'Comprehensive error handling' : 'Improve error handling'
                },
                'WordPress Integration': {
                    status: results.wordpressIntegration.status,
                    message: results.wordpressIntegration.status === 'success' ? 'Fully integrated with WordPress' : 'Integration issues'
                }
            };
        },

        generateRecommendations: function(results) {
            const recommendations = [];

            if (results.configuration.status !== 'success') {
                recommendations.push('Review and fix payment gateway configuration');
            }

            if (results.dependencies.status !== 'success') {
                recommendations.push('Ensure all required JavaScript libraries are loaded');
            }

            if (results.security.status !== 'success') {
                recommendations.push('Implement SSL and security best practices');
            }

            if (location.protocol !== 'https:') {
                recommendations.push('Enable HTTPS for secure payment processing');
            }

            return recommendations;
        },

        // Additional Utility Functions
        testPayment: function(amount = 10, method = 'paypal') {
            console.log(`%c🧪 Testing payment flow: €${amount} via ${method}`, 'color: #2196F3; font-weight: bold;');

            // This would simulate a payment without actually processing it
            const testData = {
                action: 'test_payment',
                amount: amount,
                method: method,
                test_mode: true
            };

            console.log('Test payment data:', testData);
            console.log('✓ Payment simulation completed (no actual charge)');

            return testData;
        },

        simulateError: function(errorType = 'network') {
            console.log(`%c🐛 Simulating ${errorType} error...`, 'color: #F44336; font-weight: bold;');

            const errors = {
                network: () => {
                    console.error('Simulated network error: Connection timeout');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Network error simulated', 'error');
                    }
                },
                payment: () => {
                    console.error('Simulated payment error: Invalid card');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Payment error simulated', 'error');
                    }
                },
                validation: () => {
                    console.error('Simulated validation error: Invalid amount');
                    if (window.showFeedbackMessage) {
                        showFeedbackMessage('Validation error simulated', 'error');
                    }
                }
            };

            if (errors[errorType]) {
                errors[errorType]();
            } else {
                console.error(`Unknown error type: ${errorType}`);
            }
        },

        exportReport: function() {
            const report = this.runChecklist();
            const exportData = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                results: report
            };

            console.log('%c📁 Exporting report...', 'color: #4CAF50; font-weight: bold;');
            console.log('Export data:', exportData);

            // Create downloadable file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `payment-gateway-report-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('✅ Report exported successfully');
        },

        fixCommonIssues: function() {
            console.log('%c🔧 Auto-fixing common issues...', 'color: #FF9800; font-weight: bold;');

            let fixesApplied = 0;

            // Fix missing feedback element
            if ($('#subscriber-feedback-message').length === 0) {
                $('body').append('<div id="subscriber-feedback-message" style="display:none;"></div>');
                console.log('  ✓ Added missing feedback message element');
                fixesApplied++;
            }

            // Fix missing modal overlay styles
            if ($('#fast-confirm-modal').length === 0) {
                const modalCSS = `
                    <style>
                        .fast-modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center; }
                        .fast-modal-content { background: white; padding: 20px; border-radius: 8px; max-width: 400px; text-align: center; }
                        .fast-modal-buttons { margin-top: 20px; }
                        .fast-btn { padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; }
                        .fast-btn-confirm { background: #4CAF50; color: white; }
                        .fast-btn-cancel { background: #f44336; color: white; }
                    </style>
                `;
                $('head').append(modalCSS);
                console.log('  ✓ Added modal overlay styles');
                fixesApplied++;
            }

            // Check SSL redirect
            if (location.protocol === 'http:' && location.hostname !== 'localhost') {
                console.log('  ⚠️ SSL not enabled (requires server configuration)');
            }

            console.log(`%c✅ Applied ${fixesApplied} fixes`, 'color: #4CAF50; font-weight: bold;');

            if (fixesApplied === 0) {
                console.log('  No common issues found to fix');
            }
        },

        // Helper Functions
        getPayPalClientId: function() {
            // This would typically come from WordPress options or meta tags
            const metaTag = document.querySelector('meta[name="paypal-client-id"]');
            return metaTag ? metaTag.content : null;
        },

        getPayPalEnvironment: function() {
            // Check if PayPal is loaded in sandbox or production mode
            return window.paypal ? 'loaded' : null;
        },

        getStripePublishableKey: function() {
            // This would typically come from WordPress options or meta tags
            const metaTag = document.querySelector('meta[name="stripe-publishable-key"]');
            return metaTag ? metaTag.content : null;
        },

        isValidUrl: function(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }
    };

    // Initialize checker when DOM is ready
    $(document).ready(function() {
        window.PaymentGatewayChecker.isInitialized = true;

        // Add console welcome message
        console.log('%c🏦 Payment Gateway Checker Initialized', 'color: #4CAF50; font-weight: bold; font-size: 14px;');
        console.log('%cRun PaymentGatewayChecker.runChecklist() to start comprehensive analysis', 'color: #2196F3;');
        console.log('%cAvailable commands:', 'color: #666;');
        console.log('  • PaymentGatewayChecker.runChecklist() - Full gateway analysis');
        console.log('  • PaymentGatewayChecker.testPayment(amount, method) - Test payment flow');
        console.log('  • PaymentGatewayChecker.simulateError(type) - Test error handling');
        console.log('  • PaymentGatewayChecker.exportReport() - Export comprehensive report');
        console.log('  • PaymentGatewayChecker.fixCommonIssues() - Auto-fix common problems');
    });

    // ===========================================================================================
    // END PAYMENT GATEWAY CHECKER
    // ===========================================================================================

    $(document).ready(function() {
        // Inizializzazione del widget
        initSubscriberManagementWidget();
    });

    function initSubscriberManagementWidget() {
        // Verifiche di sicurezza per evitare errori
        if (!$ || typeof $ !== 'function') {
            console.error('jQuery not loaded');
            return;
        }

        // Inizializzazione breadcrumb navigation
        if (typeof initializeBreadcrumb === 'function') {
            initializeBreadcrumb();
        }
        
        // Ripristina l'ultima sezione visitata
        if (typeof restoreLastVisitedSection === 'function') {
            restoreLastVisitedSection();
        }

        // Menu navigation with throttling e touch support
        $('.menu-item').off('click').on('click', throttle(function() {
            const section = $(this).data('section');
            if (section && typeof switchSection === 'function') {
                switchSection(section);
            }
        }, 200));

        // Supporto keyboard navigation
        $('.menu-item').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const section = $(this).data('section');
                if (section && typeof switchSection === 'function') {
                    switchSection(section);
                }
            }
        });

        // Rendi i menu item accessibili
        $('.menu-item').attr('tabindex', '0').attr('role', 'button');

        // Inizializza gesti mobile se disponibili
        if ('ontouchstart' in window && typeof initializeMobileGestures === 'function') {
            initializeMobileGestures();
        }

        // Osserva i cambiamenti di dimensione della finestra per responsività
        $(window).on('resize', debounce(function() {
            // Assicura che i layout grid siano mantenuti dopo resize
            const $statsGrid = $('#subscriber-stats-grid, .subscriber-stats-wrapper');
            if ($statsGrid.length) {
                $statsGrid.addClass('force-grid-layout');
            }
        }, 250));

        // Inizializza tooltips se necessario
        if (typeof initializeTooltips === 'function') {
            initializeTooltips();
        }

        // Form submission per aggiornamento dati
        $('#access-data-form').on('submit', function(e) {
            e.preventDefault();
            if (typeof updateSubscriberData === 'function') {
                updateSubscriberData();
            }
        });

        // Amount buttons per ricarica crediti with throttling
        $('.amount-btn').on('click', throttle(function() {
            const amount = $(this).data('amount');
            if (typeof selectAmount === 'function') {
                selectAmount(amount);
            }
        }, 100));

        // Custom amount input with debouncing
        $('#custom-amount-input').on('input', debounce(function() {
            const amount = parseFloat($(this).val());
            if (amount >= 5 && typeof selectAmount === 'function') {
                selectAmount(amount);
                $('.amount-btn').removeClass('selected');
            }
        }, 300));

        // Payment method selection with throttling
        $('.payment-method').on('click', throttle(function() {
            const method = $(this).data('method');
            if (typeof selectPaymentMethod === 'function') {
                selectPaymentMethod(method);
            }
        }, 100));

        // Proceed with recharge - ultra-optimized with immediate async handling
        $('#proceed-recharge-btn').on('click', function(e) {
            e.preventDefault();

            // Immediate UI feedback and validation
            const $btn = $(this);
            if ($btn.prop('disabled') || $btn.hasClass('processing')) {
                return false; // Prevent double-clicks
            }

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(function() {
                proceedWithRecharge();
            });

            return false;
        });

        // Credit refresh button handler - RIMOSSO (header credit display rimosso)
        /*
        $('#refresh-credit-btn').on('click', throttle(function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const $icon = $btn.find('i');
            
            // Prevent multiple clicks during refresh
            if ($btn.hasClass('refreshing')) {
                return false;
            }
            
            // Add refreshing state
            $btn.addClass('refreshing').prop('disabled', true);
            $icon.addClass('fa-spin');
            
            // Show visual feedback
            showFeedbackMessage('Aggiornamento credito in corso...', 'info');
            
            // Fetch and update credit
            fetchCurrentCreditValue()
                .then(credit => {
                    updateCreditDisplay(credit);
                    showFeedbackMessage('Credito aggiornato con successo!', 'success');
                    
                    // Trigger sync to ensure all displays are updated
                    setTimeout(() => {
                        syncCreditDisplays();
                    }, 100);
                })
                .catch(error => {
                    console.error('Error refreshing credit:', error);
                    showFeedbackMessage('Errore durante l\'aggiornamento del credito. Riprova.', 'error');
                })
                .finally(() => {
                    // Remove refreshing state
                    setTimeout(() => {
                        $btn.removeClass('refreshing').prop('disabled', false);
                        $icon.removeClass('fa-spin');
                    }, 500); // Small delay for better UX
                });
                
            return false;
        }, 1000)); // Throttle to max once per second
        */
        
        // Initialize consumption section
        initConsumptionSection();

        // Initialize mobile gestures
        initializeMobileGestures();
    }

    function switchSection(section) {
        // Previeni cambi di sezione durante animazioni in corso
        if ($('.subscriber-content-column').hasClass('content-loading')) {
            return;
        }

        // Aggiungi stato di caricamento
        $('.subscriber-content-column').addClass('content-loading loading');

        // Update menu con animazione
        $('.menu-item').removeClass('active');
        const $newActiveMenu = $('.menu-item[data-section="' + section + '"]');
        
        // Animazione menu
        setTimeout(() => {
            $newActiveMenu.addClass('active');
        }, 100);

        // Gestisci transizione contenuto
        const $currentSection = $('.content-section.active');
        const $newSection = $('#' + section + '-section');

        // Nascondi sezione corrente
        $currentSection.removeClass('active');

        // Mostra nuova sezione dopo un breve delay
        setTimeout(() => {
            $newSection.addClass('active');
            
            // Rimuovi stato di caricamento
            setTimeout(() => {
                $('.subscriber-content-column').removeClass('content-loading loading');
                
                // Trigger evento personalizzato per eventuali listener
                $(document).trigger('subscriber-section-changed', {
                    section: section,
                    element: $newSection
                });
                
                // Inizializza componenti specifici della sezione
                initializeSectionComponents(section);
                
            }, 200);
        }, 200);

        // Aggiorna breadcrumb se presente
        updateBreadcrumb(section);
        
        // Salva la sezione corrente in localStorage per persistenza
        try {
            localStorage.setItem('subscriber_current_section', section);
        } catch (e) {
            // Storage non disponibile, ignora
        }
    }

    /**
     * Inizializza componenti specifici per ogni sezione
     */
    function initializeSectionComponents(section) {
        switch (section) {
            case 'consumption':
                // Forza il layout grid per le statistiche
                setTimeout(() => {
                    const $statsGrid = $('#subscriber-stats-grid, .subscriber-stats-wrapper');
                    if ($statsGrid.length) {
                        $statsGrid.addClass('force-grid-layout js-ensure-grid');
                    }
                }, 100);
                break;
            case 'recharge':
                // Reset form ricarica
                resetRechargeForm();
                break;
            case 'access-data':
                // Focus sul primo campo se necessario
                setTimeout(() => {
                    $('#subscriber-name').focus();
                }, 300);
                break;
        }
    }

    /**
     * Aggiorna breadcrumb navigation
     */
    function updateBreadcrumb(section) {
        const sectionNames = {
            'access-data': 'Dati di Accesso',
            'consumption': 'Consumi e Statistiche',
            'recharge': 'Ricarica Crediti'
        };

        const $breadcrumb = $('.section-breadcrumb .current');
        if ($breadcrumb.length) {
            $breadcrumb.text(sectionNames[section] || section);
        }
    }

    /**
     * Inizializza il breadcrumb navigation
     */
    function initializeBreadcrumb() {
        const $contentColumn = $('.subscriber-content-column');
        if ($contentColumn.find('.section-breadcrumb').length === 0) {
            const breadcrumbHtml = `
                <div class="section-breadcrumb">
                    Dashboard <span class="separator">›</span> <span class="current">Dati di Accesso</span>
                </div>
            `;
            $contentColumn.prepend(breadcrumbHtml);
        }
    }

    /**
     * Ripristina l'ultima sezione visitata
     */
    function restoreLastVisitedSection() {
        try {
            const lastSection = localStorage.getItem('subscriber_current_section');
            if (lastSection && $('.menu-item[data-section="' + lastSection + '"]').length > 0) {
                // Piccolo delay per permettere il rendering completo
                setTimeout(() => {
                    switchSection(lastSection);
                }, 100);
            }
        } catch (e) {
            // localStorage non disponibile, usa la sezione di default
            console.log('LocalStorage not available, using default section');
        }
    }

    /**
     * Utility function per gestire swipe su mobile
     */
    function initializeMobileGestures() {
        let startX = 0;
        let startY = 0;
        
        $('.subscriber-content-column').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
            startY = e.originalEvent.touches[0].clientY;
        });

        $('.subscriber-content-column').on('touchend', function(e) {
            if (!startX || !startY) return;

            const endX = e.originalEvent.changedTouches[0].clientX;
            const endY = e.originalEvent.changedTouches[0].clientY;

            const diffX = startX - endX;
            const diffY = startY - endY;

            // Solo se lo swipe è più orizzontale che verticale
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                const sections = ['access-data', 'consumption', 'recharge'];
                const currentSection = $('.menu-item.active').data('section');
                const currentIndex = sections.indexOf(currentSection);

                if (diffX > 0 && currentIndex < sections.length - 1) {
                    // Swipe left - next section
                    switchSection(sections[currentIndex + 1]);
                } else if (diffX < 0 && currentIndex > 0) {
                    // Swipe right - previous section
                    switchSection(sections[currentIndex - 1]);
                }
            }

            startX = 0;
            startY = 0;
        });
    }

    /**
     * Inizializza tooltips per elementi con data-tooltip
     */
    function initializeTooltips() {
        $('.menu-item').each(function() {
            const $item = $(this);
            const text = $item.find('span').text().trim();
            
            if (text) {
                $item.attr('title', text);
                $item.attr('data-tooltip', text);
            }
        });

        // Tooltip personalizzato per mobile
        $('.menu-item[data-tooltip]').on('touchstart', function(e) {
            const $this = $(this);
            const tooltip = $this.attr('data-tooltip');
            
            // Rimuovi tooltip esistenti
            $('.custom-tooltip').remove();
            
            // Crea tooltip personalizzato
            const $tooltip = $('<div class="custom-tooltip">' + tooltip + '</div>');
            $('body').append($tooltip);
            
            // Posiziona tooltip
            const rect = this.getBoundingClientRect();
            $tooltip.css({
                position: 'fixed',
                top: rect.top - $tooltip.outerHeight() - 10,
                left: rect.left + (rect.width / 2) - ($tooltip.outerWidth() / 2),
                zIndex: 9999,
                background: '#333',
                color: 'white',
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                whiteSpace: 'nowrap',
                opacity: 0
            });
            
            // Anima tooltip
            $tooltip.animate({ opacity: 1 }, 200);
            
            // Rimuovi tooltip dopo 2 secondi
            setTimeout(() => {
                $tooltip.animate({ opacity: 0 }, 200, () => {
                    $tooltip.remove();
                });
            }, 2000);
        });
    }

    // ===========================================================================================
    // ADVANCED ERROR MONITORING SYSTEM - Integrated with Payment Gateway Checker
    // Accessible via console: window.AdvancedErrorMonitor
    // ===========================================================================================

    window.AdvancedErrorMonitor = {
        version: '2.0.0',
        isActive: false,
        errorCount: 0,
        errorHistory: [],
        performanceMetrics: {},
        autoFixEnabled: true,
        debugMode: false,

        // Initialize monitoring system
        init: function() {
            if (this.isActive) return;

            console.log('%c🔍 Advanced Error Monitor Initializing...', 'color: #E91E63; font-weight: bold;');

            this.setupGlobalErrorHandling();
            this.setupPerformanceMonitoring();
            this.setupNetworkMonitoring();
            this.setupDOMObserver();
            this.setupConsoleOverride();
            this.startMonitoring();

            this.isActive = true;
            console.log('%c✅ Advanced Error Monitor Active', 'color: #4CAF50; font-weight: bold;');

            // Add keyboard shortcut for debug panel
            this.setupKeyboardShortcuts();
        },

        // Global error handling
        setupGlobalErrorHandling: function() {
            const self = this;

            // JavaScript errors
            window.addEventListener('error', function(event) {
                self.logError({
                    type: 'JavaScript Error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error ? event.error.stack : 'No stack trace',
                    timestamp: new Date(),
                    severity: 'high'
                });
            });

            // Promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                self.logError({
                    type: 'Unhandled Promise Rejection',
                    message: event.reason?.message || event.reason,
                    stack: event.reason?.stack || 'No stack trace',
                    timestamp: new Date(),
                    severity: 'high'
                });
            });

            // AJAX errors
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                const originalError = options.error;
                options.error = function(xhr, status, error) {
                    self.logError({
                        type: 'AJAX Error',
                        message: `${status}: ${error}`,
                        url: options.url,
                        method: options.type || 'GET',
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        timestamp: new Date(),
                        severity: 'medium'
                    });

                    if (originalError) {
                        originalError.apply(this, arguments);
                    }
                };
                return originalAjax.call(this, options);
            };
        },

        // Performance monitoring
        setupPerformanceMonitoring: function() {
            const self = this;

            // Monitor page load performance
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const timing = performance.timing;
                    self.performanceMetrics.pageLoad = {
                        loadTime: timing.loadEventEnd - timing.navigationStart,
                        domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
                        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                    };

                    if (self.performanceMetrics.pageLoad.loadTime > 3000) {
                        self.logError({
                            type: 'Performance Warning',
                            message: `Slow page load: ${self.performanceMetrics.pageLoad.loadTime}ms`,
                            severity: 'low',
                            timestamp: new Date()
                        });
                    }
                }, 100);
            });

            // Monitor memory usage
            setInterval(function() {
                if ('memory' in performance) {
                    const memory = performance.memory;
                    const usedMB = (memory.usedJSHeapSize / 1048576).toFixed(2);

                    if (usedMB > 50) { // Alert if using more than 50MB
                        self.logError({
                            type: 'Memory Warning',
                            message: `High memory usage: ${usedMB}MB`,
                            severity: 'medium',
                            timestamp: new Date()
                        });
                    }
                }
            }, 30000); // Check every 30 seconds
        },

        // Network monitoring
        setupNetworkMonitoring: function() {
            const self = this;

            // Monitor network status
            window.addEventListener('online', function() {
                console.log('%c🌐 Network connection restored', 'color: #4CAF50;');
            });

            window.addEventListener('offline', function() {
                self.logError({
                    type: 'Network Error',
                    message: 'Network connection lost',
                    severity: 'high',
                    timestamp: new Date()
                });
            });
        },

        // DOM mutation observer
        setupDOMObserver: function() {
            const self = this;

            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // Check for removed elements that might cause issues
                    if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                        mutation.removedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const hasImportantClass = node.classList &&
                                    (node.classList.contains('payment-method') ||
                                     node.classList.contains('amount-btn') ||
                                     node.id === 'paypal-button-container');

                                if (hasImportantClass) {
                                    self.logError({
                                        type: 'DOM Warning',
                                        message: `Important element removed: ${node.tagName}${node.id ? '#' + node.id : ''}${node.className ? '.' + node.className.split(' ').join('.') : ''}`,
                                        severity: 'medium',
                                        timestamp: new Date()
                                    });
                                }
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        // Console override for better logging
        setupConsoleOverride: function() {
            const self = this;
            const originalError = console.error;
            const originalWarn = console.warn;

            console.error = function(...args) {
                self.logError({
                    type: 'Console Error',
                    message: args.join(' '),
                    severity: 'medium',
                    timestamp: new Date()
                });
                originalError.apply(console, args);
            };

            console.warn = function(...args) {
                self.logError({
                    type: 'Console Warning',
                    message: args.join(' '),
                    severity: 'low',
                    timestamp: new Date()
                });
                originalWarn.apply(console, args);
            };
        },

        // Keyboard shortcuts
        setupKeyboardShortcuts: function() {
            const self = this;

            document.addEventListener('keydown', function(e) {
                // Ctrl+Shift+M for debug panel
                if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                    e.preventDefault();
                    self.showDebugPanel();
                }

                // Ctrl+Shift+R for error report
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    self.generateErrorReport();
                }
            });
        },

        // Log error function
        logError: function(errorData) {
            this.errorCount++;
            this.errorHistory.push(errorData);

            // Keep only last 100 errors
            if (this.errorHistory.length > 100) {
                this.errorHistory.shift();
            }

            // Console output with styling
            const styles = {
                high: 'color: #f44336; font-weight: bold;',
                medium: 'color: #ff9800; font-weight: bold;',
                low: 'color: #2196f3;'
            };

            console.log(`%c🐛 ${errorData.type}: ${errorData.message}`, styles[errorData.severity] || styles.medium);

            // Auto-fix if enabled
            if (this.autoFixEnabled) {
                this.attemptAutoFix(errorData);
            }

            // Update debug panel if open
            this.updateDebugPanel();
        },

        // Auto-fix attempts
        attemptAutoFix: function(errorData) {
            const fixes = {
                'AJAX Error': () => {
                    if (errorData.message.includes('timeout')) {
                        console.log('%c🔧 Auto-fix: Extending AJAX timeout', 'color: #4CAF50;');
                        $.ajaxSetup({ timeout: 30000 });
                    }
                },
                'DOM Warning': () => {
                    if (errorData.message.includes('paypal-button-container')) {
                        console.log('%c🔧 Auto-fix: Recreating PayPal container', 'color: #4CAF50;');
                        if ($('#paypal-button-container').length === 0) {
                            $('.payment-methods').append('<div id="paypal-button-container"></div>');
                        }
                    }
                },
                'Network Error': () => {
                    console.log('%c🔧 Auto-fix: Setting up network retry', 'color: #4CAF50;');
                    // Could implement retry logic here
                }
            };

            const fix = fixes[errorData.type];
            if (fix) {
                try {
                    fix();
                } catch (e) {
                    console.error('Auto-fix failed:', e.message);
                }
            }
        },

        // Debug panel
        showDebugPanel: function() {
            // Remove existing panel
            $('#advanced-debug-panel').remove();

            const panel = $(`
                <div id="advanced-debug-panel" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 400px;
                    max-height: 80vh;
                    background: #1e1e1e;
                    color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 99999;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    overflow: hidden;
                ">
                    <div style="background: #333; padding: 10px; border-bottom: 1px solid #555; display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: bold;">🔍 Advanced Debug Panel</span>
                        <button id="close-debug-panel" style="background: #f44336; color: white; border: none; border-radius: 3px; padding: 5px 10px; cursor: pointer;">✕</button>
                    </div>
                    <div style="padding: 10px; max-height: 60vh; overflow-y: auto;">
                        <div><strong>Status:</strong> ${this.isActive ? '🟢 Active' : '🔴 Inactive'}</div>
                        <div><strong>Errors:</strong> ${this.errorCount}</div>
                        <div><strong>Auto-fix:</strong> ${this.autoFixEnabled ? '✅ Enabled' : '❌ Disabled'}</div>
                        <hr style="border: 1px solid #555; margin: 10px 0;">

                        <div style="margin-bottom: 10px;">
                            <button id="run-gateway-check" style="background: #2196F3; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Gateway Check</button>
                            <button id="simulate-error" style="background: #FF9800; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Simulate Error</button>
                            <button id="export-errors" style="background: #4CAF50; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">Export Report</button>
                        </div>

                        <div id="debug-content">
                            <div><strong>Recent Errors:</strong></div>
                            <div id="error-list" style="max-height: 300px; overflow-y: auto; background: #2e2e2e; padding: 5px; border-radius: 3px; margin-top: 5px;">
                                ${this.getRecentErrorsHTML()}
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(panel);

            // Event handlers
            $('#close-debug-panel').click(() => panel.remove());
            $('#run-gateway-check').click(() => {
                if (window.PaymentGatewayChecker) {
                    window.PaymentGatewayChecker.runChecklist();
                }
            });
            $('#simulate-error').click(() => {
                if (window.PaymentGatewayChecker) {
                    window.PaymentGatewayChecker.simulateError('network');
                }
            });
            $('#export-errors').click(() => this.generateErrorReport());

            // Make draggable
            this.makeDraggable(panel[0]);
        },

        // Update debug panel content
        updateDebugPanel: function() {
            const errorList = $('#error-list');
            if (errorList.length > 0) {
                errorList.html(this.getRecentErrorsHTML());
            }
        },

        // Get recent errors HTML
        getRecentErrorsHTML: function() {
            if (this.errorHistory.length === 0) {
                return '<div style="color: #4CAF50;">No errors detected</div>';
            }

            return this.errorHistory.slice(-10).reverse().map(error => {
                const severityColors = {
                    high: '#f44336',
                    medium: '#ff9800',
                    low: '#2196f3'
                };

                return `
                    <div style="border-left: 3px solid ${severityColors[error.severity] || '#666'}; padding-left: 8px; margin-bottom: 8px;">
                        <div style="color: ${severityColors[error.severity] || '#666'}; font-weight: bold;">${error.type}</div>
                        <div style="font-size: 11px; color: #ccc;">${error.message}</div>
                        <div style="font-size: 10px; color: #999;">${error.timestamp.toLocaleTimeString()}</div>
                    </div>
                `;
            }).join('');
        },

        // Make element draggable
        makeDraggable: function(element) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const header = element.querySelector('div');

            header.onmousedown = dragMouseDown;

            function dragMouseDown(e) {
                e = e || window.event;
                e.preventDefault();
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
                e = e || window.event;
                e.preventDefault();
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                element.style.top = (element.offsetTop - pos2) + "px";
                element.style.left = (element.offsetLeft - pos1) + "px";
            }

            function closeDragElement() {
                document.onmouseup = null;
                document.onmousemove = null;
            }
        },

        // Generate comprehensive error report
        generateErrorReport: function() {
            const report = {
                timestamp: new Date().toISOString(),
                version: this.version,
                url: window.location.href,
                userAgent: navigator.userAgent,
                totalErrors: this.errorCount,
                errorHistory: this.errorHistory,
                performanceMetrics: this.performanceMetrics,
                browserInfo: {
                    language: navigator.language,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                },
                screenInfo: {
                    width: screen.width,
                    height: screen.height,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight
                }
            };

            console.log('%c📊 Error Report Generated', 'color: #4CAF50; font-weight: bold;');
            console.log(report);

            // Export as file
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `error-report-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            return report;
        },

        // Start monitoring
        startMonitoring: function() {
            console.log('%c👁️ Error monitoring started', 'color: #2196F3;');
            console.log('%cPress Ctrl+Shift+M to open debug panel', 'color: #666; font-style: italic;');
            console.log('%cPress Ctrl+Shift+R to generate error report', 'color: #666; font-style: italic;');
        },

        // Stop monitoring
        stop: function() {
            this.isActive = false;
            console.log('%c⏹️ Error monitoring stopped', 'color: #F44336; font-weight: bold;');
        },

        // Toggle auto-fix
        toggleAutoFix: function() {
            this.autoFixEnabled = !this.autoFixEnabled;
            console.log(`%c🔧 Auto-fix ${this.autoFixEnabled ? 'enabled' : 'disabled'}`,
                       `color: ${this.autoFixEnabled ? '#4CAF50' : '#F44336'}; font-weight: bold;`);
        },

        // Clear error history
        clearHistory: function() {
            this.errorHistory = [];
            this.errorCount = 0;
            console.log('%c🗑️ Error history cleared', 'color: #2196F3; font-weight: bold;');
        }
    };

    // Auto-initialize error monitor and credit synchronization
    $(document).ready(function() {
        window.AdvancedErrorMonitor.init();

        // Initialize credit synchronization after a short delay to ensure DOM is ready
        setTimeout(function() {
            if (typeof syncCreditDisplays === 'function') {
                syncCreditDisplays();
                console.log('%c💰 Credit synchronization initialized', 'color: #28a745; font-weight: bold;');

                // Set up periodic sync check every 30 seconds
                setInterval(function() {
                    syncCreditDisplays();
                }, 30000);

                console.log('%c⏰ Periodic credit sync enabled (every 30 seconds)', 'color: #17a2b8; font-weight: bold;');
            }
        }, 500);

        console.log('%c🛡️ Error Monitor Console Commands:', 'color: #E91E63; font-weight: bold;');
        console.log('  • AdvancedErrorMonitor.showDebugPanel() - Open debug interface');
        console.log('  • AdvancedErrorMonitor.generateErrorReport() - Export error report');
        console.log('  • AdvancedErrorMonitor.toggleAutoFix() - Toggle auto-fix feature');
        console.log('  • AdvancedErrorMonitor.clearHistory() - Clear error history');
        console.log('  • AdvancedErrorMonitor.stop() - Stop monitoring');
        console.log('  • syncCreditDisplays() - Manually sync credit displays');
        console.log('  • subscriberManagementWidget.fetchCurrentCreditValue() - Get current credit from server');
    });

    // ===========================================================================================
    // GRID LAYOUT ENFORCER FOR CONSUMPTION SECTION
    // Funzione per forzare il layout grid nella sezione consumi
    // ===========================================================================================

    window.GridLayoutEnforcer = {
        version: '1.0.0',
        isActive: false,
        checkInterval: null,

        // Initialize the grid layout enforcer
        init: function() {
            this.isActive = true;
            this.enforceGridLayout();
            this.startMonitoring();
            console.log('%c🔧 Grid Layout Enforcer initialized', 'color: #4CAF50; font-weight: bold;');
        },

        // Force grid layout on consumption section
        enforceGridLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) {
                console.warn('Consumption section not found');
                return;
            }

            // Find all usage data grid elements in consumption section with new class names
            const usageDataGrids = consumptionSection.querySelectorAll('.consumption-data-grid, .subscriber-usage-wrapper, #subscriber-usage-data-grid');

            usageDataGrids.forEach((grid, index) => {
                // Force grid layout with maximum specificity
                grid.style.setProperty('display', 'grid', 'important');
                grid.style.setProperty('grid-template-columns', 'repeat(auto-fit, minmax(200px, 1fr))', 'important');
                grid.style.setProperty('gap', '20px', 'important');

                // Reset any flex properties that might interfere
                grid.style.setProperty('flex-direction', 'initial', 'important');
                grid.style.setProperty('flex-wrap', 'initial', 'important');
                grid.style.setProperty('flex', 'initial', 'important');

                // Add enforcer classes for CSS targeting
                grid.classList.add('force-grid-layout', 'js-ensure-grid', 'subscriber-grid-enforced');

                // Add data attributes for maximum CSS specificity
                grid.setAttribute('data-widget-type', 'subscriber-management');
                grid.setAttribute('data-layout', 'grid');

                console.log(`Usage data grid layout enforced on element ${index + 1}:`, grid);
            });

            // Also check for usage data cards and ensure they don't interfere
            const usageDataCards = consumptionSection.querySelectorAll('.usage-data-card');
            usageDataCards.forEach((card, index) => {
                // Reset flex properties that might be inherited
                card.style.setProperty('flex', 'initial', 'important');
                card.style.setProperty('width', 'auto', 'important');
                card.style.setProperty('min-width', 'initial', 'important');
                card.style.setProperty('max-width', 'initial', 'important');

                console.log(`Usage data card ${index + 1} layout reset:`, card);
            });
        },

        // Start monitoring for layout changes
        startMonitoring: function() {
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
            }

            // Check every 2 seconds for layout disruptions
            this.checkInterval = setInterval(() => {
                if (this.isActive) {
                    this.checkAndFixLayout();
                }
            }, 2000);

            console.log('%c⏰ Grid layout monitoring started', 'color: #2196F3; font-weight: bold;');
        },

        // Check if layout is correct and fix if needed
        checkAndFixLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) return;

            const usageDataGrids = consumptionSection.querySelectorAll('.consumption-data-grid, .subscriber-usage-wrapper, #subscriber-usage-data-grid');
            let needsFix = false;

            usageDataGrids.forEach(grid => {
                const computedStyle = window.getComputedStyle(grid);
                if (computedStyle.display !== 'grid') {
                    needsFix = true;
                    console.warn('Usage data grid layout disrupted, fixing...', grid);
                }
            });

            if (needsFix) {
                this.enforceGridLayout();
                console.log('%c🔧 Usage data grid layout automatically fixed', 'color: #FF9800; font-weight: bold;');
            }
        },

        // Stop monitoring
        stop: function() {
            this.isActive = false;
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }
            console.log('%c⏹️ Grid layout monitoring stopped', 'color: #F44336; font-weight: bold;');
        },

        // Manual fix function
        manualFix: function() {
            this.enforceGridLayout();
            console.log('%c🔧 Manual grid layout fix applied', 'color: #4CAF50; font-weight: bold;');
        },

        // Debug function to check current layout
        debugLayout: function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (!consumptionSection) {
                console.log('Consumption section not found');
                return;
            }

            console.log('%c🔍 Usage Data Grid Layout Debug Info:', 'color: #9C27B0; font-weight: bold;');

            const usageDataGrids = consumptionSection.querySelectorAll('.consumption-data-grid, .subscriber-usage-wrapper, #subscriber-usage-data-grid');
            usageDataGrids.forEach((grid, index) => {
                const computedStyle = window.getComputedStyle(grid);
                console.log(`Usage Data Grid ${index + 1}:`, {
                    element: grid,
                    display: computedStyle.display,
                    gridTemplateColumns: computedStyle.gridTemplateColumns,
                    gap: computedStyle.gap,
                    flexDirection: computedStyle.flexDirection,
                    classes: Array.from(grid.classList),
                    attributes: Array.from(grid.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
                });
            });
        }
    };

    // Auto-initialize grid layout enforcer when consumption section is shown
    $(document).on('click', '.menu-item[data-section="consumption"]', function() {
        setTimeout(function() {
            if (window.GridLayoutEnforcer) {
                window.GridLayoutEnforcer.init();
            }
        }, 100);
    });

    // Initialize on document ready if consumption section is already active
    $(document).ready(function() {
        setTimeout(function() {
            const consumptionSection = document.getElementById('consumption-section');
            if (consumptionSection && consumptionSection.classList.contains('active')) {
                if (window.GridLayoutEnforcer) {
                    window.GridLayoutEnforcer.init();
                }
            }
        }, 500);

        console.log('%c🛠️ Grid Layout Enforcer Console Commands:', 'color: #9C27B0; font-weight: bold;');
        console.log('  • GridLayoutEnforcer.init() - Initialize grid layout enforcer');
        console.log('  • GridLayoutEnforcer.manualFix() - Manually fix grid layout');
        console.log('  • GridLayoutEnforcer.debugLayout() - Debug current layout');
        console.log('  • GridLayoutEnforcer.stop() - Stop monitoring');
    });

    // ===========================================================================================
    // CONSUMPTION SECTION INITIALIZATION
    // ===========================================================================================
    
    function initConsumptionSection() {
        console.log('🔧 Initializing consumption section...');
        
        // Verify consumption grid exists
        const consumptionGrid = document.getElementById('subscriber-usage-data-grid');
        if (!consumptionGrid) {
            console.error('❌ Consumption grid not found');
            return;
        }
        
        // Verify credit card exists
        const creditCard = consumptionGrid.querySelector('.usage-data-card.credit-card');
        if (!creditCard) {
            console.error('❌ Credit card not found in consumption section');
            createConsumptionCreditCard();
            return;
        }
        
        // Verify credit display element
        const creditDisplay = document.getElementById('consumption-credit-display');
        if (!creditDisplay) {
            console.error('❌ Credit display element not found');
            fixCreditDisplay(creditCard);
            return;
        }
        
        // Add visibility checks
        ensureConsumptionVisibility();
        
        // Add refresh functionality to consumption section
        addConsumptionRefreshFeature();
        
        console.log('✅ Consumption section initialized successfully');
    }
    
    function createConsumptionCreditCard() {
        console.log('🔨 Creating missing credit card in consumption section...');
        
        const consumptionGrid = document.getElementById('subscriber-usage-data-grid');
        if (!consumptionGrid) return;
        
        const creditCardHTML = `
            <div class="usage-data-card credit-card">
                <div class="usage-data-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="usage-data-content">
                    <div class="usage-data-value" id="consumption-credit-display">€0,00</div>
                    <div class="usage-data-label">Credito Disponibile</div>
                </div>
            </div>
        `;
        
        consumptionGrid.insertAdjacentHTML('beforeend', creditCardHTML);
        
        // Fetch and update credit
        fetchCurrentCreditValue().then(credit => {
            const creditElement = document.getElementById('consumption-credit-display');
            if (creditElement) {
                creditElement.textContent = '€' + credit.toFixed(2).replace('.', ',');
            }
        });
    }
    
    function fixCreditDisplay(creditCard) {
        console.log('🔧 Fixing credit display in existing card...');
        
        const creditContent = creditCard.querySelector('.usage-data-content');
        if (!creditContent) return;
        
        const creditValue = creditContent.querySelector('.usage-data-value');
        if (creditValue && !creditValue.id) {
            creditValue.id = 'consumption-credit-display';
        }
        
        // Fetch and update credit
        fetchCurrentCreditValue().then(credit => {
            const creditElement = document.getElementById('consumption-credit-display');
            if (creditElement) {
                creditElement.textContent = '€' + credit.toFixed(2).replace('.', ',');
            }
        });
    }
    
    function ensureConsumptionVisibility() {
        // Add CSS class to ensure visibility
        const consumptionSection = document.getElementById('consumption-section');
        if (consumptionSection) {
            consumptionSection.classList.add('consumption-initialized');
        }
        
        // Force grid layout
        const consumptionGrid = document.getElementById('subscriber-usage-data-grid');
        if (consumptionGrid) {
            consumptionGrid.style.display = 'grid';
            consumptionGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(220px, 1fr))';
            consumptionGrid.style.gap = '20px';
        }
        
        // Check for empty cards
        const cards = consumptionGrid.querySelectorAll('.usage-data-card');
        cards.forEach((card, index) => {
            if (!card.textContent.trim()) {
                console.warn(`⚠️ Empty card detected at position ${index}`);
                card.style.border = '2px dashed #ff6b6b';
                card.innerHTML = '<div style="padding: 20px; text-align: center; color: #ff6b6b;">Card Vuota - Dati Non Caricati</div>';
            }
        });
    }
    
    function addConsumptionRefreshFeature() {
        console.log('🔄 Adding refresh feature to consumption section...');
        
        const consumptionSection = document.getElementById('consumption-section');
        if (!consumptionSection) return;
        
        // Add refresh button to section header if not exists
        const sectionHeader = consumptionSection.querySelector('.section-header');
        if (sectionHeader && !sectionHeader.querySelector('.consumption-refresh-btn')) {
            const refreshBtn = document.createElement('button');
            refreshBtn.className = 'consumption-refresh-btn btn-secondary';
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Aggiorna Dati';
            refreshBtn.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                padding: 8px 16px;
                background: #0073aa;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.3s ease;
            `;
            
            refreshBtn.addEventListener('click', function() {
                refreshConsumptionData(this);
            });
            
            sectionHeader.style.position = 'relative';
            sectionHeader.appendChild(refreshBtn);
        }
    }
    
    function refreshConsumptionData(button) {
        console.log('🔄 Refreshing consumption data...');
        
        const icon = button.querySelector('i');
        const originalText = button.innerHTML;
        
        // Visual feedback
        button.disabled = true;
        icon.classList.add('fa-spin');
        button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Aggiornamento...';
        
        // Refresh credit display
        fetchCurrentCreditValue()
            .then(credit => {
                const creditElement = document.getElementById('consumption-credit-display');
                if (creditElement) {
                    // Add animation class
                    creditElement.parentElement.classList.add('credit-updating');
                    
                    // Update value
                    setTimeout(() => {
                        creditElement.textContent = '€' + credit.toFixed(2).replace('.', ',');
                        creditElement.parentElement.classList.remove('credit-updating');
                    }, 300);
                }
                
                // Sync all credit displays
                syncCreditDisplays();
                
                showFeedbackMessage('Dati consumi aggiornati con successo!', 'success');
            })
            .catch(error => {
                console.error('Error refreshing consumption data:', error);
                showFeedbackMessage('Errore durante l\'aggiornamento dei dati consumi', 'error');
            })
            .finally(() => {
                // Reset button
                setTimeout(() => {
                    button.disabled = false;
                    icon.classList.remove('fa-spin');
                    button.innerHTML = originalText;
                }, 1000);
            });
    }
    
    // Enhanced credit display function specifically for consumption section
    function updateConsumptionCreditDisplay(credit) {
        const creditElement = document.getElementById('consumption-credit-display');
        if (!creditElement) {
            console.warn('❌ Consumption credit display element not found');
            return;
        }
        
        const creditCard = creditElement.closest('.usage-data-card');
        if (creditCard) {
            // Add update animation
            creditCard.classList.add('credit-updating');
            
            // Update the value
            setTimeout(() => {
                creditElement.textContent = formatCurrency(credit);
                creditCard.classList.remove('credit-updating');
                
                // Add success flash
                creditCard.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.5)';
                setTimeout(() => {
                    creditCard.style.boxShadow = '';
                }, 1000);
            }, 200);
        } else {
            creditElement.textContent = formatCurrency(credit);
        }
    }
    
    // Monitor consumption section visibility
    function monitorConsumptionSection() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const consumptionSection = document.getElementById('consumption-section');
                    if (consumptionSection && consumptionSection.classList.contains('active')) {
                        // Section is now active, ensure everything is working
                        setTimeout(() => {
                            initConsumptionSection();
                            if (window.GridLayoutEnforcer) {
                                window.GridLayoutEnforcer.init();
                            }
                        }, 100);
                    }
                }
            });
        });
        
        const consumptionSection = document.getElementById('consumption-section');
        if (consumptionSection) {
            observer.observe(consumptionSection, {
                attributes: true,
                attributeFilter: ['class']
            });
        }
    }
    
    // Add consumption section to the credit update system
    function addConsumptionToGlobalUpdates() {
        // Override the original updateCreditDisplay to include consumption
        const originalUpdateCreditDisplay = window.subscriberManagementWidget?.updateCreditDisplay;
        
        if (originalUpdateCreditDisplay) {
            window.subscriberManagementWidget.updateCreditDisplay = function(credit) {
                originalUpdateCreditDisplay.call(this, credit);
                updateConsumptionCreditDisplay(credit);
            };
        }
    }

    // ===========================================================================================
    // INITIALIZATION SYSTEM FOR CONSUMPTION SECTION
    // ===========================================================================================
    
    // Initialize all consumption features when document is ready
    $(document).ready(function() {
        console.log('🚀 Initializing enhanced consumption section features...');
        
        // Initialize consumption section monitoring
        monitorConsumptionSection();
        
        // Add consumption to global credit updates
        addConsumptionToGlobalUpdates();
        
        // Initialize if consumption section is already active
        setTimeout(() => {
            const consumptionSection = document.getElementById('consumption-section');
            if (consumptionSection && consumptionSection.classList.contains('active')) {
                initConsumptionSection();
            }
        }, 1000);
        
        console.log('✅ Consumption section enhancement complete');
        console.log('%c💡 Consumption Console Commands:', 'color: #28a745; font-weight: bold;');
        console.log('  • initConsumptionSection() - Initialize consumption section');
        console.log('  • updateConsumptionCreditDisplay(credit) - Update consumption credit');
        console.log('  • createConsumptionCreditCard() - Create missing credit card');
    });

    // Make functions globally available for debugging
    window.consumptionSectionDebug = {
        init: initConsumptionSection,
        updateCredit: updateConsumptionCreditDisplay,
        createCard: createConsumptionCreditCard,
        refreshData: refreshConsumptionData,
        monitor: monitorConsumptionSection
    };

    // ===========================================================================================
    // CREDIT RECHARGE FUNCTIONS
    // ===========================================================================================

    /**
     * Seleziona un importo per la ricarica
     */
    function selectAmount(amount) {
        // Rimuovi selezione precedente
        $('.amount-btn').removeClass('selected');
        
        // Seleziona il bottone corretto
        $('.amount-btn[data-amount="' + amount + '"]').addClass('selected');
        
        // Pulisci custom input se un bottone è selezionato
        if ($('.amount-btn[data-amount="' + amount + '"]').length > 0) {
            $('#custom-amount-input').val('');
        }
        
        // Salva l'importo selezionato
        $('#selected-amount').val(amount);
        
        // Aggiorna il bottone di ricarica
        updateRechargeButton();
        
        console.log('Amount selected:', amount);
    }

    /**
     * Seleziona un metodo di pagamento
     */
    function selectPaymentMethod(method) {
        // Rimuovi selezione precedente
        $('.payment-method').removeClass('selected');
        
        // Seleziona il metodo
        $('.payment-method[data-method="' + method + '"]').addClass('selected');
        
        // Salva il metodo selezionato
        $('#selected-method').val(method);
        
        // Aggiorna il bottone di ricarica
        updateRechargeButton();
        
        console.log('Payment method selected:', method);
    }

    /**
     * Aggiorna lo stato del bottone di ricarica
     */
    function updateRechargeButton() {
        const amount = $('#selected-amount').val();
        const method = $('#selected-method').val();
        const $btn = $('#proceed-recharge-btn');
        
        if (amount && method && parseFloat(amount) >= 5) {
            $btn.prop('disabled', false).removeClass('disabled');
            $btn.html('<i class="fas fa-shopping-cart"></i> Procedi con la Ricarica - €' + parseFloat(amount).toFixed(2));
        } else {
            $btn.prop('disabled', true).addClass('disabled');
            $btn.html('<i class="fas fa-shopping-cart"></i> Procedi con la Ricarica');
        }
    }

    /**
     * Procede con la ricarica dei crediti
     */
    function proceedWithRecharge() {
        // Verifica che l'oggetto AJAX sia disponibile
        if (typeof subscriberManagementAjax === 'undefined') {
            console.error('subscriberManagementAjax object not found. Check wp_localize_script.');
            showFeedbackMessage('Errore di configurazione: oggetto AJAX non trovato', 'error');
            return;
        }

        if (!subscriberManagementAjax.ajax_url) {
            console.error('AJAX URL not configured');
            showFeedbackMessage('Errore di configurazione: URL AJAX non configurato', 'error');
            return;
        }

        const amount = parseFloat($('#selected-amount').val());
        const method = $('#selected-method').val();
        const $btn = $('#proceed-recharge-btn');
        
        // Validazione
        if (!amount || amount < 5) {
            showFeedbackMessage('Seleziona un importo valido (minimo €5)', 'error');
            return;
        }
        
        if (!method) {
            showFeedbackMessage('Seleziona un metodo di pagamento', 'error');
            return;
        }
        
        // Ottieni l'ID del sottoscrittore
        const subscriberId = getSubscriberId();
        if (!subscriberId) {
            showFeedbackMessage('Errore: ID sottoscrittore non trovato', 'error');
            return;
        }
        
        // UI feedback immediato
        const originalText = $btn.html();
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Elaborazione in corso...').prop('disabled', true).addClass('processing');
        
        // Chiamata AJAX
        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'recharge_credits',
                subscriber_id: subscriberId,
                amount: amount,
                method: method,
                nonce: subscriberManagementAjax.nonce || 'fallback_nonce'
            },
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                    
                    // Aggiorna il display del credito
                    updateAllCreditDisplays(response.data.new_credit);
                    
                    // Reset del form
                    resetRechargeForm();
                    
                } else {
                    const errorMessage = response.data && response.data.message 
                        ? response.data.message 
                        : 'Errore durante la ricarica dei crediti';
                    showFeedbackMessage(errorMessage, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error, xhr.responseText);
                showFeedbackMessage('Errore di connessione durante la ricarica', 'error');
            },
            complete: function() {
                // Ripristina il bottone
                $btn.html(originalText).prop('disabled', false).removeClass('processing');
                updateRechargeButton(); // Ricalcola lo stato del bottone
            }
        });
    }

    /**
     * Reset del form di ricarica
     */
    function resetRechargeForm() {
        $('.amount-btn').removeClass('selected');
        $('.payment-method').removeClass('selected');
        $('#custom-amount-input').val('');
        $('#selected-amount').val('');
        $('#selected-method').val('');
        updateRechargeButton();
    }

    /**
     * Ottiene l'ID del sottoscrittore dall'attributo del widget o dai dati utente
     */
    function getSubscriberId() {
        // Cerca l'ID nel widget container
        const containerId = $('.subscriber-management-widget-container').data('subscriber-id');
        if (containerId) return containerId;
        
        // Fallback: cerca nei hidden fields
        const hiddenId = $('#subscriber-id').val();
        if (hiddenId) return hiddenId;
        
        // Fallback: usa un ID di test per sviluppo
        console.warn('Subscriber ID not found, using test ID');
        return 999; // ID di test
    }

    /**
     * Aggiorna tutti i display del credito
     */
    function updateAllCreditDisplays(newCredit) {
        const formattedCredit = '€' + parseFloat(newCredit).toFixed(2).replace('.', ',');
        
        // Aggiorna tutti i possibili display del credito
        $('#recharge-credit-display, #consumption-credit-display, .credit-value, .credit-amount').each(function() {
            const $element = $(this);
            $element.addClass('credit-updating');
            
            setTimeout(() => {
                $element.text(formattedCredit);
                $element.removeClass('credit-updating').addClass('credit-updated');
                
                setTimeout(() => {
                    $element.removeClass('credit-updated');
                }, 1000);
            }, 200);
        });
    }

    /**
     * Mostra un messaggio di feedback all'utente
     */
    function showFeedbackMessage(message, type = 'info') {
        let $feedback = $('#subscriber-feedback-message');
        
        // Crea l'elemento se non esiste
        if ($feedback.length === 0) {
            $feedback = $('<div id="subscriber-feedback-message" class="feedback-message"></div>');
            $('.subscriber-management-widget-container').after($feedback);
        }
        
        // Imposta il messaggio e il tipo
        $feedback.removeClass('success error info warning')
                .addClass(type)
                .html('<i class="fas fa-' + getFeedbackIcon(type) + '"></i> ' + message)
                .slideDown();
        
        // Auto-hide dopo 5 secondi per messaggi di successo
        if (type === 'success') {
            setTimeout(() => {
                $feedback.slideUp();
            }, 5000);
        }
    }

    /**
     * Ottiene l'icona appropriata per il tipo di messaggio
     */
    function getFeedbackIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Rendi le funzioni globalmente disponibili per il debugging
    window.rechargeDebug = {
        selectAmount: selectAmount,
        selectPaymentMethod: selectPaymentMethod,
        proceedWithRecharge: proceedWithRecharge,
        resetForm: resetRechargeForm,
        updateDisplays: updateAllCreditDisplays,
        showMessage: showFeedbackMessage
    };

})(jQuery);