/**
 * Mega Tooltip - Visualizzazione estesa dell'analisi dei documenti
 * 
 * Questo script aggiunge una funzionalità di tooltip esteso che permette
 * di visualizzare il contenuto dell'analisi in una finestra più grande
 * con possibilità di scorrimento e visualizzazione tabs.
 */
class MegaTooltip {
    constructor(options = {}) {
        this.options = {
            triggerSelector: '.mega-tooltip-trigger',
            containerClass: 'mega-tooltip-container',
            title: 'Visualizzazione Dettagliata',
            closeOnEsc: true,
            closeOnClickOutside: true,
            showTabs: true,
            ...options
        };
        
        this.isVisible = false;
        this.container = null;
        this.trigger = null;
        this.initialized = false;
        
        // Inizializza il tooltip
        this.init();
    }
    
    /**
     * Inizializza il tooltip
     */
    init() {
        // Crea il container del tooltip se non esiste già
        if (!document.querySelector('.' + this.options.containerClass)) {
            this.createTooltipContainer();
        } else {
            this.container = document.querySelector('.' + this.options.containerClass);
        }
        
        // Aggiungi gestori eventi
        this.addEventListeners();
        
        this.initialized = true;
        console.log('MegaTooltip initialized');
    }
    
    /**
     * Crea il container del mega tooltip
     */
    createTooltipContainer() {
        const container = document.createElement('div');
        container.className = this.options.containerClass;
        
        // Crea struttura interna del tooltip
        container.innerHTML = `
            <div class="mega-tooltip-content">
                <div class="mega-tooltip-header">
                    <h2>${this.options.title}</h2>
                    <div class="mega-tooltip-close">&times;</div>
                </div>
                ${this.options.showTabs ? `
                <div class="mega-tooltip-tabs">
                    <div class="mega-tooltip-tab active" data-tab="analysis">Analisi Completa</div>
                    <div class="mega-tooltip-tab" data-tab="keypoints">Punti Chiave</div>
                </div>
                ` : ''}
                <div class="mega-tooltip-body">
                    <div class="mega-tooltip-tab-content active" id="mega-tab-analysis"></div>
                    <div class="mega-tooltip-tab-content" id="mega-tab-keypoints"></div>
                </div>
            </div>
        `;
        
        // Aggiungi il container al body
        document.body.appendChild(container);
        this.container = container;
    }
    
    /**
     * Aggiunge gestori eventi per il tooltip
     */
    addEventListeners() {
        // Gestisci click sui trigger
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest(this.options.triggerSelector);
            if (trigger) {
                e.preventDefault();
                this.trigger = trigger;
                this.show();
            }
        });
        
        // Gestisci click sul pulsante di chiusura
        if (this.container) {
            const closeBtn = this.container.querySelector('.mega-tooltip-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hide());
            }
            
            // Gestisci click al di fuori del contenuto
            if (this.options.closeOnClickOutside) {
                this.container.addEventListener('click', (e) => {
                    if (!e.target.closest('.mega-tooltip-content')) {
                        this.hide();
                    }
                });
            }
            
            // Gestisci click sui tab
            const tabs = this.container.querySelectorAll('.mega-tooltip-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabName = tab.getAttribute('data-tab');
                    this.switchTab(tabName);
                });
            });
        }
        
        // Gestisci tasto ESC
        if (this.options.closeOnEsc) {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isVisible) {
                    this.hide();
                }
            });
        }
    }
    
    /**
     * Mostra il mega tooltip e carica il contenuto
     */
    show() {
        if (!this.container) return;
        
        // Ottieni il contenuto dell'analisi
        this.updateContent();
        
        // Mostra il tooltip
        this.container.classList.add('visible');
        this.isVisible = true;
        
        // Impedisci lo scorrimento del body
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * Nasconde il mega tooltip
     */
    hide() {
        if (!this.container) return;
        
        // Nascondi il tooltip
        this.container.classList.remove('visible');
        this.isVisible = false;
        
        // Ripristina lo scorrimento del body
        document.body.style.overflow = '';
    }
    
    /**
     * Aggiorna il contenuto del tooltip con l'analisi corrente
     */
    updateContent() {
        // Trova i contenuti di analisi e punti chiave
        let analysisContent = '';
        let keyPointsContent = '';
        
        // Ottiene il contenuto dell'analisi completa
        const analysisElement = document.getElementById('analysis-content');
        if (analysisElement) {
            analysisContent = analysisElement.innerHTML;
        }
        
        // Ottiene il contenuto dei punti chiave
        const keyPointsElement = document.getElementById('tab-keypoints');
        if (keyPointsElement) {
            keyPointsContent = keyPointsElement.innerHTML;
        }
        
        // Aggiorna il contenuto del mega tooltip
        if (this.container) {
            const megaAnalysisTab = this.container.querySelector('#mega-tab-analysis');
            const megaKeyPointsTab = this.container.querySelector('#mega-tab-keypoints');
            
            if (megaAnalysisTab) {
                megaAnalysisTab.innerHTML = analysisContent || '<p>Nessuna analisi disponibile</p>';
            }
            
            if (megaKeyPointsTab) {
                megaKeyPointsTab.innerHTML = keyPointsContent || '<p>Nessun punto chiave disponibile</p>';
            }
        }
    }
    
    /**
     * Cambia il tab attivo
     * @param {string} tabName - Il nome del tab da attivare
     */
    switchTab(tabName) {
        if (!this.container) return;
        
        // Rimuovi la classe active da tutti i tab e contenuti
        const tabs = this.container.querySelectorAll('.mega-tooltip-tab');
        const contents = this.container.querySelectorAll('.mega-tooltip-tab-content');
        
        tabs.forEach(tab => tab.classList.remove('active'));
        contents.forEach(content => content.classList.remove('active'));
        
        // Aggiungi la classe active al tab e contenuto selezionato
        const selectedTab = this.container.querySelector(`.mega-tooltip-tab[data-tab="${tabName}"]`);
        const selectedContent = this.container.querySelector(`#mega-tab-${tabName}`);
        
        if (selectedTab) selectedTab.classList.add('active');
        if (selectedContent) selectedContent.classList.add('active');
    }
}

// Inizializza il MegaTooltip quando il documento è pronto
jQuery(document).ready(function($) {
    // Aspetta un breve momento per assicurarsi che gli altri script siano caricati
    setTimeout(() => {
        // Inizializza l'istanza globale del MegaTooltip
        window.megaTooltip = new MegaTooltip({
            title: 'Visualizzazione Estesa Analisi',
            triggerSelector: '.mega-tooltip-trigger'
        });
        
        console.log('MegaTooltip inizializzato e pronto');
    }, 500);
});