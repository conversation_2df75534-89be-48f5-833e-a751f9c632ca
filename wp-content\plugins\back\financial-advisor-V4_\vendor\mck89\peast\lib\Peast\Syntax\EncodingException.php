<?php
/**
 * This file is part of the Peast package
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information refer to the LICENSE file
 * distributed with this source code
 */
namespace Peast\Syntax;

/**
 * Encoding exception class. Encoding errors while scanning the source are
 * thrown using this using this exception class.
 * 
 * <AUTHOR> <<EMAIL>>
 * 
 * @codeCoverageIgnore
 */
class EncodingException extends \Exception
{
}