<?php
/**
 * Class Document_Stats
 *
 * Gestisce le statistiche relative all'utilizzo del visualizzatore di documenti:
 * - Conteggio delle analisi eseguite
 * - Monitoraggio dei token utilizzati
 * - Stima dei costi
 * - Gestione del credito disponibile
 * - Ultime analisi effettuate
 *
 * @package Financial_Advisor
 *
 * @note Risolto errore di sintassi nella funzione subscriberLogout() cambiando
 *       le dichiarazioni const ES6 a var e correggendo le virgolette nelle stringhe.
 *       Questo assicura la compatibilità con i browser più vecchi e mantiene il credito
 *       disponibile durante il processo di logout.
 */

if (!defined('ABSPATH')) {
    exit;
}

class Document_Stats {
    private static $instance = null;
    public $stats_table;
    private $recent_analyses_table;

    /**
     * Get the singleton instance of this class
     *
     * @return Document_Stats
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Costruttore
     */
    public function __construct() {
        global $wpdb;

        // Verifica se è stato richiesto un logout
        if (isset($_GET['loggedout']) && $_GET['loggedout'] === 'true') {
            // Pulisci i cookie
            setcookie('fa_estimated_cost', '', time() - 3600, '/');
            setcookie('fa_actual_cost', '', time() - 3600, '/');

            // Rimuovi il parametro dall'URL per evitare loop
            $clean_url = remove_query_arg('loggedout');
            if ($clean_url !== false) {
                wp_redirect($clean_url);
                exit;
            }
        }

        // Definisci i nomi delle tabelle con prefisso wpcd
        $this->stats_table = 'wpcd_document_user_stats';
        $this->recent_analyses_table = 'wpcd_document_recent_analyses';

        // Registra gli hook AJAX per recuperare i dati delle statistiche
        add_action('wp_ajax_get_document_stats', array($this, 'get_document_stats'));
        add_action('wp_ajax_nopriv_get_document_stats', array($this, 'get_document_stats'));
        // Nuovo hook AJAX per aggiornare la spesa effettiva e contatori dopo un'analisi
        add_action('wp_ajax_update_actual_cost_after_analysis', array($this, 'update_actual_cost_after_analysis'));
        add_action('wp_ajax_nopriv_update_actual_cost_after_analysis', array($this, 'update_actual_cost_after_analysis'));

        // Nuovo endpoint AJAX per resettare le statistiche al caricamento del widget
        add_action('wp_ajax_reset_stats_on_widget_load', array($this, 'reset_stats_on_widget_load'));
        add_action('wp_ajax_nopriv_reset_stats_on_widget_load', array($this, 'reset_stats_on_widget_load'));

        // Nuovo endpoint AJAX per recuperare solo le analisi recenti
        add_action('wp_ajax_get_recent_analyses', array($this, 'get_recent_analyses_ajax'));
        add_action('wp_ajax_nopriv_get_recent_analyses', array($this, 'get_recent_analyses_ajax'));

        // Nuovo endpoint AJAX per pulire i dati utente prima del logout
        add_action('wp_ajax_clean_user_data_before_logout', array($this, 'clean_user_data_before_logout'));
        add_action('wp_ajax_nopriv_clean_user_data_before_logout', array($this, 'clean_user_data_before_logout'));

        // Endpoint di emergenza per forzare il ripristino delle statistiche
        add_action('wp_ajax_force_reset_stats', array($this, 'force_reset_stats'));
        add_action('wp_ajax_nopriv_force_reset_stats', array($this, 'force_reset_stats'));

        // Endpoint per sincronizzazione credito
        add_action('wp_ajax_get_current_credit', array($this, 'get_current_credit'));
        add_action('wp_ajax_nopriv_get_current_credit', array($this, 'get_current_credit'));

        // Pulizia dati utente al logout (manteniamo anche questo come backup)
        add_action('wp_logout', array($this, 'clean_user_recent_analyses_on_logout'));

        // Registrazione dei meta per il conteggio token/costi
        add_action('save_document_analysis', array($this, 'update_stats_on_analysis_save'), 10, 3);

        // Verifica e crea le tabelle se necessario
        add_action('plugins_loaded', array($this, 'check_stats_tables'));

        // Aggancio per l'attivazione del plugin
        register_activation_hook(plugin_dir_path(__DIR__) . 'document-advisor-plugin.php', array($this, 'create_stats_tables'));
    }

    /**
     * Crea le tabelle dedicate alle statistiche utente
     */
    public function create_stats_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Tabella delle statistiche utente
        $sql = "CREATE TABLE IF NOT EXISTS {$this->stats_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            analysis_count int(11) unsigned NOT NULL DEFAULT 0,
            tokens_used bigint(20) unsigned NOT NULL DEFAULT 0,
            credits_available decimal(10,2) NOT NULL DEFAULT 10.00,
            actual_cost decimal(10,2) NOT NULL DEFAULT 0.00,
            tot_cost decimal(10,2) NOT NULL DEFAULT 0.00,
            last_update datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Tabella delle analisi recenti
        $sql_recent = "CREATE TABLE IF NOT EXISTS {$this->recent_analyses_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            analysis_id bigint(20) unsigned NOT NULL,
            title varchar(255) NOT NULL,
            date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            tokens int(11) unsigned NOT NULL DEFAULT 0,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";

        dbDelta($sql_recent);

        // Migra i dati esistenti
        $this->migrate_existing_stats();
    }

    /**
     * Verifica che le tabelle delle statistiche esistano
     */
    public function check_stats_tables() {
        global $wpdb;

        // Controlla se la tabella principale esiste
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->stats_table}'") === $this->stats_table;

        if (!$table_exists) {
            $this->create_stats_tables();
        }
    }

    /**
     * Migra i dati esistenti dalle user_meta alla nuova tabella
     */
    private function migrate_existing_stats() {
        global $wpdb;

        // Verifica se ci sono già dati nella tabella
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->stats_table}");

        if ($count > 0) {
            // La tabella contiene già dati, non serve migrare
            dv_debug_log('Tabella statistiche già popolata, salto la migrazione');
            return;
        }

        dv_debug_log('Avvio migrazione dati statistiche da user_meta a tabella dedicata');

        // Ottieni tutti gli utenti con dati nelle meta
        $users_with_stats = $wpdb->get_results(
            "SELECT user_id, meta_key, meta_value FROM {$wpdb->usermeta}
            WHERE meta_key IN ('document_viewer_tokens_used', 'document_viewer_analysis_count', 'document_viewer_recent_analyses')"
        );

        // Raggruppa i risultati per user_id
        $user_data = array();
        foreach ($users_with_stats as $stat) {
            if (!isset($user_data[$stat->user_id])) {
                $user_data[$stat->user_id] = array(
                    'tokens_used' => 0,
                    'analysis_count' => 0,
                    'recent_analyses' => array()
                );
            }

            if ($stat->meta_key === 'document_viewer_tokens_used') {
                $user_data[$stat->user_id]['tokens_used'] = (int)$stat->meta_value;
            } elseif ($stat->meta_key === 'document_viewer_analysis_count') {
                $user_data[$stat->user_id]['analysis_count'] = (int)$stat->meta_value;
            } elseif ($stat->meta_key === 'document_viewer_recent_analyses') {
                $user_data[$stat->user_id]['recent_analyses'] = maybe_unserialize($stat->meta_value);
            }
        }

        dv_debug_log('Trovati ' . count($user_data) . ' utenti con dati statistici da migrare');

        // Inserisci i dati nella nuova tabella
        foreach ($user_data as $user_id => $stats) {
            // Inserisci statistiche principali
            $wpdb->insert(
                $this->stats_table,
                array(
                    'user_id' => $user_id,
                    'analysis_count' => $stats['analysis_count'],
                    'tokens_used' => $stats['tokens_used'],
                    'credits_available' => 10.00, // Valore predefinito per i crediti
                    'last_update' => current_time('mysql')
                ),
                array('%d', '%d', '%d', '%f', '%s')
            );

            // Inserisci le analisi recenti
            if (!empty($stats['recent_analyses']) && is_array($stats['recent_analyses'])) {
                foreach ($stats['recent_analyses'] as $analysis) {
                    if (isset($analysis['id'], $analysis['title'], $analysis['date'], $analysis['tokens'])) {
                        $wpdb->insert(
                            $this->recent_analyses_table,
                            array(
                                'user_id' => $user_id,
                                'analysis_id' => $analysis['id'],
                                'title' => $analysis['title'],
                                'date' => $analysis['date'],
                                'tokens' => $analysis['tokens']
                            ),
                            array('%d', '%d', '%s', '%s', '%d')
                        );
                    }
                }
            }
        }

        dv_debug_log('Migrazione dati statistiche completata con successo');
    }

    /**
     * Recupera e formatta le statistiche utente
     */
    public function get_document_stats() {
    // Verifica il nonce per sicurezza
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
        wp_send_json_error(array('message' => 'Verifica di sicurezza fallita.'));
        return;
    }

    $user_id = get_current_user_id();
    $is_wp_user = ($user_id > 0);
    $is_subscriber = false;
    $external_user_id = 0;

    // Verifica se l'utente è un sottoscrittore esterno
    if (!$is_wp_user) {
        dv_debug_log('Utente non WordPress rilevato, verifica se è un sottoscrittore esterno');

        // Prima, verifichiamo usando la classe FA_Access_Control se disponibile
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();

            if ($is_subscriber) {
                // Ottieni i dati del sottoscrittore
                $subscriber_data = fa_access_control()->get_current_subscriber_data();

                if (is_array($subscriber_data) && isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                    dv_debug_log('Utente sottoscrittore identificato tramite FA_Access_Control, ID: ' . $external_user_id);
                }
            }
        }

        // Se ancora non abbiamo un ID utente, prova a ottenerlo direttamente dal cookie
        if (!$external_user_id && isset($_COOKIE['fa_subscriber_login'])) {
            try {
                // Tenta di decodificare il cookie - prima rimuovi eventuali barre di escape
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);

                if (is_array($subscriber_data) && isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                    $is_subscriber = true;
                    dv_debug_log('Utente esterno identificato tramite cookie: ' . $external_user_id);
                } else {
                    dv_debug_log('Cookie subscriber presente ma ID non trovato: ' . print_r($subscriber_data, true));
                }
            } catch (Exception $e) {
                dv_debug_log('Errore nella decodifica dei dati del sottoscrittore: ' . $e->getMessage());
            }
        }
    }

    // Se è un utente WordPress standard, ottieni le sue statistiche
    if ($is_wp_user) {
        dv_debug_log('Recupero statistiche per utente WordPress ID: ' . $user_id);

        // Ottieni le statistiche dell'utente
        $stats = $this->get_user_stats($user_id);

        if (empty($stats)) {
            // Inizializza le statistiche se non esistono
            $this->initialize_user_stats($user_id);
            $stats = $this->get_user_stats($user_id);
        }

        // Recupera le analisi recenti
        $recent_analyses = $this->get_recent_analyses($user_id, 5);

        // Recupera le date della prima e dell'ultima analisi
        $dates = array('first' => '-', 'last' => '-');

        if (!empty($recent_analyses)) {
            // L'ultima analisi è in prima posizione nell'array
            $dates['last'] = isset($recent_analyses[0]['date']) ? self::format_date($recent_analyses[0]['date']) : '-';

            // Recupera la data della prima analisi
            global $wpdb;
            $first_analysis = $wpdb->get_var($wpdb->prepare(
                "SELECT MIN(date) FROM {$this->recent_analyses_table} WHERE user_id = %d",
                $user_id
            ));

            if ($first_analysis) {
                $dates['first'] = self::format_date($first_analysis);
            }
        }

        // Prepara i dati per la risposta JSON
        $user_data = get_userdata($user_id);
        $user_roles = $user_data->roles;
        $user_role = !empty($user_roles) ? ucfirst($user_roles[0]) : 'Abbonato';

        // Ottieni il cost_per_token per questo utente (utilizza il valore globale se impostato, altrimenti 0.01)
        $cost_per_token = isset($GLOBALS['fa_user_cost_per_token']) ? $GLOBALS['fa_user_cost_per_token'] : 0.01;

        // Log del tipo di utente e costo associato
        if (function_exists('dv_debug_log')) {
            dv_debug_log("Utente WordPress ID: {$user_id}, Ruolo: {$user_role}, Costo per token: {$cost_per_token}");
        }

        $data = array(
            'user' => array(
                'id' => $user_id,
                'name' => $user_data->display_name,
                'email' => $user_data->user_email,
                'role' => $user_role,
                'avatar' => get_avatar_url($user_id)
            ),
            'stats' => array(
                'analysis_count' => $stats->analysis_count,
                'tokens_used' => $stats->tokens_used,
                'credits_available' => number_format((float)$stats->credits_available, 2, ',', '.'),
                'actual_cost' => number_format((float)$stats->actual_cost, 2, ',', '.'),
                'tot_cost' => number_format((float)$stats->tot_cost, 2, ',', '.'),
                'cost_estimate' => $stats->tokens_used > 0 ? number_format($this->calculate_analysis_cost($stats->tokens_used), 2, ',', '.') : '0,00',
                'cost_per_token' => $cost_per_token
            ),
            'dates' => $dates,
            'recent_analyses' => $recent_analyses
        );

        dv_debug_log('Statistiche recuperate con successo per utente WordPress');

        wp_send_json_success($data);

    }
    // Se è un utente sottoscrittore esterno con ID valido, recupera le sue statistiche
    else if ($is_subscriber && $external_user_id > 0) {
        dv_debug_log('Recupero statistiche per utente esterno ID: ' . $external_user_id);

        // Ottieni le statistiche dell'utente esterno
        $stats = $this->get_external_user_stats($external_user_id);

        if (!$stats) {
            // Se non riusciamo a ottenere le statistiche, invia una risposta con dati vuoti piuttosto che un errore
            $user_name = '';

            if (isset($subscriber_data['name'])) {
                $user_name = $subscriber_data['name'];
                if (isset($subscriber_data['surname'])) {
                    $user_name .= ' ' . $subscriber_data['surname'];
                }
            } else if (isset($subscriber_data['email'])) {
                $user_name = $subscriber_data['email'];
            } else {
                $user_name = 'Subscriber #' . $external_user_id;
            }

            // Crea dati statistici vuoti ma validi per l'utente esterno
            $data = array(
                'user' => array(
                    'id' => $external_user_id,
                    'name' => $user_name,
                    'email' => isset($subscriber_data['email']) ? $subscriber_data['email'] : '',
                    'avatar' => '',
                    'role' => isset($subscriber_data['type']) ? $subscriber_data['type'] : 'Subscriber'
                ),
                'stats' => array(
                    'analysis_count' => 0,
                    'tokens_used' => 0,
                    'credits_available' => isset($subscriber_data['credit']) ? number_format(floatval($subscriber_data['credit']), 2, ',', '.') : '0,00',
                    'actual_cost' => '0,00',
                    'tot_cost' => '0,00',
                    'cost_estimate' => '0,00'
                ),
                'dates' => array('first' => '-', 'last' => '-'),
                'recent_analyses' => array()
            );

            dv_debug_log('Invio statistiche vuote ma valide per utente esterno');
            wp_send_json_success($data);
            return;
        }

        // Recupera le analisi recenti
        $recent_analyses = $this->get_recent_analyses($external_user_id, 5);

        // Prepara i dati per la risposta JSON
        $user_name = '';
        if (isset($subscriber_data['name'])) {
            $user_name = $subscriber_data['name'];
            if (isset($subscriber_data['surname'])) {
                $user_name .= ' ' . $subscriber_data['surname'];
            }
        } else {
            // Cerca i dati dell'utente nella tabella user_subscription con prefisso corretto
            global $wpdb;
            $users_table = 'wpcd_user_subscription';
            $user = $wpdb->get_row($wpdb->prepare(
                "SELECT name, surname, email, tipo_subscription FROM {$users_table} WHERE id = %d",
                $external_user_id
            ), ARRAY_A);

            if ($user) {
                $user_name = $user['name'] . ' ' . $user['surname'];
            } else {
                $user_name = 'Subscriber #' . $external_user_id;
            }
        }

        // Ottieni il tipo di abbonamento
        $subscription_type = isset($subscriber_data['type']) ? $subscriber_data['type'] :
                           (isset($user['tipo_subscription']) ? $user['tipo_subscription'] : 'Subscriber');

        // Ottieni il cost_per_token specifico per questo tipo di sottoscrizione
        global $wpdb;
        $type_cost = $wpdb->get_var($wpdb->prepare(
            "SELECT cost_per_token FROM wpcd_type_subscription WHERE type_sub = %s",
            $subscription_type
        ));

        // Se esiste un valore specifico per questo tipo, usalo
        if ($type_cost !== null) {
            $cost_per_token = (float) $type_cost;
            // Salva il valore in una variabile globale per accessibilità da altre funzioni
            $GLOBALS['fa_user_cost_per_token'] = $cost_per_token;
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Trovato cost_per_token specifico ({$cost_per_token}) dal tipo di sottoscrizione {$subscription_type}");
            }
        } else {
            // Fallback al valore globale o al valore predefinito
            $cost_per_token = isset($GLOBALS['fa_user_cost_per_token']) ? $GLOBALS['fa_user_cost_per_token'] : 0.01;
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Nessun costo specifico trovato per il tipo '{$subscription_type}', utilizzo valore predefinito: {$cost_per_token}");
            }
        }

        // Log del tipo di utente esterno e costo associato
        if (function_exists('dv_debug_log')) {
            dv_debug_log("Utente esterno ID: {$external_user_id}, Tipo abbonamento: {$subscription_type}, Costo per token: {$cost_per_token}");
        }

        $data = array(
            'user' => array(
                'id' => $external_user_id,
                'name' => $user_name,
                'email' => isset($subscriber_data['email']) ? $subscriber_data['email'] :
                          (isset($user['email']) ? $user['email'] : ''),
                'avatar' => '',
                'role' => $subscription_type
            ),
            'stats' => array(
                'analysis_count' => $stats->analysis_count,
                'tokens_used' => $stats->tokens_used,
                'credits_available' => number_format((float)$stats->credits_available, 2, ',', '.'),
                'actual_cost' => number_format((float)$stats->actual_cost, 2, ',', '.'),
                'tot_cost' => number_format((float)$stats->tot_cost, 2, ',', '.'),
                'cost_estimate' => $stats->tokens_used > 0 ? number_format($this->calculate_analysis_cost($stats->tokens_used), 2, ',', '.') : '0,00',
                'cost_per_token' => $cost_per_token
            ),
            'dates' => array('first' => '-', 'last' => '-'),
            'recent_analyses' => $recent_analyses
        );

        // Aggiungi le date se ci sono analisi recenti
        if (!empty($recent_analyses)) {
            $data['dates']['last'] = isset($recent_analyses[0]['date']) ? self::format_date($recent_analyses[0]['date']) : '-';

            global $wpdb;
            $first_date = $wpdb->get_var($wpdb->prepare(
                "SELECT MIN(date) FROM {$this->recent_analyses_table} WHERE user_id = %d",
                $external_user_id
            ));

            if ($first_date) {
                $data['dates']['first'] = self::format_date($first_date);
            }
        }

        dv_debug_log('Statistiche recuperate con successo per utente esterno');

        wp_send_json_success($data);
    }
    // Se non abbiamo un utente valido, invia una risposta di successo con dati vuoti
    else {
        // Questo è importante: invece di un errore invia una risposta di successo con dati vuoti
        // per assicurare che l'interfaccia utente mostri informazioni minime senza errori
        $data = array(
            'user' => array(
                'id' => 0,
                'name' => 'Visitatore',
                'email' => '',
                'avatar' => '',
                'role' => 'guest'
            ),
            'stats' => array(
                'analysis_count' => 0,
                'tokens_used' => 0,
                'credits_available' => '0,00',
                'actual_cost' => '0,00',
                'tot_cost' => '0,00',
                'cost_estimate' => '0,00'
            ),
            'dates' => array('first' => '-', 'last' => '-'),
            'recent_analyses' => array()
        );

        dv_debug_log('Nessun utente valido identificato, invio statistiche vuote');
        wp_send_json_success($data);
    }
}

    /**
     * Aggiorna le statistiche quando viene salvata un'analisi
     *
     * @param int $analysis_id ID dell'analisi salvata
     * @param array $analysis_data Dati dell'analisi
     * @param int $token_count Conteggio token utilizzati
     */
    public function update_stats_on_analysis_save($analysis_id, $analysis_data, $token_count) {
        // Utilizza il nuovo sistema unificato di identificazione utente
        $user_info = fa_get_current_user_info();
        $user_id = $user_info['id'];
        $user_type = $user_info['type'];

        dv_debug_log('Aggiornamento statistiche per utente: ID=' . $user_id . ', Tipo=' . $user_type);

        // Verifica se questa è un'analisi salvata esplicitamente dall'utente
        $is_explicit_save = isset($analysis_data['saved_explicitly']) && $analysis_data['saved_explicitly'] === true;
        dv_debug_log('Salvataggio esplicito: ' . ($is_explicit_save ? 'sì' : 'no'));

        // Se è un utente subscriber (esterno), aggiorna le statistiche nella tabella User_subscription
        if ($user_type === 'subscriber' && $user_id > 0) {
            // Usa il sistema transazionale per aggiornare le statistiche
            $stats_update = [
                'tokens_used' => $token_count,
                'analysis_count' => 1,
                'cost' => $this->calculate_analysis_cost($token_count)
            ];

            $result = fa_update_user_stats_transaction($user_id, 'subscriber', $stats_update);

            if ($result) {
                dv_debug_log('Statistiche aggiornate con successo per utente subscriber ID: ' . $user_id);

                // Salva l'analisi recente SOLO SE non è un salvataggio esplicito
                // (perché per i non-WordPress users l'analisi è già stata salvata in wpcd_user_subscription_stats durante l'analisi)
                if (!$is_explicit_save) {
                    $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $token_count);
                } else {
                    dv_debug_log('Salvataggio analisi recente saltato (già salvata) per utente subscriber ID: ' . $user_id);
                }
            } else {
                dv_debug_log('Errore nell\'aggiornamento delle statistiche per utente subscriber ID: ' . $user_id);
            }

            // Trigger JavaScript event to refresh stats panel
            do_action('document_analysis_saved', $analysis_id, $user_id);
            return;
        }

        // Se è un utente WordPress, aggiorna le statistiche nella tabella stats
        if ($user_type === 'wordpress' && $user_id > 0) {
            // Usa il sistema transazionale per aggiornare le statistiche
            $stats_update = [
                'tokens_used' => $token_count,
                'analysis_count' => 1,
                'cost' => $this->calculate_analysis_cost($token_count)
            ];

            $result = fa_update_user_stats_transaction($user_id, 'wordpress', $stats_update);

            if ($result) {
                dv_debug_log('Statistiche aggiornate con successo per utente WordPress ID: ' . $user_id);
                // Per gli utenti WordPress, salviamo sempre l'analisi recente
                // (Gli utenti WordPress usano una tabella diversa per le analisi recenti)
                $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $token_count);
            } else {
                dv_debug_log('Errore nell\'aggiornamento delle statistiche per utente WordPress ID: ' . $user_id);
            }

            // Trigger JavaScript event to refresh stats panel
            do_action('document_analysis_saved', $analysis_id, $user_id);
            return;
        }

        // Se è un utente guest, usa un ID temporaneo
        if ($user_type === 'guest') {
            // Verifica se questa è un'analisi salvata esplicitamente dall'utente
            $is_explicit_save = isset($analysis_data['saved_explicitly']) && $analysis_data['saved_explicitly'] === true;

            // Usa un ID temporaneo per gli utenti guest basato sulla sessione
            if (session_id()) {
                $user_id = 900000 + abs(crc32(session_id()) % 99999);
                dv_debug_log('ID utente guest temporaneo assegnato in update_stats_on_analysis_save: ' . $user_id);
                dv_debug_log('Salvataggio esplicito per guest: ' . ($is_explicit_save ? 'sì' : 'no'));

                // Per gli utenti guest, aggiorniamo direttamente senza transazione
                global $wpdb;

                // Ottieni o crea le statistiche dell'utente
                $stats = $this->get_user_stats($user_id);

                if (empty($stats)) {
                    $this->initialize_user_stats($user_id);
                    $stats = $this->get_user_stats($user_id);
                }

                // Calcola il costo dell'analisi
                $cost = $this->calculate_analysis_cost($token_count);

                // Aggiorna le statistiche
                $wpdb->update(
                    'wpcd_fa_user_stats',
                    [
                        'tokens_used' => $stats->tokens_used + $token_count,
                        'analysis_count' => $stats->analysis_count + 1,
                        'total_cost' => $stats->total_cost + $cost,
                        'credits_available' => $stats->credits_available - $cost,
                        'last_analysis_date' => current_time('mysql')
                    ],
                    ['id' => $user_id],
                    ['%d', '%d', '%f', '%f', '%s'],
                    ['%d']
                );

                // Gli utenti guest usano la stessa tabella di WordPress, quindi possiamo salvare sempre
                $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $token_count);

                // Trigger JavaScript event to refresh stats panel
                do_action('document_analysis_saved', $analysis_id, $user_id);

                dv_debug_log('Statistiche aggiornate per utente guest temporaneo ID: ' . $user_id);
                return;
            }

            dv_debug_log('Sessione non disponibile per utente guest, impossibile assegnare ID temporaneo');
        }

        dv_debug_log('Tipo utente non riconosciuto o ID non valido: ' . $user_type . ', ID=' . $user_id);

        // Fallback per utenti non identificati: salva comunque l'analisi se possibile
        if ($user_id > 0) {
            $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $token_count);

            // Trigger JavaScript event to refresh stats panel
            do_action('document_analysis_saved', $analysis_id, $user_id);
        }
    }

    /**
     * Aggiorna la spesa effettiva e incrementa i contatori dopo un'analisi completata
     * Questo metodo è chiamato via AJAX quando l'API ha generato la risposta
     */
    /**
     * Aggiorna le statistiche di un utente esterno (non WordPress) nella tabella User_subscription
     *
     * @param int $external_user_id ID dell'utente esterno
     * @param int $token_count Conteggio token utilizzati
     * @return bool Esito dell'operazione
     */
    public function update_external_user_stats($external_user_id, $token_count) {
        global $wpdb;

        // Calcola il costo dell'analisi
        $cost = $this->calculate_analysis_cost($token_count);

        // Nome della tabella User_subscription con prefisso corretto
        $users_table = 'wpcd_user_subscription';

        // Verifica che l'utente esista
        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$users_table} WHERE id = %d",
            $external_user_id
        ));

        if (!$user) {
            dv_debug_log("Utente esterno con ID $external_user_id non trovato nella tabella User_subscription");
            return false;
        }

        // Verifica che l'utente abbia credito sufficiente
        if ($user->credit < $cost) {
            dv_debug_log("Utente esterno $external_user_id non ha credito sufficiente. Disponibile: {$user->credit}, Richiesto: $cost");
            // In questo caso potremmo implementare una gestione specifica per mancanza di credito
            // Per ora continuiamo comunque, ma in futuro potremmo bloccare l'analisi
        }

        // Aggiorna i campi statistici
        $new_credit = max(0, $user->credit - $cost);
        $new_analysis_count = $user->analysis_count + 1;
        $new_tokens_used = $user->tokens_used + $token_count;
        $new_actual_cost = $user->actual_cost + $cost;
        $new_tot_cost = $user->tot_cost + $cost;

        $result = $wpdb->update(
            $users_table,
            array(
                'credit' => $new_credit,
                'analysis_count' => $new_analysis_count,
                'tokens_used' => $new_tokens_used,
                'actual_cost' => $new_actual_cost,
                'tot_cost' => $new_tot_cost,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $external_user_id),
            array('%f', '%d', '%d', '%f', '%f', '%s'),
            array('%d')
        );

        if ($result === false) {
            dv_debug_log("Errore nell'aggiornamento delle statistiche per l'utente esterno $external_user_id: " . $wpdb->last_error);
            return false;
        }

        dv_debug_log("Statistiche utente esterno $external_user_id aggiornate. Nuovo credito: $new_credit, Analisi: $new_analysis_count, Token: $new_tokens_used, Actual cost: $new_actual_cost, Tot cost: $new_tot_cost");
        return true;
    }

    /**
     * Salva un'analisi recente per un utente
     *
     * @param int $user_id ID dell'utente
     * @param int $analysis_id ID dell'analisi
     * @param array $analysis_data Dati dell'analisi
     * @param int $token_count Conteggio token utilizzati
     */
    private function save_recent_analysis($user_id, $analysis_id, $analysis_data, $token_count) {
        global $wpdb;

        // Log per debug
        dv_debug_log(sprintf(
            "Salvataggio analisi recente: user_id=%d, analysis_id=%d, title=%s, tokens=%d, salvataggio_esplicito=%s",
            $user_id, $analysis_id,
            isset($analysis_data['title']) ? $analysis_data['title'] : 'Analisi senza titolo',
            $token_count,
            isset($analysis_data['saved_explicitly']) && $analysis_data['saved_explicitly'] ? 'sì' : 'no'
        ));

        // Determina se l'utente è un utente WordPress o un utente esterno
        $is_wp_user = true;
        $user_type = "wordpress";

        // Controlla se abbiamo un utente esterno
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            if (fa_access_control()->is_current_user_subscriber()) {
                $subscriber_data = fa_access_control()->get_current_subscriber_data();
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $is_wp_user = false;
                    $user_type = "subscriber";
                }
            }
        }

        dv_debug_log("save_recent_analysis: Tipo utente identificato: " . $user_type . " (ID: " . $user_id . ")");

        // Se è un utente WordPress, usa la tabella standard delle analisi recenti
        if ($is_wp_user) {
            // Get client time for document_analysis
            $timezone_offset = isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) : 0;
            $client_time = date('Y-m-d H:i:s', time() - ($timezone_offset * 60));

            // Salva l'analisi nella tabella delle recenti per utenti WP
            $result = $wpdb->insert(
                $this->recent_analyses_table,
                array(
                    'user_id' => $user_id,
                    'analysis_id' => $analysis_id,
                    'title' => isset($analysis_data['title']) ? $analysis_data['title'] : 'Analisi senza titolo',
                    'date' => $client_time,
                    'tokens' => $token_count
                ),
                array('%d', '%d', '%s', '%s', '%d')
            );

            if ($result === false) {
                dv_debug_log("Errore nel salvataggio dell'analisi recente (utente WP): " . $wpdb->last_error);
            }

            // Limita le analisi recenti a 10
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$this->recent_analyses_table}
                WHERE user_id = %d AND id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM {$this->recent_analyses_table}
                        WHERE user_id = %d ORDER BY date DESC LIMIT 10
                    ) AS temp
                )",
                $user_id, $user_id
            ));
        } else {
            // Per utenti non WordPress, usa la tabella user_subscription_stats con il prefisso corretto
            $subscription_stats_table = $wpdb->prefix . 'user_subscription_stats';

            // Recupera il nome utente dal sistema di gestione utenti esterni
            $username = '';
            $subscriber_data = fa_access_control()->get_current_subscriber_data();
            if (isset($subscriber_data['username'])) {
                $username = $subscriber_data['username'];
            }

            // Get client time for created_at
            $timezone_offset = isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) : 0;
            $client_time = date('Y-m-d H:i:s', time() - ($timezone_offset * 60));

            dv_debug_log("Subscriber user, saving to table: {$subscription_stats_table} with ID: {$user_id}, username: {$username}");

            // Salva l'analisi nella tabella delle statistiche per utenti non WP
            $result = $wpdb->insert(
                $subscription_stats_table,
                array(
                    'User_id' => $user_id,
                    'Username' => $username,
                    'Title_analysis' => isset($analysis_data['title']) ? $analysis_data['title'] : 'Analisi senza titolo',
                    'Tokens_used' => $token_count,
                    'Actual_Cost' => $this->calculate_analysis_cost($token_count),
                    'Created_at' => $client_time
                ),
                array('%d', '%s', '%s', '%d', '%f', '%s')
            );

            if ($result === false) {
                dv_debug_log("Errore nel salvataggio dell'analisi recente (utente esterno): " . $wpdb->last_error);
            } else {
                dv_debug_log("Analisi salvata con successo nella tabella {$subscription_stats_table} per l'utente esterno ID: {$user_id}");
            }

            // Limita le analisi recenti a 10 anche per gli utenti non WordPress
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$subscription_stats_table}
                WHERE User_id = %d AND ID NOT IN (
                    SELECT ID FROM (
                        SELECT ID FROM {$subscription_stats_table}
                        WHERE User_id = %d ORDER BY Created_at DESC LIMIT 10
                    ) AS temp
                )",
                $user_id, $user_id
            ));
        }
    }

    /**
     * Recupera le statistiche di un utente esterno (non WordPress) dalla tabella User_subscription
     *
     * @param int $external_user_id ID dell'utente esterno
     * @return object|null Oggetto con le statistiche dell'utente esterno
     */
    public function get_external_user_stats($external_user_id) {
        global $wpdb;

        // Nome della tabella User_subscription con prefisso corretto
        $users_table = 'wpcd_user_subscription';

        // Recupera i dati dell'utente direttamente dalla tabella
        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$users_table} WHERE id = %d",
            $external_user_id
        ));

        if (!$user) {
            dv_debug_log('Utente esterno non trovato: ' . $external_user_id);
            return null;
        }

        // Recupera le analisi recenti solo per la visualizzazione
        $recent_analyses = $this->get_recent_analyses($external_user_id, 5); // Ultime 5 analisi

        // Crea un oggetto di statistiche usando i valori dalla tabella
        $stats = new stdClass();
        $stats->user_id = $external_user_id;
        $stats->analysis_count = intval($user->analysis_count);
        $stats->tokens_used = intval($user->tokens_used);
        // Supporta entrambi i nomi del campo: credit e credits_available
        $stats->credits_available = property_exists($user, 'credits_available') ? floatval($user->credits_available) : floatval($user->credit);
        $stats->actual_cost = floatval($user->actual_cost);
        $stats->tot_cost = floatval($user->tot_cost);

        dv_debug_log('Statistiche utente esterno recuperate con successo dalla tabella User_subscription');

        return $stats;
    }

    /**
     * Ottiene la data e l'ora corrente in formato MySQL
     *
     * @return string Data e ora in formato MySQL
     */
    private function get_current_mysql_datetime() {
        global $wpdb;
        if (isset($wpdb) && method_exists($wpdb, 'get_var')) {
            return $wpdb->get_var('SELECT NOW()');
        }
        return date('Y-m-d H:i:s');
    }

    /**
     * Invia una risposta JSON standardizzata
     *
     * @param array $response Array con i dati della risposta
     */
    private function send_json_response($response) {
        if (!headers_sent()) {
            header('Content-Type: application/json');
        }

        if (function_exists('wp_die')) {
            wp_die(json_encode($response));
        } else {
            die(json_encode($response));
        }
    }

    /**
     * Invia una risposta JSON di errore
     *
     * @param string $message Messaggio di errore
     */
    private function send_json_error($message) {
        $this->send_json_response(array(
            'success' => false,
            'message' => $message
        ));
    }

    public function update_actual_cost_after_analysis() {
        global $wpdb;

        // Inizializza la risposta di default
        $response = array(
            'success' => false,
            'message' => 'Errore sconosciuto',
            'data' => null
        );

        try {
            // Verifica se la richiesta è stata fatta tramite AJAX
            if (defined('DOING_AJAX') && DOING_AJAX) {
                // Assicurati che le funzioni di WordPress siano disponibili
                if (!function_exists('wp_verify_nonce')) {
                    if (defined('ABSPATH') && file_exists(ABSPATH . 'wp-includes/pluggable.php')) {
                        require_once(ABSPATH . 'wp-includes/pluggable.php');
                    } else {
                        throw new Exception('Impossibile caricare le funzionalità di WordPress');
                    }
                }

                // Verifica il nonce per sicurezza
                if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
                    throw new Exception('Verifica di sicurezza fallita');
                }

                // Utilizza il sistema unificato di identificazione utente
                $user_info = fa_get_current_user_info();
                $user_id = $user_info['id'];
                $user_type = $user_info['type'];

                if (!$user_id) {
                    throw new Exception('Utente non riconosciuto');
                }

                // Ottieni i parametri dalla richiesta
                $cost = isset($_POST['cost']) ? (float) $_POST['cost'] : 0;
                $tokens = isset($_POST['tokens']) ? (int) $_POST['tokens'] : 0;

                if ($cost <= 0) {
                    throw new Exception('Costo non valido');
                }

                if ($tokens <= 0) {
                    throw new Exception('Conteggio token non valido');
                }

                // Gestione diversa in base al tipo di utente
                if ($user_type === 'subscriber') {
                    // Logica per utenti esterni (subscriber)
                    $stats = $this->get_external_user_stats($user_id);

                    if (!$stats) {
                        throw new Exception('Utente esterno non trovato');
                    }

                    // Calcola il nuovo costo totale e i token utilizzati
                    $new_tot_cost = $stats->tot_cost + $cost;
                    $tokens_used = $stats->tokens_used + $tokens;
                    $credits_available = max(0, $stats->credits_available - $cost);
                    $actual_cost = $stats->actual_cost + $cost;

                    // Aggiorna i valori nella tabella user_subscription
                    $result = $wpdb->update(
                        'wpcd_user_subscription',
                        array(
                            'credit' => $credits_available, // Usa 'credit' invece di 'credits_available'
                            'tokens_used' => $tokens_used,
                            'actual_cost' => $actual_cost,
                            'tot_cost' => $new_tot_cost,
                            'analysis_count' => $stats->analysis_count + 1,
                            // Usa data client-side
                            'last_update' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
                        ),
                        array('id' => $user_id),
                        array('%f', '%d', '%f', '%f', '%d', '%s'),
                        array('%d')
                    );

                    // Salva anche l'analisi recente
                    $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : time();
                    $analysis_title = isset($_POST['analysis_title']) ? sanitize_text_field($_POST['analysis_title']) : 'Analisi documento';
                    $analysis_data = array('title' => $analysis_title);
                    $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $tokens);

                    if ($result === false) {
                        throw new Exception("Errore durante l'aggiornamento delle statistiche per l'utente esterno {$user_id}");
                    }

                    $response['data'] = array(
                        'tokens_used' => $tokens_used,
                        'tot_cost' => $new_tot_cost,
                        'credits_available' => $credits_available,
                        'actual_cost' => $actual_cost,
                        'analysis_count' => $stats->analysis_count + 1
                    );

                    // Ottieni il cost_per_token specifico per il tipo di sottoscrizione
                    $subscription_type = $wpdb->get_var($wpdb->prepare(
                        "SELECT tipo_subscription FROM wpcd_user_subscription WHERE id = %d",
                        $user_id
                    ));

                    // Recupera il cost_per_token specifico per questo tipo di abbonamento
                    if (!empty($subscription_type)) {
                        $type_cost = $wpdb->get_var($wpdb->prepare(
                            "SELECT cost_per_token FROM wpcd_type_subscription WHERE type_sub = %s",
                            $subscription_type
                        ));

                        if ($type_cost !== null) {
                            $cost_per_token = (float) $type_cost;
                            $GLOBALS['fa_user_cost_per_token'] = $cost_per_token;
                            dv_debug_log("Trovato cost_per_token specifico ({$cost_per_token}) per tipo di sottoscrizione {$subscription_type}");
                        } else {
                            $cost_per_token = isset($GLOBALS['fa_user_cost_per_token']) ? $GLOBALS['fa_user_cost_per_token'] : 0.01;
                            dv_debug_log("Nessun cost_per_token trovato per tipo '{$subscription_type}', uso valore fallback: {$cost_per_token}");
                        }
                    } else {
                        $cost_per_token = isset($GLOBALS['fa_user_cost_per_token']) ? $GLOBALS['fa_user_cost_per_token'] : 0.01;
                        dv_debug_log("Utente esterno {$user_id} senza tipo di sottoscrizione, uso valore predefinito: {$cost_per_token}");
                    }

                    // Aggiungi cost_per_token ai dati della risposta
                    $response['data']['cost_per_token'] = $cost_per_token;

                    // Log per verificare i dati aggiornati
                    dv_debug_log(sprintf(
                        "Statistiche aggiornate per utente esterno %d: tokens=%d, tot_cost=%.2f, credits=%.2f, actual_cost=%.2f, analyses=%d, cost_per_token=%.5f",
                        $user_id, $tokens_used, $new_tot_cost, $credits_available, $actual_cost, $stats->analysis_count + 1, $cost_per_token
                    ));

                    // Log specifico per il credito disponibile (per debug)
                    dv_debug_log("CREDITO DISPONIBILE AGGIORNATO per utente esterno {$user_id}: {$credits_available}");

                } elseif ($user_type === 'wordpress') {
                    // Logica per utenti WordPress
                    $stats = $this->get_user_stats($user_id);

                    if (!$stats) {
                        $this->initialize_user_stats($user_id);
                        $stats = (object) array(
                            'tokens_used' => 0,
                            'tot_cost' => 0,
                            'analysis_count' => 0,
                            'credits_available' => 10.00
                        );
                    }

                    // Calcola il nuovo costo totale e i token utilizzati
                    $new_tot_cost = $stats->tot_cost + $cost;
                    $tokens_used = $stats->tokens_used + $tokens;
                    $credits_available = max(0, $stats->credits_available - $cost);
                    $actual_cost = property_exists($stats, 'actual_cost') ? $stats->actual_cost + $cost : $cost;

                    // Aggiorna i valori nella tabella delle statistiche
                    $result = $wpdb->update(
                        $this->stats_table,
                        array(
                            'tokens_used' => $tokens_used,
                            'tot_cost' => $new_tot_cost,
                            'credits_available' => $credits_available,
                            'actual_cost' => $actual_cost,
                            'analysis_count' => $stats->analysis_count + 1,
                            // Usa data client-side
                            'last_update' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
                        ),
                        array('user_id' => $user_id),
                        array('%d', '%f', '%f', '%f', '%d', '%s'),
                        array('%d')
                    );

                    // Salva anche l'analisi recente
                    $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : time();
                    $analysis_title = isset($_POST['analysis_title']) ? sanitize_text_field($_POST['analysis_title']) : 'Analisi documento';
                    $analysis_data = array('title' => $analysis_title);
                    $this->save_recent_analysis($user_id, $analysis_id, $analysis_data, $tokens);

                    if ($result === false) {
                        throw new Exception("Errore durante l'aggiornamento delle statistiche per l'utente WordPress {$user_id}");
                    }

                    $response['data'] = array(
                        'tokens_used' => $tokens_used,
                        'tot_cost' => $new_tot_cost,
                        'credits_available' => $credits_available,
                        'actual_cost' => $actual_cost,
                        'analysis_count' => $stats->analysis_count + 1
                    );

                    // Per gli utenti WordPress, utilizziamo sempre il valore predefinito
                    $cost_per_token = isset($GLOBALS['fa_user_cost_per_token']) ? $GLOBALS['fa_user_cost_per_token'] : 0.01;

                    // Aggiungi cost_per_token ai dati della risposta
                    $response['data']['cost_per_token'] = $cost_per_token;

                    // Log dettagliato per il debugging
                    dv_debug_log("Utente WordPress ID: {$user_id} - utilizzando cost_per_token: {$cost_per_token}");

                    // Log per verificare i dati aggiornati
                    dv_debug_log(sprintf(
                        "Statistiche aggiornate per utente WordPress %d: tokens=%d, tot_cost=%.2f, credits=%.2f, actual_cost=%.2f, analyses=%d, cost_per_token=%.5f",
                        $user_id, $tokens_used, $new_tot_cost, $credits_available, $actual_cost, $stats->analysis_count + 1, $cost_per_token
                    ));
                } else {
                    throw new Exception('Tipo di utente non supportato');
                }

                // Se siamo arrivati qui, tutto è andato bene
                $response['success'] = true;
                $response['message'] = 'Statistiche aggiornate con successo';

            } else {
                throw new Exception('Richiesta non valida');
            }

        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Errore in update_actual_cost_after_analysis: ' . $e->getMessage());
            } else {
                error_log('Errore in update_actual_cost_after_analysis: ' . $e->getMessage());
            }
        }

        // Invia la risposta JSON
        $this->send_json_response($response);
    }

    /**
     * Calcola il costo di un'analisi in base ai token utilizzati
     *
     * @param int $token_count Numero di token utilizzati
     * @return float Costo dell'analisi
     */
    private function calculate_analysis_cost($token_count) {
        // Costo per token predefinito (in euro) per utenti WordPress
        $cost_per_token = 0.01;

        // Verifica se l'utente è un sottoscrittore esterno
        $user_info = fa_get_current_user_info();
        if (!empty($user_info) && isset($user_info['type']) && $user_info['type'] === 'subscriber' && !empty($user_info['id'])) {
            global $wpdb;

            // Ottieni il tipo di sottoscrizione dell'utente
            $subscription_type = $wpdb->get_var($wpdb->prepare(
                "SELECT tipo_subscription FROM wpcd_user_subscription WHERE id = %d",
                $user_info['id']
            ));

            if (!empty($subscription_type)) {
                // Recupera il cost_per_token per questo tipo di sottoscrizione
                $type_cost = $wpdb->get_var($wpdb->prepare(
                    "SELECT cost_per_token FROM wpcd_type_subscription WHERE type_sub = %s",
                    $subscription_type
                ));

                // Se esiste un valore specifico per questo tipo, usalo
                if ($type_cost !== null) {
                    $cost_per_token = (float) $type_cost;

                    // Log per il debug della tariffazione
                    if (function_exists('dv_debug_log')) {
                        dv_debug_log("Usando cost_per_token specifico ({$cost_per_token}) per utente {$user_info['id']} con tipo di sottoscrizione {$subscription_type}");
                    }
                } else {
                    // Log per eventuali errori: tipo di sottoscrizione non trovato nella tabella wpcd_type_subscription
                    if (function_exists('dv_debug_log')) {
                        dv_debug_log("ATTENZIONE: Nessun cost_per_token trovato per tipo di sottoscrizione '{$subscription_type}' nella tabella wpcd_type_subscription");
                    }
                }
            } else {
                // Log per eventuali errori: utente senza tipo di sottoscrizione
                if (function_exists('dv_debug_log')) {
                    dv_debug_log("ATTENZIONE: Utente subscriber ID {$user_info['id']} non ha un tipo di sottoscrizione definito");
                }
            }
        }

        // Salva il valore per uso futuro - spostato fuori dal blocco if per essere sempre impostato
        $GLOBALS['fa_user_cost_per_token'] = $cost_per_token;

        // Calcola il costo
        return $token_count * $cost_per_token;
    }

    /**
     * Inizializza le statistiche per un nuovo utente
     *
     * @param int $user_id ID utente
     */
    public function initialize_user_stats($user_id) {
        global $wpdb;

        // Controlla se esistono già statistiche per l'utente
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->stats_table} WHERE user_id = %d",
            $user_id
        ));

        if ($exists) {
            return;
        }

        // Inserisci un nuovo record per l'utente
        $wpdb->insert(
            $this->stats_table,
            array(
                'user_id' => $user_id,
                'analysis_count' => 0,
                'tokens_used' => 0,
                'credits_available' => 10.00, // Crediti iniziali
                'actual_cost' => 0.00, // Spesa effettiva iniziale
                'tot_cost' => 0.00, // Costo totale iniziale
                'last_update' => current_time('mysql')
            ),
            array('%d', '%d', '%d', '%f', '%f', '%f', '%s')
        );

        dv_debug_log("Creato nuovo record statistiche per utente $user_id con 10.00 crediti");
    }

    /**
     * Aggiunge credito all'account dell'utente
     *
     * @param int $user_id ID utente
     * @param float $amount Quantità di credito da aggiungere
     * @return bool Esito dell'operazione
     */
    public function add_user_credit($user_id, $amount) {
        if ($amount <= 0) {
            return false;
        }

        global $wpdb;

        // Ottieni o crea le statistiche dell'utente
        $stats = $this->get_user_stats($user_id);

        if (empty($stats)) {
            $this->initialize_user_stats($user_id);
            $stats = $this->get_user_stats($user_id);
        }

        // Aggiorna il credito disponibile
        $result = $wpdb->update(
            $this->stats_table,
            array(
                'credits_available' => $stats->credits_available + $amount,
                'last_update' => current_time('mysql')
            ),
            array('user_id' => $user_id),
            array('%f', '%s'),
            array('%d')
        );

        if ($result !== false) {
            dv_debug_log("Aggiunti $amount crediti all'utente $user_id. Nuovo totale: " . ($stats->credits_available + $amount));
        }

        return $result !== false;
    }

    /**
     * Ottieni le statistiche dell'utente dalla tabella dedicata
     *
     * @param int $user_id ID utente
     * @return object|null Oggetto con le statistiche dell'utente
     */
    public function get_user_stats($user_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->stats_table} WHERE user_id = %d",
            $user_id
        ));
    }

    /**
     * Ottieni le analisi recenti dell'utente
     *
     * @param int $user_id ID utente
     * @param int $limit Numero massimo di analisi da recuperare
     * @return array Lista delle analisi recenti
     */
    private function get_recent_analyses($user_id, $limit = 5) {
        global $wpdb;

        // Determina se l'utente è un utente WordPress o un utente esterno
        $is_wp_user = true;

        // Controlla se abbiamo un utente esterno
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            if (fa_access_control()->is_current_user_subscriber()) {
                $subscriber_data = fa_access_control()->get_current_subscriber_data();
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $is_wp_user = false;
                }
            }
        }

        if ($is_wp_user) {
            // Per utenti WordPress, usa la tabella standard delle analisi recenti
            $analyses = $wpdb->get_results($wpdb->prepare(
                "SELECT analysis_id as id, title, date, tokens
                FROM {$this->recent_analyses_table}
                WHERE user_id = %d
                ORDER BY date DESC
                LIMIT %d",
                $user_id, $limit
            ), ARRAY_A);
        } else {
            // Per utenti non WordPress, usa la nuova tabella wpcd_user_subscription_stats

            $table_name = $wpdb->prefix . 'user_subscription_stats';

            // Log della query per debug
            dv_debug_log("Esecuzione query per analisi recenti utente non-WP {$user_id} sulla tabella {$table_name}");

            // Verifica se la tabella esiste
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
            if (!$table_exists) {
                dv_debug_log("ERRORE: La tabella {$table_name} non esiste!");
                return array();
            }

            $analyses = $wpdb->get_results($wpdb->prepare(
                "SELECT ID as id, Title_analysis as title, Created_at as date, Tokens_used as tokens
                FROM {$table_name}
                WHERE User_id = %d
                ORDER BY Created_at DESC
                LIMIT %d",
                $user_id, $limit
            ), ARRAY_A);

            // Log del risultato della query
            dv_debug_log("Risultato query: " . ($analyses ? count($analyses) . " analisi trovate" : "Nessuna analisi trovata") .
                         ". Ultimo errore SQL: " . $wpdb->last_error);
        }

        return $analyses ? $analyses : array();
    }

    /**
     * Endpoint AJAX per recuperare le analisi recenti
     */
    public function get_recent_analyses_ajax() {
        global $wpdb; // Aggiungi la dichiarazione globale di $wpdb

        // Verifica il nonce per sicurezza, ma continua anche in caso di fallimento
        // per supportare utenti non WordPress
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_verified = wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }

        // Log della verifica nonce
        if (!$nonce_verified) {
            dv_debug_log('Nonce non verificato nella richiesta get_recent_analyses_ajax, ma continuiamo per supportare utenti non WordPress');
        }

        $user_id = get_current_user_id();

        // Verifica se l'utente è un sottoscrittore esterno
        $is_subscriber = false;
        if (!$user_id && class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();

            if ($is_subscriber) {
                // Ottieni i dati del sottoscrittore
                $subscriber_data = fa_access_control()->get_current_subscriber_data();

                // Usa l'ID del sottoscrittore o un valore speciale
                $external_user_id = isset($subscriber_data['id']) ? intval($subscriber_data['id']) : 999999;

                // Assicurati che l'ID sia sempre positivo e non zero
                $user_id = max(1, $external_user_id);

                dv_debug_log('Utente sottoscrittore esterno rilevato in get_recent_analyses_ajax, ID assegnato: ' . $user_id);
            }
        }

        // Cerca l'ID utente nei cookie come fallback
        if (!$user_id && isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $user_id = intval($subscriber_data['id']);
                    $is_subscriber = true;
                    dv_debug_log('ID utente recuperato dal cookie: ' . $user_id);
                }
            } catch (Exception $e) {
                dv_debug_log('Errore nella decodifica del cookie: ' . $e->getMessage());
            }
        }

        if (!$user_id) {
            wp_send_json_error(array('message' => 'Utente non riconosciuto.'));
            return;
        }

        // Log per debug
        dv_debug_log("Richiesta analisi recenti per utente ID: {$user_id}");

        // Recupera le analisi recenti
        $recent_analyses = $this->get_recent_analyses($user_id, 5); // Ultime 5 analisi

        // Log per debug - numero di analisi trovate
        dv_debug_log("Trovate " . count($recent_analyses) . " analisi recenti per utente ID: {$user_id}");

        if (empty($recent_analyses)) {
            // Anche se non ci sono analisi, inviamo una risposta di successo con un array vuoto
            wp_send_json_success(array('recent_analyses' => array(), 'message' => 'Nessuna analisi recente trovata.'));
            return;
        }

        // Formatta le date per una visualizzazione migliore
        foreach ($recent_analyses as &$analysis) {
            if (isset($analysis['date'])) {
                $analysis['date'] = self::format_date($analysis['date']);
            }
        }

        // Verifica se ci sono errori nell'ultimo comando SQL
        if ($wpdb->last_error) {
            dv_debug_log('Errore SQL durante il recupero delle analisi recenti: ' . $wpdb->last_error);
            wp_send_json_error(array(
                'message' => 'Errore nel database durante il recupero delle analisi recenti',
                'sql_error' => $wpdb->last_error,
                'is_subscriber' => $is_subscriber,
                'user_id' => $user_id
            ));
            return;
        }

        wp_send_json_success(array(
            'recent_analyses' => $recent_analyses,
            'count' => count($recent_analyses),
            'is_subscriber' => $is_subscriber,
            'user_id' => $user_id
        ));
    }    /**
     * Pulizia delle analisi recenti al logout e azzeramento dei valori di spesa
     */
    public function clean_user_recent_analyses_on_logout() {
        $user_id = get_current_user_id();

        // Verifica se l'utente è un sottoscrittore esterno
        $is_subscriber = false;
        if (!$user_id && class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();

            if ($is_subscriber) {
                // Ottieni i dati del sottoscrittore
                $subscriber_data = fa_access_control()->get_current_subscriber_data();

                // Usa l'ID del sottoscrittore o un valore speciale
                $external_user_id = isset($subscriber_data['id']) ? intval($subscriber_data['id']) : 999999;

                // Assicurati che l'ID sia sempre positivo e non zero
                $user_id = max(1, $external_user_id);

                dv_debug_log('Utente sottoscrittore esterno rilevato in clean_user_recent_analyses_on_logout, ID assegnato: ' . $user_id);
            }
        }

        if (!$user_id) {
            return;
        }

        global $wpdb;

        // Registra l'azione nel log
        dv_debug_log("Esecuzione pulizia dati al logout per utente ID: {$user_id}");

        // Elimina solo le analisi recenti dell'utente
        $result = $wpdb->delete(
            $this->recent_analyses_table,
            array('user_id' => $user_id),
            array('%d')
        );

        // Azzera i valori di spesa effettiva e spesa totale nella tabella delle statistiche
        // ma mantiene il valore dei crediti disponibili
        $update_result = $wpdb->update(
            $this->stats_table,
            array(
                'actual_cost' => 0.00, // Azzera spesa effettiva
                'tot_cost' => 0.00,    // Azzera spesa totale
                // Usa data client-side
                'last_update' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
            ),
            array('user_id' => $user_id),
            array('%f', '%f', '%s'),
            array('%d')
        );

        dv_debug_log("Pulizia analisi recenti completata per utente ID: {$user_id}. Record eliminati: " . ($result !== false ? $result : 'errore'));
        dv_debug_log("Azzeramento valori di spesa completato per utente ID: {$user_id}. Crediti preservati. Risultato: " . ($update_result !== false ? 'successo' : 'errore'));
    }

    /**
     * Pulizia delle analisi recenti e azzeramento spese tramite AJAX
     * Questo metodo viene chiamato esplicitamente prima del logout
     */    public function clean_user_data_before_logout() {
        // Verifica il nonce per sicurezza
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Determina il tipo di utente e ottieni l'ID utente corretto
        $user_id = get_current_user_id();
        $is_wordpress_user = ($user_id > 0);
        $is_subscriber = false;
        $user_type = 'wordpress';
        $redirect_url = site_url('/');

        // Verifica se l'utente è un sottoscrittore esterno
        if (!$is_wordpress_user && class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();

            if ($is_subscriber) {
                // Ottieni i dati del sottoscrittore
                $subscriber_data = fa_access_control()->get_current_subscriber_data();
                $user_type = 'external';

                // Usa l'ID del sottoscrittore o un valore speciale
                $external_user_id = isset($subscriber_data['id']) ? intval($subscriber_data['id']) : 999999;

                // Assicurati che l'ID sia sempre positivo e non zero
                $user_id = max(1, $external_user_id);

                // Imposta l'URL di reindirizzamento specifico per gli utenti esterni
                $redirect_url = isset($subscriber_data['redirect_url']) ? $subscriber_data['redirect_url'] : site_url('/');

                dv_debug_log('Utente sottoscrittore esterno rilevato in clean_user_data_before_logout, ID assegnato: ' . $user_id . ', URL di reindirizzamento: ' . $redirect_url);
            }
        }

        if (!$user_id) {
            wp_send_json_error([
                'message' => 'Utente non riconosciuto',
                'redirect' => site_url('/')
            ]);
            return;
        }

        global $wpdb;

        // Registra l'azione nel log
        dv_debug_log("Esecuzione pulizia dati tramite AJAX prima del logout per utente ID: {$user_id} (tipo: {$user_type})");

        // Elimina le analisi recenti dell'utente dalla tabella document_recent_analyses
        $result = $wpdb->delete(
            $this->recent_analyses_table,
            array('user_id' => $user_id),
            array('%d')
        );

        // Per entrambi i tipi di utenti, azzeriamo solo i costi ma manteniamo il conteggio di analisi, token e crediti
        $update_data = array(
            'actual_cost' => 0.00,       // Azzera spesa effettiva
            'tot_cost' => 0.00,          // Azzera spesa totale
            // Usa data client-side
            'last_update' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
        );

        // Aggiorna la tabella appropriata in base al tipo di utente
        if ($user_type === 'wordpress') {
            // Per utenti WordPress, aggiorna la tabella document_user_stats
            $update_result = $wpdb->update(
                $this->stats_table,
                $update_data,
                array('user_id' => $user_id),
                array('%f', '%f', '%s'),
                array('%d')
            );

            dv_debug_log("Azzeramento valori di spesa per utente WordPress ID: {$user_id} nella tabella {$this->stats_table}. Risultato: " . ($update_result !== false ? 'successo' : 'errore'));
        } else {
            // Per utenti esterni, aggiorna la tabella wpcd_user_subscription
            $users_table = 'wpcd_user_subscription';

            // Get current client time via JavaScript
            $client_time_js = "<script>
                var now = new Date();
                var timezoneOffset = now.getTimezoneOffset();
                document.cookie = 'client_timezone_offset=' + timezoneOffset + ';path=/';
            <\/script>";

            // Get timezone offset from cookie or default to server time
            $timezone_offset = isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) : 0;
            $client_time = date('Y-m-d H:i:s', time() - ($timezone_offset * 60));

            $update_result = $wpdb->update(
                $users_table,
                array(
                    'actual_cost' => 0.00,
                    'tot_cost' => 0.00,
                    'updated_at' => $client_time
                ),
                array('id' => $user_id),
                array('%f', '%f', '%s'),
                array('%d')
            );

            // Output JavaScript to set the cookie
            echo $client_time_js;

            dv_debug_log("Azzeramento valori di spesa per utente esterno ID: {$user_id} nella tabella {$users_table}. Risultato: " . ($update_result !== false ? 'successo' : 'errore'));
        }

        dv_debug_log("Pulizia AJAX analisi recenti completata per utente ID: {$user_id}. Record eliminati: " . ($result !== false ? $result : 'errore'));

        // Registra l'operazione di pulizia dati nel log di debug con dettagli specifici per il tipo di utente
        if ($user_type === 'wordpress') {
            dv_debug_log("Reset costi completato per utente WordPress (ID: {$user_id}). Mantenuti analisi, token e crediti.");
        } else {
            dv_debug_log("Reset costi completato per utente esterno (ID: {$user_id}). Mantenuti analisi, token e crediti.");
        }

        // Se l'utente è un sottoscrittore esterno, eliminiamo anche i cookie di autenticazione
        if ($is_subscriber && function_exists('fa_access_control')) {
            fa_access_control()->clear_subscriber_cookies();
            dv_debug_log("Cookie di autenticazione eliminati per l'utente esterno ID: {$user_id}");
        }

        // Inviamo l'URL di reindirizzamento nella risposta
        wp_send_json_success([
            'message' => 'Dati utente puliti con successo',
            'redirect' => $redirect_url
        ]);
    }    /**
     * Resetta le statistiche al caricamento del widget
     * Nota: La spesa stimata viene gestita solo lato client e non dovrebbe essere ricalcolata qui
     */
    public function reset_stats_on_widget_load() {
        // Verifica il nonce per sicurezza
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(array('message' => 'Verifica di sicurezza fallita.'));
            return;
        }

        // Utilizza il sistema unificato di identificazione utente
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];
        $user_type = $user_info['type'];

        if (!$user_id) {
            wp_send_json_error(array('message' => 'Utente non riconosciuto.'));
            return;
        }

        global $wpdb;
        $result = false;

        try {
            dv_debug_log("Reset statistiche al caricamento del widget per utente: ID={$user_id}, Tipo={$user_type}");

            if ($user_type === 'subscriber') {
                // Aggiorna le statistiche dell'utente esterno
                $users_table = 'wpcd_user_subscription';
                // Verifica che la tabella esista prima di eseguire l'update
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$users_table}'") === $users_table;

                if ($table_exists) {
                    // Preserva la spesa totale come dato statistico - azzera solo la spesa effettiva della sessione
                    $result = $wpdb->update(
                        $users_table,
                        array(
                            'actual_cost' => 0.00,     // Azzera solo spesa effettiva della sessione corrente
                            // NON azzerare tot_cost - è un dato statistico cumulativo
                            // Usa data client-side
                            'updated_at' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
                        ),
                        array('id' => $user_id),
                        array('%f', '%s'),  // Rimuovi %f per tot_cost
                        array('%d')
                    );

                    if ($result === false) {
                        dv_debug_log("Errore SQL durante l'update per utente esterno: " . $wpdb->last_error);
                    } else {
                        dv_debug_log("SQL Update eseguito con successo: {$result} righe aggiornate");
                    }
                } else {
                    dv_debug_log("Tabella {$users_table} non trovata");
                    // Consideriamo questa situazione come una riuscita comunque
                    $result = true;
                }                    // Non eliminiamo il cookie della spesa stimata per evitare di perdere il valore
                    // La spesa stimata viene gestita solo dal client quando carica un documento
                    // NON azzerare fa_estimated_cost qui
            } else {
                // Utente WordPress standard
                // Verifica che la tabella esista
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->stats_table}'") === $this->stats_table;

                if ($table_exists) {
                    // Verifica se esiste già un record per l'utente
                    $exists = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$this->stats_table} WHERE user_id = %d",
                        $user_id
                    ));

                    if (!$exists) {
                        // Inizializza le statistiche se non esistono
                        $this->initialize_user_stats($user_id);
                        dv_debug_log("Inizializzate nuove statistiche per utente WordPress ID: {$user_id}");
                    }

                    // Preserva la spesa totale come dato statistico - azzera solo la spesa effettiva della sessione
                    $result = $wpdb->update(
                        $this->stats_table,
                        array(
                            'actual_cost' => 0.00,     // Azzera solo spesa effettiva della sessione corrente
                            // NON azzerare tot_cost - è un dato statistico cumulativo
                            // Usa data client-side
                            'last_update' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
                        ),
                        array('user_id' => $user_id),
                        array('%f', '%s'),  // Rimuovi %f per tot_cost
                        array('%d')
                    );

                    if ($result === false) {
                        dv_debug_log("Errore SQL durante l'update per utente WordPress: " . $wpdb->last_error);
                    } else {
                        dv_debug_log("SQL Update eseguito con successo: {$result} righe aggiornate");
                    }
                } else {
                    // Crea la tabella se non esiste
                    $this->create_stats_tables();
                    // Inizializza le statistiche per l'utente
                    $this->initialize_user_stats($user_id);
                    dv_debug_log("Tabella statistiche creata e inizializzata per utente WordPress ID: {$user_id}");
                    $result = true;
                }

                // Non eliminiamo il cookie della spesa stimata qui
                // La spesa stimata viene gestita solo dal client e deve essere mantenuta tra le sessioni
            }

            // Se non ci sono stati errori nell'SQL (anche se non è stata modificata nessuna riga)
            // consideriamo l'operazione riuscita
            dv_debug_log("Spesa effettiva resettata con successo per utente ID: {$user_id} (spesa totale preservata)");
            wp_send_json_success(array(
                'message' => 'Spesa effettiva resettata con successo (spesa totale preservata come dato statistico)',
                'actual_cost' => '0,00'
                // Non includiamo tot_cost perché non viene più azzerata
            ));
        } catch (Exception $e) {
            dv_debug_log("Eccezione durante il reset delle statistiche: " . $e->getMessage());
            wp_send_json_error(array('message' => 'Errore durante il reset delle statistiche: ' . $e->getMessage()));
        }
    }

    /**
     * Ripristino forzato delle statistiche in caso di problemi persistenti
     * Questo metodo è pensato come ultima risorsa quando le statistiche mostrano errori
     */
    public function force_reset_stats() {
        // Verifica il nonce per sicurezza (usiamo lo stesso nonce di document_viewer)
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(array('message' => 'Verifica di sicurezza fallita.'));
            return;
        }

        dv_debug_log("Tentativo di ripristino forzato delle statistiche");

        try {
            // Determina l'ID utente
            $user_info = $this->get_current_user_info();
            $user_id = $user_info['id'];
            $user_type = $user_info['type'];

            if (!$user_id) {
                wp_send_json_error(array('message' => 'Utente non identificato.'));
                return;
            }

            global $wpdb;

            // Log dell'operazione
            dv_debug_log("Ripristino forzato per utente: ID={$user_id}, Tipo={$user_type}");

            // Per utenti WordPress
            if ($user_type === 'wordpress') {
                // Elimina completamente i dati esistenti
                $wpdb->delete(
                    $this->stats_table,
                    array('user_id' => $user_id),
                    array('%d')
                );

                // Elimina le analisi recenti
                $wpdb->delete(
                    $this->recent_analyses_table,
                    array('user_id' => $user_id),
                    array('%d')
                );

                // Ricrea i dati iniziali
                $this->initialize_user_stats($user_id);

                dv_debug_log("Dati statistiche ripristinati completamente per utente WordPress ID: {$user_id}");
            }
            // Per utenti esterni
            else if ($user_type === 'subscriber') {
                $users_table = 'wpcd_user_subscription';

                // Verifica che la tabella esista
                if ($wpdb->get_var("SHOW TABLES LIKE '{$users_table}'") === $users_table) {
                    // Aggiorna i valori a zero ma mantieni i crediti
                    $user_data = $wpdb->get_row($wpdb->prepare(
                        "SELECT credit FROM {$users_table} WHERE id = %d",
                        $user_id
                    ));
                    $credit = $user_data ? $user_data->credit : 10.00;
                    $wpdb->update(
                        $users_table,
                        array(
                            'actual_cost' => 0.00,
                            'tot_cost' => 0.00,
                            // Usa data client-side
                            'updated_at' => date('Y-m-d H:i:s', time() - (isset($_COOKIE['client_timezone_offset']) ? intval($_COOKIE['client_timezone_offset']) * 60 : 0))
                        ),
                        array('id' => $user_id),
                        array('%f', '%f', '%s'),
                        array('%d')
                    );

                    // Elimina le analisi recenti
                    $wpdb->delete(
                        $this->recent_analyses_table,
                        array('user_id' => $user_id),
                        array('%d')
                    );

                    dv_debug_log("Dati statistiche ripristinati per utente esterno ID: {$user_id}, credito preservato: {$credit}");
                }
            }

            // Pulisci i cookie correlati
            if (isset($_COOKIE['fa_estimated_cost'])) {
                setcookie('fa_estimated_cost', '', time() - 3600, '/');
            }
            if (isset($_COOKIE['fa_actual_cost'])) {
                setcookie('fa_actual_cost', '', time() - 3600, '/');
            }

            // Invia risposta di successo
            wp_send_json_success(array(
                'message' => 'Statistiche ripristinate correttamente',
                'user_id' => $user_id,
                'user_type' => $user_type
            ));
        }
        catch (Exception $e) {
            dv_debug_log("Errore durante il ripristino forzato: " . $e->getMessage());
            wp_send_json_error(array('message' => 'Errore durante il ripristino: ' . $e->getMessage()));
        }
    }

    /**
     * Endpoint AJAX per sincronizzare il credito dal database
     */
    public function get_current_credit() {
        // Verifica il nonce per sicurezza
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'subscriber_management_nonce')) {
            wp_send_json_error(array('message' => 'Verifica di sicurezza fallita.'));
            return;
        }

        try {
            // Determina l'ID utente
            $user_info = $this->get_current_user_info();
            $user_id = $user_info['id'];
            $user_type = $user_info['type'];

            if (!$user_id) {
                wp_send_json_error(array('message' => 'Utente non identificato.'));
                return;
            }

            global $wpdb;
            $credit = 0.00;

            // Per utenti WordPress
            if ($user_type === 'wordpress') {
                $stats = $this->get_user_stats($user_id);
                if ($stats) {
                    $credit = (float)$stats->credits_available;
                }
            }
            // Per utenti esterni (subscriber)
            else if ($user_type === 'subscriber') {
                $users_table = 'wpcd_user_subscription';
                $user_data = $wpdb->get_row($wpdb->prepare(
                    "SELECT credit FROM {$users_table} WHERE id = %d",
                    $user_id
                ));

                if ($user_data) {
                    $credit = (float)$user_data->credit;
                }
            }

            dv_debug_log("Credito sincronizzato dal database per utente ID: {$user_id}, Tipo: {$user_type}, Credito: {$credit}");

            wp_send_json_success(array(
                'credit' => $credit,
                'user_id' => $user_id,
                'user_type' => $user_type
            ));

        } catch (Exception $e) {
            dv_debug_log("Errore durante la sincronizzazione del credito: " . $e->getMessage());
            wp_send_json_error(array('message' => 'Errore durante la sincronizzazione del credito: ' . $e->getMessage()));
        }
    }

    /**
     * Helper method per ottenere informazioni sull'utente corrente (WP o esterno)
     * Questo metodo è un fallback nel caso fa_get_current_user_info() non sia disponibile
     */
    private function get_current_user_info() {
        // Prima, controlla se la funzione globale è disponibile
        if (function_exists('fa_get_current_user_info')) {
            return fa_get_current_user_info();
        }

        // Fallback: implementa la logica localmente
        $user_id = get_current_user_id();
        $is_subscriber = false;
        $external_user_id = 0;

        // Se è un utente WordPress
        if ($user_id > 0) {
            return [
                'id' => $user_id,
                'type' => 'wordpress',
                'is_logged_in' => true
            ];
        }

        // Verifica se è un sottoscrittore esterno
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();

            if ($is_subscriber) {
                // Ottieni i dati del sottoscrittore
                $subscriber_data = fa_access_control()->get_current_subscriber_data();

                if (is_array($subscriber_data) && isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                }
            }
        }

        // Fallback: verifica anche nel cookie (in caso FA_Access_Control non sia disponibile)
        if ($external_user_id === 0 && isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);

                if (is_array($subscriber_data) && isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                    $is_subscriber = true;
                    dv_debug_log('ID utente recuperato dal cookie in get_current_user_info: ' . $external_user_id);
                }
            } catch (Exception $e) {
                dv_debug_log('Errore nella decodifica del cookie in get_current_user_info: ' . $e->getMessage());
            }
        }

        // Se è stato trovato un ID sottoscrittore esterno
        if ($external_user_id > 0) {
            return [
                'id' => $external_user_id,
                'type' => 'subscriber',
                'is_logged_in' => true
            ];
        }

        // Utente non identificato
        return [
            'id' => 0,
            'type' => 'guest',
            'is_logged_in' => false
        ];
    }

    /**
     * Formatta una data in un formato leggibile
     *
     * @param string $date_string Data in formato MySQL
     * @return string Data formattata
     */
    public static function format_date($date_string, $for_js = false) {
        $date = new DateTime($date_string);
        if ($for_js) {
            // Return ISO 8601 format for JavaScript Date parsing
            return $date->format('c');
        }
        return $date->format('d/m/Y H:i');
    }

    /**
     * Restituisce l'HTML del widget delle statistiche
     *
     * @return string HTML del widget delle statistiche
     */
    public static function get_stats_widget_html() {
        $user_id = get_current_user_id();
        $is_subscriber = false;

        // Verifica se l'utente è un sottoscrittore non WordPress
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $is_subscriber = fa_access_control()->is_current_user_subscriber();
        }

        if (!$user_id && !$is_subscriber) {
            // Utente non loggato e non è un sottoscrittore, mostra messaggio di login
            return '<div class="stats-widget-login-message">
                <p>Effettua il login per visualizzare le tue statistiche di utilizzo.</p>
            </div>';
        }

        // Init classe per accesso ai metodi
        $stats = new self();

        // Verifica se è un utente WordPress o un sottoscrittore esterno
        if ($user_id) {
            // Utente WordPress standard
            $user_data = get_userdata($user_id);
            $user_roles = $user_data->roles;
            $user_role = !empty($user_roles) ? ucfirst($user_roles[0]) : 'Abbonato';
            $user_name = $user_data->display_name;
            $avatar = get_avatar($user_id, 40);

            // Ottieni statistiche
            $stats_data = $stats->get_user_stats($user_id);

            if (empty($stats_data)) {
                $stats->initialize_user_stats($user_id);
                $stats_data = $stats->get_user_stats($user_id);
            }

            $recent_analyses = $stats->get_recent_analyses($user_id, 5);
        } else {
            // Utente sottoscrittore non WordPress
            $subscriber_data = fa_access_control()->get_current_subscriber_data();

            if (!$subscriber_data) {
                // Fallback nel caso in cui il cookie esista ma i dati non siano validi
                return '<div class="stats-widget-login-message">
                    <p>Effettua il login per visualizzare le tue statistiche di utilizzo.</p>
                </div>';
            }

            $user_name = isset($subscriber_data['name']) ? $subscriber_data['name'] : 'Sottoscrittore';
            $user_role = 'Sottoscrittore Esterno';

            // Usa un avatar generico per i sottoscrittori
            $avatar = '<div class="subscriber-avatar"><span class="dashicons dashicons-admin-users"></span></div>';

            // Recupera le statistiche reali dell'utente esterno dal database
            if (isset($subscriber_data['id'])) {
                $external_user_id = intval($subscriber_data['id']);
                $stats_data = $stats->get_external_user_stats($external_user_id);

                // Recupera le analisi recenti dell'utente esterno solo se esistono statistiche
                if (!empty($stats_data)) {
                    $recent_analyses = $stats->get_recent_analyses($external_user_id, 5);
                } else {
                    // Se non esistono statistiche, non mostrare nulla
                    return '<div class="stats-widget-login-message">
                        <p>Nessun dato statistico disponibile per questo account.</p>
                    </div>';
                }
            } else {
                // Se l'ID non è disponibile, non mostrare nulla
                return '<div class="stats-widget-login-message">
                    <p>Impossibile identificare l\'utente. Effettua nuovamente il login.</p>
                </div>';
            }
        }


        $analysis_count = $stats_data->analysis_count;
        $tokens_used = $stats_data->tokens_used;

        // Format all monetary values with exactly 2 decimal places
        $credits_available = (float)$stats_data->credits_available;
        $actual_cost = (float)$stats_data->actual_cost;
        $tot_cost = (float)$stats_data->tot_cost;

        // Format with exactly 2 decimal places and comma as decimal separator
        $credits_available = number_format($credits_available, 2, ',', '.');
        $actual_cost = number_format($actual_cost, 2, ',', '.');
        $tot_cost = number_format($tot_cost, 2, ',', '.');

        // Format token count with thousands separator
        $formatted_tokens = number_format($tokens_used, 0, ',', '.');

        // Genera HTML del widget
        $html = '<div class="stats-widget" id="document-stats-widget">            <div class="stats-widget-header">
                <div class="stats-user-info">
                    <div class="stats-avatar">' . $avatar . '</div>
                    <div class="stats-user-details">
                        <div class="stats-user-name">' . esc_html($user_name) . '</div>
                        <div class="stats-user-role">' . esc_html($user_role) . '</div>
                    </div>
                    <div class="stats-logout-button">';

        // Diverso link di logout a seconda del tipo di utente
        if ($user_id) {
            // Utente WordPress
            $logout_url = add_query_arg('loggedout', 'true', wp_logout_url(get_permalink()));
            $html .= '<a href="' . esc_url($logout_url) . '" class="stats-logout-link" onclick="return cleanUserDataBeforeLogout(event)">';
            $html .= '<i class="fas fa-sign-out-alt"></i> ' . __('Logout', 'document-viewer-plugin') . '';
            $html .= '</a>';

            // Aggiungi script per gestire la pulizia dei cookie
            $html .= "
            <script>
            function cleanUserDataBeforeLogout(event) {
                // Rimuovi i cookie di sessione
                document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
                document.cookie = 'fa_actual_cost=; path=/; max-age=0';
                return true; // Consenti la navigazione
            }
            </script>";
        } else {
            // Utente sottoscrittore esterno - usa un link personalizzato per il logout
            $logout_url = add_query_arg('loggedout', 'true', home_url('/'));
            $html .= '<a href="' . esc_url($logout_url) . '" class="stats-logout-link" onclick="return subscriberLogout(event)">';
            $html .= '<i class="fas fa-sign-out-alt"></i> ' . __('Logout', 'document-viewer-plugin') . '';
            $html .= '</a>';

            // Aggiungi script per gestire il logout personalizzato
            $html .= "
            <script>
            function subscriberLogout(event) {
                event.preventDefault();
                // Rimuovi i cookie di sessione
                document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
                document.cookie = 'fa_actual_cost=; path=/; max-age=0';

                // Reindirizza alla home con il parametro loggedout
                window.location.href = '" . esc_js($logout_url) . "';
                return false;
            }
            </script>";
        }

        $html .= '</div>
                </div>
            </div>

            <div class="stats-widget-content">
                <div class="stats-counters">
                    <div class="stat-item">
                        <div class="stat-value">' . esc_html($analysis_count) . '</div>
                        <div class="stat-label">Analisi</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">' . esc_html($formatted_tokens) . '</div>
                        <div class="stat-label">Token</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value credit-value">€ ' . esc_html($credits_available) . '</div>
                        <div class="stat-label">Credito</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value cost-value">€ ' . esc_html($actual_cost) . '</div>
                        <div class="stat-label">Spesa effettiva</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value total-cost-value">€ ' . esc_html($tot_cost) . '</div>
                        <div class="stat-label">Spesa totale</div>
                    </div>
                </div>

                <div class="stats-recent-analyses">
                    <h4>Ultime analisi</h4>';

        if (!empty($recent_analyses)) {
            $html .= '<ul class="recent-analyses-list">';
            foreach ($recent_analyses as $analysis) {
                $html .= '<li class="recent-analysis-item">
                    <div class="analysis-item-title">' . esc_html($analysis['title']) . '</div>
                    <div class="analysis-item-meta">
                        <span class="analysis-date" data-timestamp="' . esc_attr(self::format_date($analysis['date'], true)) . '">' . esc_html(self::format_date($analysis['date'])) . '</span>
                        <span class="analysis-tokens">' . esc_html(number_format($analysis['tokens'], 0, ',', '.')) . ' token</span>
                    </div>
                </li>';
            }
            $html .= '</ul>';
        } else {
            $html .= '<p class="no-analyses">Nessuna analisi effettuata.</p>';
        }

        $html .= '</div>
            </div>
            <div class="stats-widget-footer">
                <!-- Removed refresh stats button as requested -->
            </div>';

        // Aggiungi il riferimento al file JavaScript esterno
        $html .= '<script src="' . esc_url(plugins_url('assets/js/document-stats.js', dirname(__FILE__))) . '"></script>';
        $html .= '<script>
            // Funzione per formattare la data locale
            function formatLocalDate(dateString) {
                const options = {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                    hour: "2-digit",
                    minute: "2-digit"
                };
                return new Date(dateString).toLocaleString("it-IT", options);
            }

            // Aggiorna tutte le date  locale
            document.addEventListener("DOMContentLoaded", function() {
                const dateElements = document.querySelectorAll(".analysis-date[data-timestamp]");
                dateElements.forEach(function(element) {
                    const dateString = element.getAttribute("data-timestamp");
                    if (dateString) {
                        element.textContent = formatLocalDate(dateString);
                    }
                });
            });

            // Funzione di compatibilità per il logout
            function cleanUserDataBeforeLogout(event) {
                if (window.cleanUserDataBeforeLogout) {
                    return window.cleanUserDataBeforeLogout(event);
                }
                // Fallback in caso il file JS non sia caricato
                event.preventDefault();
                window.location.href = "' . esc_js(wp_logout_url(get_permalink())) . '";
            }

            // Funzione di compatibilità per il logout degli utenti esterni
            function subscriberLogout(event) {
                if (window.subscriberLogout) {
                    return window.subscriberLogout(event);
                }
                // Fallback in caso il file JS non sia caricato
                event.preventDefault();
                window.location.href = "' . esc_js(wp_logout_url(get_permalink())) . '";
            }
        </script>';

        return $html;
    }
}

/**
 * Returns the singleton instance of the Document_Stats class
 *
 * @return Document_Stats The singleton instance
 */
function dv_document_stats() {
    return Document_Stats::get_instance();
}
