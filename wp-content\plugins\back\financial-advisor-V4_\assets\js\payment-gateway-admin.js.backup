(function($) {
    'use strict';

    $(document).ready(function() {
        initPaymentGatewayAdmin();
    });

    function initPaymentGatewayAdmin() {
        // Tab navigation
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            switchTab($(this).attr('href').substring(1));
        });

        // PayPal form submission
        $('#paypal-config-form').on('submit', function(e) {
            e.preventDefault();
            savePayPalConfig();
        });

        // Stripe form submission
        $('#stripe-config-form').on('submit', function(e) {
            e.preventDefault();
            saveStripeConfig();
        });

        // Test connections
        $('#test-paypal-config').on('click', function() {
            testPayPalConfig();
        });

        $('#test-stripe-config').on('click', function() {
            testStripeConfig();
        });

        // Password field visibility toggle
        $('input[type="password"]').each(function() {
            addPasswordToggle($(this));
        });

        // Testing & Debug tab event handlers
        $('#test-all-gateways').on('click', function() {
            testAllGateways();
        });
        
        $('#test-webhook-endpoints').on('click', function() {
            testWebhookEndpoints();
        });
        
        $('#simulate-timeout').on('click', function() {
            simulateError('timeout');
        });
        
        $('#simulate-api-error').on('click', function() {
            simulateError('api_error');
        });
        
        $('#simulate-network-error').on('click', function() {
            simulateError('network_error');
        });
        
        $('#run-performance-test').on('click', function() {
            runPerformanceTest();
        });
        
        $('#stress-test-gateways').on('click', function() {
            stressTestGateways();
        });
        
        $('#open-debug-console').on('click', function() {
            openDebugConsole();
        });
        
        $('#export-debug-report').on('click', function() {
            exportDebugReport();
        });
        
        // System Checklist tab event handlers
        $('#run-full-checklist').on('click', function() {
            runFullChecklist();
        });
        
        $('#run-quick-check').on('click', function() {
            runQuickCheck();
        });
        
        $('#export-checklist-report').on('click', function() {
            exportChecklistReport();
        });
        
        // Error Monitoring tab event handlers
        $('#refresh-monitoring').on('click', function() {
            refreshMonitoringStats();
        });
        
        $('#clear-error-logs').on('click', function() {
            clearErrorLogs();
        });
        
        $('#enable-auto-fix').on('click', function() {
            toggleAutoFix();
        });
        
        $('#start-monitoring').on('click', function() {
            startErrorMonitoring();
        });
        
        // Debug panel handlers
        $('#close-debug-panel').on('click', function() {
            closeDebugPanel();
        });
        
        // Checklist category toggle
        $(document).on('click', '.category-header', function() {
            const $items = $(this).next('.category-items');
            $items.toggleClass('expanded');
        });
        
        // Auto-refresh monitoring stats every 30 seconds
        setInterval(function() {
            if ($('#monitoring-tab').hasClass('active')) {
                refreshMonitoringStats(true); // Silent refresh
            }
        }, 30000);
    }

    function switchTab(tabId) {
        // Update nav tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $('a[href="#' + tabId + '"]').addClass('nav-tab-active');

        // Update content
        $('.tab-content').removeClass('active');
        $('#' + tabId).addClass('active');
    }

    function savePayPalConfig() {
        const $form = $('#paypal-config-form');
        const $submitBtn = $('#save-paypal-config');
        
        // Collect form data
        const formData = {
            action: 'save_paypal_config',
            nonce: paymentGatewayAjax.nonce,
            client_id: $('#paypal_client_id').val(),
            client_secret: $('#paypal_client_secret').val(),
            environment: $('#paypal_environment').val(),
            webhook_id: $('#paypal_webhook_id').val(),
            is_active: $('#paypal_is_active').is(':checked') ? 1 : 0
        };

        // Validate required fields
        if (!formData.client_id || !formData.client_secret) {
            showFeedbackMessage('Please fill in all required fields.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.save_error, 'error');
            },
            complete: function() {
                $submitBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function saveStripeConfig() {
        const $form = $('#stripe-config-form');
        const $submitBtn = $('#save-stripe-config');
        
        // Collect form data
        const formData = {
            action: 'save_stripe_config',
            nonce: paymentGatewayAjax.nonce,
            public_key: $('#stripe_public_key').val(),
            secret_key: $('#stripe_secret_key').val(),
            environment: $('#stripe_environment').val(),
            webhook_secret: $('#stripe_webhook_secret').val(),
            is_active: $('#stripe_is_active').is(':checked') ? 1 : 0
        };

        // Validate required fields
        if (!formData.public_key || !formData.secret_key) {
            showFeedbackMessage('Please fill in all required fields.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.save_error, 'error');
            },
            complete: function() {
                $submitBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function testPayPalConfig() {
        const $testBtn = $('#test-paypal-config');
        
        // Collect form data for testing
        const testData = {
            action: 'test_paypal_config',
            nonce: paymentGatewayAjax.nonce,
            client_id: $('#paypal_client_id').val(),
            client_secret: $('#paypal_client_secret').val(),
            environment: $('#paypal_environment').val()
        };

        // Validate required fields
        if (!testData.client_id || !testData.client_secret) {
            showFeedbackMessage('Please fill in Client ID and Client Secret before testing.', 'warning');
            return;
        }

        // Show loading state
        $testBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: testData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.test_error, 'error');
            },
            complete: function() {
                $testBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function testStripeConfig() {
        const $testBtn = $('#test-stripe-config');
        
        // Collect form data for testing
        const testData = {
            action: 'test_stripe_config',
            nonce: paymentGatewayAjax.nonce,
            secret_key: $('#stripe_secret_key').val()
        };

        // Validate required fields
        if (!testData.secret_key) {
            showFeedbackMessage('Please fill in Secret Key before testing.', 'warning');
            return;
        }

        // Show loading state
        $testBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: testData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.test_error, 'error');
            },
            complete: function() {
                $testBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function addPasswordToggle(passwordField) {
        const $field = passwordField;
        const $wrapper = $('<div class="password-field-wrapper" style="position: relative; display: inline-block; width: 100%;"></div>');
        const $toggleBtn = $('<button type="button" class="password-toggle" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; color: #666;">👁️</button>');
        
        $field.wrap($wrapper);
        $field.after($toggleBtn);
        
        $toggleBtn.on('click', function() {
            const type = $field.attr('type') === 'password' ? 'text' : 'password';
            $field.attr('type', type);
            $toggleBtn.text(type === 'password' ? '👁️' : '🙈');
        });
    }

    function showFeedbackMessage(message, type) {
        const $feedback = $('#gateway-feedback-message');
        
        $feedback
            .removeClass('notice-success notice-error notice-warning')
            .addClass('notice-' + type)
            .text(message)
            .show();

        // Auto hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(function() {
                $feedback.fadeOut();
            }, 5000);
        }

        // Scroll to message
        $('html, body').animate({
            scrollTop: $feedback.offset().top - 50
        }, 500);
    }

    // Environment change handlers
    $('#paypal_environment').on('change', function() {
        const environment = $(this).val();
        const $badge = $('.paypal-environment-badge');
        
        if ($badge.length === 0) {
            $(this).after('<span class="environment-badge paypal-environment-badge"></span>');
        }
        
        $('.paypal-environment-badge')
            .removeClass('sandbox live')
            .addClass(environment)
            .text(environment.toUpperCase());
    });

    $('#stripe_environment').on('change', function() {
        const environment = $(this).val();
        const $badge = $('.stripe-environment-badge');
        
        if ($badge.length === 0) {
            $(this).after('<span class="environment-badge stripe-environment-badge"></span>');
        }
        
        $('.stripe-environment-badge')
            .removeClass('test live')
            .addClass(environment)
            .text(environment.toUpperCase());
    });

    // Trigger environment badges on load
    $('#paypal_environment, #stripe_environment').trigger('change');

    // Testing & Debug Functions
    function testAllGateways() {
        showTestingResults();
        updateTestingOutput('🔄 Running comprehensive gateway tests...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_gateway_connection',
                gateway: 'all',
                test_type: 'comprehensive',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateTestingOutput('✅ Gateway tests completed successfully!\n\n');
                    updateTestingOutput(formatTestResults(response.data.results));
                } else {
                    updateTestingOutput('❌ Gateway tests failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error occurred during testing\n');
            }
        });
    }
      function testWebhookEndpoints() {
        showTestingResults();
        updateTestingOutput('🔄 Testing webhook endpoints...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_webhook_endpoints',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data.results;
                    Object.entries(results).forEach(([gateway, result]) => {
                        const status = result.success ? '✅' : '❌';
                        updateTestingOutput(`${status} ${gateway.toUpperCase()} webhook: ${result.message}\n`);
                    });
                    updateTestingOutput('📊 Webhook endpoint testing completed\n');
                } else {
                    updateTestingOutput('❌ Webhook testing failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during webhook testing\n');
            }
        });
    }
    
    function simulateError(errorType) {
        showTestingResults();
        updateTestingOutput('🧪 Simulating ' + errorType + ' error...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'simulate_payment_error',
                error_type: errorType,
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                updateTestingOutput('✅ Error simulation completed\n');
                updateTestingOutput('📝 Error details: ' + JSON.stringify(response, null, 2) + '\n');
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                updateTestingOutput('🎯 Simulated error triggered successfully!\n');
                updateTestingOutput('📝 Error type: ' + errorType + '\n');
                updateTestingOutput('📝 Error response: ' + JSON.stringify(response.data, null, 2) + '\n');
            }
        });
    }
      function runPerformanceTest() {
        showTestingResults();
        updateTestingOutput('⚡ Running performance tests...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_performance_test',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    updateTestingOutput(`\n📊 Performance Test Results:\n`);
                    updateTestingOutput(`Total execution time: ${results.total_time}ms\n`);
                    updateTestingOutput(`Average response time: ${results.average_time}ms\n`);
                    updateTestingOutput(`Memory usage: ${results.memory_usage}\n`);
                    updateTestingOutput(`Tests completed: ${results.test_count}\n`);
                    
                    if (results.gateway_results) {
                        Object.entries(results.gateway_results).forEach(([gateway, result]) => {
                            updateTestingOutput(`${gateway.toUpperCase()}: ${result.response_time}ms - ${result.status}\n`);
                        });
                    }
                } else {
                    updateTestingOutput('❌ Performance test failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during performance testing\n');
            }
        });
    }
      function stressTestGateways() {
        showTestingResults();
        updateTestingOutput('💪 Running stress test...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'stress_test_gateways',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    updateTestingOutput(`\n🏆 Stress Test Results:\n`);
                    updateTestingOutput(`Total requests: ${results.total_requests}\n`);
                    updateTestingOutput(`Successful requests: ${results.successful}\n`);
                    updateTestingOutput(`Failed requests: ${results.failed}\n`);
                    updateTestingOutput(`Success rate: ${results.success_rate}%\n`);
                    updateTestingOutput(`Average response time: ${results.avg_response_time}ms\n`);
                    updateTestingOutput(`Peak memory usage: ${results.peak_memory}\n`);
                } else {
                    updateTestingOutput('❌ Stress test failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during stress testing\n');
            }
        });
    }
    
    function openDebugConsole() {
        if (typeof window.AdvancedErrorMonitor !== 'undefined') {
            window.AdvancedErrorMonitor.showDebugPanel();
        } else {
            $('#debug-panel').show().addClass('fade-in');
            updateDebugConsole('🔧 Debug Console Initialized\n');
            updateDebugConsole('📊 System Status: Online\n');
            updateDebugConsole('⚙️ Available Commands:\n');
            updateDebugConsole('  - PaymentGatewayChecker.runChecklist()\n');
            updateDebugConsole('  - AdvancedErrorMonitor.showStats()\n');
            updateDebugConsole('  - Use Ctrl+Shift+M for quick access\n\n');
        }
    }
    
    function exportDebugReport() {
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'export_debug_report',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    downloadReport(response.data.report, response.data.filename);
                    showMessage(paymentGatewayAjax.messages.export_success, 'success');
                } else {
                    showMessage('Failed to export debug report: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showMessage('Error occurred while exporting debug report', 'error');
            }
        });
    }
    
    // System Checklist Functions
    function runFullChecklist() {
        $('#checklist-progress').show();
        $('#checklist-results').hide();
        updateChecklistProgress(0, 'Initializing comprehensive checklist...');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_gateway_checklist',
                type: 'full',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                updateChecklistProgress(100, 'Checklist completed!');
                
                setTimeout(function() {
                    $('#checklist-progress').hide();
                    if (response.success) {
                        displayChecklistResults(response.data.results);
                        showMessage(paymentGatewayAjax.messages.checklist_success, 'success');
                    } else {
                        showMessage(paymentGatewayAjax.messages.checklist_error + ': ' + response.data.message, 'error');
                    }
                }, 1000);
            },
            error: function() {
                $('#checklist-progress').hide();
                showMessage('Error occurred while running checklist', 'error');
            }
        });
        
        // Simulate progress updates
        animateProgress();
    }
    
    function runQuickCheck() {
        $('#checklist-progress').show();
        $('#checklist-results').hide();
        updateChecklistProgress(0, 'Running quick health check...');
        
        // Quick check simulation
        animateProgress(3000);
        
        setTimeout(function() {
            updateChecklistProgress(100, 'Quick check completed!');
            setTimeout(function() {
                $('#checklist-progress').hide();
                displayQuickCheckResults();
            }, 1000);
        }, 3000);
    }
    
    function exportChecklistReport() {
        const results = getCurrentChecklistResults();
        if (results) {
            downloadReport(results, 'checklist-report-' + new Date().toISOString().slice(0,19) + '.json');
            showMessage('Checklist report exported successfully!', 'success');
        } else {
            showMessage('No checklist results to export. Please run a checklist first.', 'warning');
        }
    }
    
    // Error Monitoring Functions    function refreshMonitoringStats(silent = false) {
        if (!silent) {
            $('#refresh-monitoring').prop('disabled', true).prepend('<span class="loading-spinner"></span>');
        }
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_system_status',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateMonitoringStats(response.data.error_stats || {});
                    updateErrorLogs(response.data.recent_logs || []);
                    updateSystemStatusDisplay(response.data);
                    if (!silent) {
                        showMessage('Monitoring stats refreshed', 'success');
                    }
                } else {
                    if (!silent) {
                        showMessage('Failed to refresh stats: ' + response.data.message, 'error');
                    }
                }
            },
            error: function() {
                if (!silent) {
                    showMessage('Error occurred while refreshing stats', 'error');
                }
            },
            complete: function() {
                if (!silent) {
                    $('#refresh-monitoring').prop('disabled', false).find('.loading-spinner').remove();
                }
            }
        });
    }
    
    function clearErrorLogs() {
        if (!confirm('Are you sure you want to clear all error logs?')) {
            return;
        }
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_error_logs',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#error-logs-list').html('<p class="no-errors">No errors recorded. System running smoothly! ✅</p>');
                    updateMonitoringStats({total: 0, recent: 0});
                    showMessage(paymentGatewayAjax.messages.logs_cleared, 'success');
                } else {
                    showMessage('Failed to clear logs: ' + response.data.message, 'error');
                }
            },            error: function() {
                showMessage('Error occurred while clearing logs', 'error');
            }
        });
    }
        });
    }
    
    function toggleAutoFix() {
        const $btn = $('#enable-auto-fix');
        const isEnabled = $btn.hasClass('auto-fix-enabled');
        
        if (isEnabled) {
            $btn.removeClass('auto-fix-enabled').text('🔧 Enable Auto-Fix');
            if (typeof window.AdvancedErrorMonitor !== 'undefined') {
                window.AdvancedErrorMonitor.disableAutoFix();
            }
            showMessage('Auto-fix disabled', 'info');
        } else {
            $btn.addClass('auto-fix-enabled').text('🛑 Disable Auto-Fix');
            if (typeof window.AdvancedErrorMonitor !== 'undefined') {
                window.AdvancedErrorMonitor.enableAutoFix();
            }
            showMessage('Auto-fix enabled', 'success');
        }
    }
    
    function startErrorMonitoring() {
        const $btn = $('#start-monitoring');
        const isMonitoring = $btn.hasClass('monitoring-active');
        
        if (isMonitoring) {
            $btn.removeClass('monitoring-active').text('▶️ Start Monitoring');
            $('#monitoring-live-feed').hide();
            showMessage('Error monitoring stopped', 'info');
        } else {
            $btn.addClass('monitoring-active').text('⏹️ Stop Monitoring');
            $('#monitoring-live-feed').show();
            startLiveFeed();
            showMessage('Error monitoring started', 'success');
        }
    }
    
    // Helper Functions
    function showTestingResults() {
        $('#testing-results').show().addClass('fade-in');
        $('#testing-output').html('');
    }
    
    function updateTestingOutput(message) {
        const $output = $('#testing-output');
        $output.append(message);
        $output.scrollTop($output[0].scrollHeight);
    }
    
    function formatTestResults(results) {
        let output = '';
        for (const [gateway, result] of Object.entries(results)) {
            output += `${gateway.toUpperCase()} Gateway:\n`;
            output += `  Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}\n`;
            output += `  Message: ${result.message}\n\n`;
        }
        return output;
    }
    
    function updateDebugConsole(message) {
        const $console = $('#debug-console-output');
        $console.append(message);
        $console.scrollTop($console[0].scrollHeight);
    }
    
    function closeDebugPanel() {
        $('#debug-panel').removeClass('fade-in').hide();
    }
    
    function updateChecklistProgress(percentage, message) {
        $('.progress-fill').css('width', percentage + '%');
        $('.progress-text').text(message);
    }
    
    function animateProgress(duration = 5000) {
        let progress = 0;
        const interval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress >= 90) {
                progress = 90;
                clearInterval(interval);
            }
            updateChecklistProgress(progress, 'Running checks... ' + Math.round(progress) + '%');
        }, 200);
    }
    
    function displayChecklistResults(results) {
        $('#checklist-results').show().addClass('fade-in');
        
        let passed = 0, failed = 0, warnings = 0;
        let detailsHtml = '';
        
        for (const [key, category] of Object.entries(results)) {
            let categoryPassed = 0, categoryFailed = 0, categoryWarnings = 0;
            
            category.items.forEach(item => {
                if (item.status === 'passed') categoryPassed++;
                else if (item.status === 'failed') categoryFailed++;
                else categoryWarnings++;
            });
            
            passed += categoryPassed;
            failed += categoryFailed;
            warnings += categoryWarnings;
            
            const categoryStatus = categoryFailed > 0 ? 'failed' : (categoryWarnings > 0 ? 'warning' : 'passed');
            
            detailsHtml += `
                <div class="checklist-category">
                    <div class="category-header">
                        <div>
                            <span class="category-status ${categoryStatus}"></span>
                            <strong>${category.title}</strong>
                        </div>
                        <span class="category-summary">${categoryPassed}✅ ${categoryWarnings}⚠️ ${categoryFailed}❌</span>
                    </div>
                    <div class="category-items">
                        ${category.items.map(item => `
                            <div class="checklist-item">
                                <span class="item-status ${item.status}"></span>
                                <div class="item-content">
                                    <div class="item-name">${item.name}</div>
                                    <div class="item-message">${item.message}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        $('.summary-passed .summary-count').text(passed);
        $('.summary-failed .summary-count').text(failed);
        $('.summary-warnings .summary-count').text(warnings);
        
        $('#checklist-details').html(detailsHtml);
    }
    
    function displayQuickCheckResults() {        const quickResults = {
            'basic': {
                title: 'Basic Configuration',
                items: [
                    {name: 'WordPress Installation', status: 'passed', message: 'WordPress properly configured'},
                    {name: 'SSL Certificate', status: 'passed', message: 'SSL active and valid'},
                    {name: 'Plugin Activation', status: 'passed', message: 'Payment gateway plugin active'}
                ]
            },
            'connectivity': {
                title: 'Quick Connectivity Check',
                items: [
                    {name: 'Internet Connection', status: 'passed', message: 'Connection stable'},
                    {name: 'API Endpoints', status: 'passed', message: 'Endpoints reachable'},
                    {name: 'DNS Resolution', status: 'passed', message: 'DNS working correctly'}
                ]            }
        };
        
        displayChecklistResults(quickResults);
    }
    
    function getCurrentChecklistResults() {
        // Return cached results if available
        return window.lastChecklistResults || null;
    }
    
    function updateMonitoringStats(data) {
        $('#total-errors-count').text(data.total || 0);
        $('#recent-errors-count').text(data.recent || 0);
        $('#error-rate').text(data.rate || '0%');
        $('#uptime').text(data.uptime || '99.9%');
    }
    
    function updateErrorLogs(logs) {
        const $logsList = $('#error-logs-list');
        
        if (!logs || logs.length === 0) {
            $logsList.html('<p class="no-errors">No errors recorded. System running smoothly! ✅</p>');
            return;
        }
        
        let logsHtml = logs.map(log => `
            <div class="error-log-item">
                <div class="error-header">
                    <span class="error-level ${log.level}">${log.level.toUpperCase()}</span>
                    <span class="error-time">${log.timestamp}</span>
                </div>
                <div class="error-message">${log.message}</div>
                ${log.context ? `<div class="error-context">${JSON.stringify(log.context, null, 2)}</div>` : ''}
            </div>
        `).join('');
        
        $logsList.html(logsHtml);
    }
    
    function startLiveFeed() {
        const $feed = $('#monitoring-live-feed .feed-content');
        
        // Simulate live error monitoring feed
        setInterval(function() {
            if ($('#start-monitoring').hasClass('monitoring-active')) {
                const timestamp = new Date().toLocaleTimeString();
                const feedEntry = `<div class="feed-entry">[${timestamp}] System monitoring active - All gateways operational</div>`;
                $feed.prepend(feedEntry);
                
                // Keep only last 20 entries
                $feed.children().slice(20).remove();
            }
        }, 5000);
    }
    
    function downloadReport(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
    
    function showMessage(message, type) {
        showFeedbackMessage(message, type);
    }
    
    // Add new advanced functions for the enhanced backend integration
    function validateGatewaySettings() {
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'validate_gateway_settings',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage('Gateway settings validated successfully!', 'success');
                    displayValidationResults(response.data);
                } else {
                    showFeedbackMessage('Validation failed: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred during validation', 'error');
            }
        });
    }
    
    function displayValidationResults(results) {
        let validationHtml = '<div class="validation-results">';
        
        Object.entries(results).forEach(([gateway, result]) => {
            const status = result.valid ? 'valid' : 'invalid';
            validationHtml += `
                <div class="validation-item ${status}">
                    <h4>${gateway.toUpperCase()} Gateway</h4>
                    <p>Status: ${result.valid ? '✅ Valid' : '❌ Invalid'}</p>
                    <p>Message: ${result.message}</p>
                    ${result.issues ? `<ul>${result.issues.map(issue => `<li>${issue}</li>`).join('')}</ul>` : ''}
                </div>
            `;
        });
        
        validationHtml += '</div>';
        
        // Display results in a modal or dedicated area
        $('#validation-results-container').html(validationHtml).show();
    }
    
    // Enhanced stress test function with backend integration
    function stressTestGatewaysAdvanced() {
        showTestingResults();
        updateTestingOutput('💪 Running advanced stress test...\n');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'stress_test_gateways',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    updateTestingOutput(`\n🏆 Advanced Stress Test Results:\n`);
                    updateTestingOutput(`Total requests: ${results.total_requests}\n`);
                    updateTestingOutput(`Successful requests: ${results.successful}\n`);
                    updateTestingOutput(`Failed requests: ${results.failed}\n`);
                    updateTestingOutput(`Success rate: ${results.success_rate}%\n`);
                    updateTestingOutput(`Average response time: ${results.avg_response_time}ms\n`);
                    updateTestingOutput(`Peak memory usage: ${results.peak_memory}\n`);
                } else {
                    updateTestingOutput('❌ Stress test failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during stress testing\n');
            }
        });
    }
    
    function getSystemStatus() {
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_system_status',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateSystemStatusDisplay(response.data);
                } else {
                    showFeedbackMessage('Failed to get system status: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred while getting system status', 'error');
            }
        });
    }
    
    function updateSystemStatusDisplay(status) {
        // Update various status indicators
        $('#system-uptime').text(status.uptime || '99.9%');
        $('#memory-usage').text(status.memory_usage || 'Normal');
        $('#database-status').text(status.database_status || 'Connected');
        $('#gateway-status').text(status.gateway_status || 'Operational');
        
        // Update error counts
        updateMonitoringStats(status.error_stats || {});
    }
    
    function autoFixErrors() {
        if (!confirm('Are you sure you want to run auto-fix? This will attempt to automatically resolve detected issues.')) {
            return;
        }
        
        showFeedbackMessage('Running auto-fix procedures...', 'info');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'auto_fix_errors',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    let message = `Auto-fix completed! Fixed ${results.fixed_count} issues.`;
                    if (results.remaining_count > 0) {
                        message += ` ${results.remaining_count} issues require manual attention.`;
                    }
                    showFeedbackMessage(message, 'success');
                    
                    // Refresh monitoring stats
                    refreshMonitoringStats(true);
                } else {
                    showFeedbackMessage('Auto-fix failed: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred during auto-fix', 'error');
            }
        });
    }
    
    // Add keyboard shortcuts for quick access
    $(document).on('keydown', function(e) {
        // Ctrl+Shift+M for quick monitoring
        if (e.ctrlKey && e.shiftKey && e.key === 'M') {
            e.preventDefault();
            switchTab('monitoring-tab');
        }
        
        // Ctrl+Shift+T for testing
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            switchTab('testing-tab');
        }
        
        // Ctrl+Shift+D for debug console
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            openDebugConsole();
        }
    });

})(jQuery);
