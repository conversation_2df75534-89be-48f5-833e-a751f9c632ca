<?xml version="1.0" encoding="UTF-8"?>
<OfficeApp
    xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
    xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
    xsi:type="TaskPaneApp">

    <!-- Informazioni di base -->
    <Id>FA-EXCEL-ADDIN-12345</Id>
    <Version>*******</Version>
    <ProviderName>Financial Advisor</ProviderName>
    <DefaultLocale>en-US</DefaultLocale>
    <DisplayName DefaultValue="Financial Advisor" />
    <Description DefaultValue="Analyze financial data using AI" />
    <IconUrl DefaultValue="https://example.com/wp-content/plugins/financial-advisor-V4/assets/images/icon-32.png" />
    <HighResolutionIconUrl DefaultValue="https://example.com/wp-content/plugins/financial-advisor-V4/assets/images/icon-64.png" />
    <SupportUrl DefaultValue="https://example.com" />

    <!-- Impostazioni host -->
    <Hosts>
        <Host Name="Workbook" />
    </Hosts>

    <!-- Impostazioni di default -->
    <DefaultSettings>
        <SourceLocation DefaultValue="https://example.com/office-addin/" />
    </DefaultSettings>

    <!-- Permessi richiesti -->
    <Permissions>ReadWriteDocument</Permissions>

    <!-- Versione per Office 2016 e successivi -->
    <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
        <Hosts>
            <Host xsi:type="Workbook">
                <DesktopFormFactor>
                    <GetStarted>
                        <Title resid="GetStarted.Title"/>
                        <Description resid="GetStarted.Description"/>
                        <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
                    </GetStarted>
                    <FunctionFile resid="Commands.Url"/>
                    <ExtensionPoint xsi:type="PrimaryCommandSurface">
                        <OfficeTab id="TabHome">
                            <Group id="CommandsGroup">
                                <Label resid="CommandsGroup.Label"/>
                                <Icon>
                                    <bt:Image size="16" resid="Icon.16x16"/>
                                    <bt:Image size="32" resid="Icon.32x32"/>
                                    <bt:Image size="80" resid="Icon.80x80"/>
                                </Icon>
                                <Control xsi:type="Button" id="TaskpaneButton">
                                    <Label resid="TaskpaneButton.Label"/>
                                    <Supertip>
                                        <Title resid="TaskpaneButton.Label"/>
                                        <Description resid="TaskpaneButton.Tooltip"/>
                                    </Supertip>
                                    <Icon>
                                        <bt:Image size="16" resid="Icon.16x16"/>
                                        <bt:Image size="32" resid="Icon.32x32"/>
                                        <bt:Image size="80" resid="Icon.80x80"/>
                                    </Icon>
                                    <Action xsi:type="ShowTaskpane">
                                        <TaskpaneId>ButtonId1</TaskpaneId>
                                        <SourceLocation resid="Taskpane.Url"/>
                                    </Action>
                                </Control>
                            </Group>
                        </OfficeTab>
                    </ExtensionPoint>
                </DesktopFormFactor>
            </Host>
        </Hosts>
        <Resources>
            <bt:Images>
                <bt:Image id="Icon.16x16" DefaultValue="https://example.com/wp-content/plugins/financial-advisor-V4/assets/images/icon-16.png"/>
                <bt:Image id="Icon.32x32" DefaultValue="https://example.com/wp-content/plugins/financial-advisor-V4/assets/images/icon-32.png"/>
                <bt:Image id="Icon.80x80" DefaultValue="https://example.com/wp-content/plugins/financial-advisor-V4/assets/images/icon-80.png"/>
            </bt:Images>
            <bt:Urls>
                <bt:Url id="Commands.Url" DefaultValue="https://example.com/office-addin/"/>
                <bt:Url id="Taskpane.Url" DefaultValue="https://example.com/office-addin/"/>
                <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://example.com"/>
            </bt:Urls>
            <bt:ShortStrings>
                <bt:String id="GetStarted.Title" DefaultValue="Get started with Financial Advisor"/>
                <bt:String id="CommandsGroup.Label" DefaultValue="Financial Advisor"/>
                <bt:String id="TaskpaneButton.Label" DefaultValue="Financial Advisor"/>
            </bt:ShortStrings>
            <bt:LongStrings>
                <bt:String id="GetStarted.Description" DefaultValue="Financial Advisor is now loaded. Go to the HOME tab and click the Financial Advisor button to get started."/>
                <bt:String id="TaskpaneButton.Tooltip" DefaultValue="Click to open Financial Advisor"/>
            </bt:LongStrings>
        </Resources>
    </VersionOverrides>
</OfficeApp>
