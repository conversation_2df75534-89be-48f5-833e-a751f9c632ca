{"packages": [{"name": "antecedent/patchwork", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/antecedent/patchwork.git", "reference": "1bf183a3e1bd094f231a2128b9ecc5363c269245"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antecedent/patchwork/zipball/1bf183a3e1bd094f231a2128b9ecc5363c269245", "reference": "1bf183a3e1bd094f231a2128b9ecc5363c269245", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": ">=4"}, "time": "2024-12-11T10:19:54+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Method redefinition (monkey-patching) functionality for PHP.", "homepage": "https://antecedent.github.io/patchwork/", "keywords": ["aop", "aspect", "interception", "monkeypatching", "redefinition", "runkit", "testing"], "support": {"issues": "https://github.com/antecedent/patchwork/issues", "source": "https://github.com/antecedent/patchwork/tree/2.2.1"}, "install-path": "../antecedent/patchwork"}, {"name": "brain/monkey", "version": "2.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Brain-WP/BrainMonkey.git", "reference": "d95a9d895352c30f47604ad1b825ab8fa9d1a373"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Brain-WP/BrainMonkey/zipball/d95a9d895352c30f47604ad1b825ab8fa9d1a373", "reference": "d95a9d895352c30f47604ad1b825ab8fa9d1a373", "shasum": ""}, "require": {"antecedent/patchwork": "^2.1.17", "mockery/mockery": "^1.3.5 || ^1.4.4", "php": ">=5.6.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.1", "phpcompatibility/php-compatibility": "^9.3.0", "phpunit/phpunit": "^5.7.26 || ^6.0 || ^7.0 || >=8.0 <8.5.12 || ^8.5.14 || ^9.0"}, "time": "2024-08-29T20:15:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-version/1": "1.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["inc/api.php"], "psr-4": {"Brain\\Monkey\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gmazzap.me", "role": "Developer"}], "description": "Mocking utility for PHP functions and WordPress plugin API", "keywords": ["Monkey Patching", "interception", "mock", "mock functions", "mockery", "patchwork", "redefinition", "runkit", "test", "testing"], "support": {"issues": "https://github.com/Brain-WP/BrainMonkey/issues", "source": "https://github.com/Brain-WP/BrainMonkey"}, "install-path": "../brain/monkey"}, {"name": "composer/ca-bundle", "version": "1.5.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/f65c239c970e7f072f067ab78646e9f0b2935175", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "time": "2025-03-06T14:30:56+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./ca-bundle"}, {"name": "composer/class-map-generator", "version": "1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "time": "2025-03-24T13:50:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./class-map-generator"}, {"name": "composer/composer", "version": "2.8.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "reference": "b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d", "shasum": ""}, "require": {"composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.2 || ^3.2", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^6.3.1", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.11 || ^3.2", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0", "symfony/phpunit-bridge": "^6.4.3 || ^7.0.1"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "time": "2025-05-13T12:01:37+00:00", "bin": ["bin/composer"], "type": "library", "extra": {"phpstan": {"includes": ["phpstan/rules.neon"]}, "branch-alias": {"dev-main": "2.8-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "security": "https://github.com/composer/composer/security/policy", "source": "https://github.com/composer/composer/tree/2.8.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./composer"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "time": "2021-04-07T13:37:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./metadata-minifier"}, {"name": "composer/pcre", "version": "3.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "time": "2024-11-12T16:29:46+00:00", "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./pcre"}, {"name": "composer/semver", "version": "3.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "time": "2024-09-19T14:15:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./semver"}, {"name": "composer/spdx-licenses", "version": "1.5.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/edf364cefe8c43501e21e88110aac10b284c3c9f", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "time": "2025-05-12T21:07:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./spdx-licenses"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "time": "2024-05-06T16:37:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./xdebug-handler"}, {"name": "doctrine/instantiator", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "time": "2022-12-30T00:23:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "install-path": "../doctrine/instantiator"}, {"name": "eftec/bladeone", "version": "3.52", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/EFTEC/BladeOne.git", "reference": "a19bf66917de0b29836983db87a455a4f6e32148"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/EFTEC/BladeOne/zipball/a19bf66917de0b29836983db87a455a4f6e32148", "reference": "a19bf66917de0b29836983db87a455a4f6e32148", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16.1", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.5.4"}, "suggest": {"eftec/bladeonehtml": "Extension to create forms", "ext-mbstring": "This extension is used if it's active"}, "time": "2021-04-17T13:49:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"eftec\\bladeone\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The standalone version Blade Template Engine from Laravel in a single php file", "homepage": "https://github.com/EFTEC/BladeOne", "keywords": ["blade", "php", "template", "templating", "view"], "support": {"issues": "https://github.com/EFTEC/BladeOne/issues", "source": "https://github.com/EFTEC/BladeOne/tree/3.52"}, "install-path": "../eftec/bladeone"}, {"name": "gettext/gettext", "version": "v4.8.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/php-gettext/Gettext.git", "reference": "11af89ee6c087db3cf09ce2111a150bca5c46e12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Gettext/zipball/11af89ee6c087db3cf09ce2111a150bca5c46e12", "reference": "11af89ee6c087db3cf09ce2111a150bca5c46e12", "shasum": ""}, "require": {"gettext/languages": "^2.3", "php": ">=5.4.0"}, "require-dev": {"illuminate/view": "^5.0.x-dev", "phpunit/phpunit": "^4.8|^5.7|^6.5", "squizlabs/php_codesniffer": "^3.0", "symfony/yaml": "~2", "twig/extensions": "*", "twig/twig": "^1.31|^2.0"}, "suggest": {"illuminate/view": "Is necessary if you want to use the Blade extractor", "symfony/yaml": "Is necessary if you want to use the Yaml extractor/generator", "twig/extensions": "Is necessary if you want to use the Twig extractor", "twig/twig": "Is necessary if you want to use the Twig extractor"}, "time": "2024-05-18T10:25:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP gettext manager", "homepage": "https://github.com/oscarotero/Gettext", "keywords": ["JS", "gettext", "i18n", "mo", "po", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Gettext/issues", "source": "https://github.com/php-gettext/Gettext/tree/v4.8.12"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "install-path": "../gettext/gettext"}, {"name": "gettext/languages", "version": "2.12.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/php-gettext/Languages.git", "reference": "0b0b0851c55168e1dfb14305735c64019732b5f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Languages/zipball/0b0b0851c55168e1dfb14305735c64019732b5f1", "reference": "0b0b0851c55168e1dfb14305735c64019732b5f1", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "time": "2025-03-19T11:14:02+00:00", "bin": ["bin/export-plural-rules", "bin/import-cldr-data"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Gettext\\Languages\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "gettext languages with plural rules", "homepage": "https://github.com/php-gettext/Languages", "keywords": ["cldr", "i18n", "internationalization", "l10n", "language", "languages", "localization", "php", "plural", "plural rules", "plurals", "translate", "translations", "unicode"], "support": {"issues": "https://github.com/php-gettext/Languages/issues", "source": "https://github.com/php-gettext/Languages/tree/2.12.1"}, "funding": [{"url": "https://paypal.me/mlocati", "type": "custom"}, {"url": "https://github.com/mlocati", "type": "github"}], "install-path": "../gettext/languages"}, {"name": "hamcrest/hamcrest-php", "version": "v2.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0 || ^3.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0 || ^8.0 || ^9.0"}, "time": "2025-04-30T06:54:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.1.1"}, "install-path": "../hamcrest/hamcrest-php"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/35d262c94959571e8736db1e5c9bc36ab94ae900", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "time": "2025-04-04T13:08:07+00:00", "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.1"}, "install-path": "../justin<PERSON>bow/json-schema"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "time": "2024-11-28T04:54:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "install-path": "../marc-mabe/php-enum"}, {"name": "mck89/peast", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5", "reference": "3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2025-03-07T19:44:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.17.0"}, "install-path": "../mck89/peast"}, {"name": "mockery/mockery", "version": "1.6.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "time": "2024-05-16T03:13:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "install-path": "../mockery/mockery"}, {"name": "mpdf/mpdf", "version": "v8.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e175b05e3e00977b85feb96a8cccb174ac63621f", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "time": "2024-11-18T15:30:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "install-path": "../mpdf/mpdf"}, {"name": "mpdf/psr-http-message-shim", "version": "v2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/f25a0153d645e234f9db42e5433b16d9b113920f", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f", "shasum": ""}, "require": {"psr/http-message": "^2.0"}, "time": "2023-10-02T14:34:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/v2.0.1"}, "install-path": "../mpdf/psr-http-message-shim"}, {"name": "mpdf/psr-log-aware-trait", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/a633da6065e946cc491e1c962850344bb0bf3e78", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78", "shasum": ""}, "require": {"psr/log": "^3.0"}, "time": "2023-05-03T06:19:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v3.0.0"}, "install-path": "../mpdf/psr-log-aware-trait"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "time": "2025-04-29T12:36:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "install-path": "../myclabs/deep-copy"}, {"name": "nb/oxymel", "version": "v0.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nb/oxymel.git", "reference": "cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nb/oxymel/zipball/cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c", "reference": "cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c", "shasum": ""}, "require": {"php": ">=5.2.4"}, "time": "2013-02-24T15:01:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Oxymel": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://extrapolate.me/"}], "description": "A sweet XML builder", "homepage": "https://github.com/nb/oxymel", "keywords": ["xml"], "support": {"issues": "https://github.com/nb/oxymel/issues", "source": "https://github.com/nb/oxymel/tree/master"}, "install-path": "../nb/oxymel"}, {"name": "nikic/php-parser", "version": "v5.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "time": "2024-12-30T11:07:19+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "install-path": "../nikic/php-parser"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "phar-io/manifest", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:33:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../phar-io/manifest"}, {"name": "phar-io/version", "version": "3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2022-02-21T01:04:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "install-path": "../phar-io/version"}, {"name": "phpoffice/math", "version": "0.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/fc2eb6d1a61b058d5dac77197059db30ee3c8329", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "time": "2024-08-12T07:30:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "support": {"issues": "https://github.com/PHPOffice/Math/issues", "source": "https://github.com/PHPOffice/Math/tree/0.2.0"}, "install-path": "../phpoffice/math"}, {"name": "phpoffice/phpword", "version": "1.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/8392134ce4b5dba65130ba956231a1602b848b7f", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.2"}, "require-dev": {"dompdf/dompdf": "^2.0", "ext-gd": "*", "ext-libxml": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpstan/phpstan-phpunit": "@stable", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "time": "2024-08-30T18:03:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "support": {"issues": "https://github.com/PHPOffice/PHPWord/issues", "source": "https://github.com/PHPOffice/PHPWord/tree/1.3.0"}, "install-path": "../phpoffice/phpword"}, {"name": "phpunit/php-code-coverage", "version": "9.2.32", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "time": "2024-08-22T04:23:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-code-coverage"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2021-12-02T12:48:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-file-iterator"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "time": "2020-09-28T05:58:55+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-invoker"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T05:33:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-text-template"}, {"name": "phpunit/php-timer", "version": "5.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:16:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-timer"}, {"name": "phpunit/phpunit", "version": "9.6.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "time": "2025-05-02T06:40:34+00:00", "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.23"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "install-path": "../phpunit/phpunit"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}, {"name": "react/promise", "version": "v3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "time": "2024-05-24T10:39:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/promise"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2024-03-02T06:27:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/cli-parser"}, {"name": "sebastian/code-unit", "version": "1.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:08:54+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-09-28T05:30:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit-reverse-lookup"}, {"name": "sebastian/comparator", "version": "4.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2022-09-14T12:41:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/comparator"}, {"name": "sebastian/complexity", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-12-22T06:19:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/complexity"}, {"name": "sebastian/diff", "version": "4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "time": "2024-03-02T06:30:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/diff"}, {"name": "sebastian/environment", "version": "5.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "time": "2023-02-03T06:03:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/environment"}, {"name": "sebastian/exporter", "version": "4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "time": "2024-03-02T06:33:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/exporter"}, {"name": "sebastian/global-state", "version": "5.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "time": "2024-03-02T06:35:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/global-state"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-12-22T06:20:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/lines-of-code"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:12:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-enumerator"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:14:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-reflector"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-02-03T06:07:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/recursion-context"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "time": "2024-03-14T16:00:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/resource-operations"}, {"name": "sebastian/type", "version": "3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2023-02-03T06:13:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/type"}, {"name": "sebastian/version", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "time": "2020-09-28T06:39:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/version"}, {"name": "seld/jsonlint", "version": "1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "time": "2024-07-11T14:55:45+00:00", "bin": ["bin/jsonlint"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.11.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "install-path": "../seld/jsonlint"}, {"name": "seld/phar-utils", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "time": "2022-08-31T10:31:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.1"}, "install-path": "../seld/phar-utils"}, {"name": "seld/signal-handler", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/signal-handler.git", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"phpstan/phpstan": "^1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^7.5.20 || ^8.5.23", "psr/log": "^1 || ^2 || ^3"}, "time": "2023-09-03T09:24:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "support": {"issues": "https://github.com/Seldaek/signal-handler/issues", "source": "https://github.com/Seldaek/signal-handler/tree/2.0.2"}, "install-path": "../seld/signal-handler"}, {"name": "setasign/fpdi", "version": "v2.6.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/67c31f5e50c93c20579ca9e23035d8c540b51941", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2025-02-05T13:22:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "install-path": "../setasign/fpdi"}, {"name": "smalot/pdfparser", "version": "v2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/8440edbf58c8596074e78ada38dcb0bd041a5948", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "time": "2025-03-31T13:16:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.12.0"}, "install-path": "../smalot/pdfparser"}, {"name": "symfony/console", "version": "v6.4.21", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "time": "2025-04-07T15:42:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/filesystem", "version": "v7.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "time": "2024-10-25T15:15:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "v7.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "time": "2024-12-30T19:00:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php73", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2025-01-02T08:10:11+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php81"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/string", "version": "v7.2.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "time": "2025-04-20T20:18:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "theseer/tokenizer", "version": "1.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:36:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../theseer/tokenizer"}, {"name": "thiagoalessio/tesseract_ocr", "version": "2.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thiagoalessio/tesseract-ocr-for-php.git", "reference": "232a8cb9d571992f9bd1e263f2f6909cf6c173a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thiagoalessio/tesseract-ocr-for-php/zipball/232a8cb9d571992f9bd1e263f2f6909cf6c173a1", "reference": "232a8cb9d571992f9bd1e263f2f6909cf6c173a1", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/php-code-coverage": "^2.2.4 || ^9.0.0"}, "time": "2023-10-05T21:14:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"thiagoalessio\\TesseractOCR\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "thia<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A wrapper to work with Tesseract OCR inside PHP.", "keywords": ["OCR", "Tesseract", "text recognition"], "support": {"irc": "irc://irc.freenode.net/tesseract-ocr-for-php", "issues": "https://github.com/thiagoalessio/tesseract-ocr-for-php/issues", "source": "https://github.com/thiagoalessio/tesseract-ocr-for-php"}, "install-path": "../thiagoalessio/tesseract_ocr"}, {"name": "wp-cli/cache-command", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/cache-command.git", "reference": "14f76b0bc8f9fa0a680e9c70e18fcf627774d055"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/cache-command/zipball/14f76b0bc8f9fa0a680e9c70e18fcf627774d055", "reference": "14f76b0bc8f9fa0a680e9c70e18fcf627774d055", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-05-06T01:43:20+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["cache", "cache add", "cache decr", "cache delete", "cache flush", "cache flush-group", "cache get", "cache incr", "cache patch", "cache pluck", "cache replace", "cache set", "cache supports", "cache type", "transient", "transient delete", "transient get", "transient list", "transient patch", "transient pluck", "transient set", "transient type"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["cache-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Manages object and transient caches.", "homepage": "https://github.com/wp-cli/cache-command", "support": {"issues": "https://github.com/wp-cli/cache-command/issues", "source": "https://github.com/wp-cli/cache-command/tree/v2.2.0"}, "install-path": "../wp-cli/cache-command"}, {"name": "wp-cli/checksum-command", "version": "v2.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/checksum-command.git", "reference": "39992dbd66835f8d5c2cc5bfeacf9d2c450cbafe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/checksum-command/zipball/39992dbd66835f8d5c2cc5bfeacf9d2c450cbafe", "reference": "39992dbd66835f8d5c2cc5bfeacf9d2c450cbafe", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-10T11:02:20+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["core verify-checksums", "plugin verify-checksums"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["checksum-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Verifies file integrity by comparing to published checksums.", "homepage": "https://github.com/wp-cli/checksum-command", "support": {"issues": "https://github.com/wp-cli/checksum-command/issues", "source": "https://github.com/wp-cli/checksum-command/tree/v2.3.1"}, "install-path": "../wp-cli/checksum-command"}, {"name": "wp-cli/config-command", "version": "v2.3.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/config-command.git", "reference": "994b3dc9e8284fc978366920d5c5ae0dde3a004e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/config-command/zipball/994b3dc9e8284fc978366920d5c5ae0dde3a004e", "reference": "994b3dc9e8284fc978366920d5c5ae0dde3a004e", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12", "wp-cli/wp-config-transformer": "^1.4.0"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4.2.8"}, "time": "2025-04-11T09:37:43+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["config", "config edit", "config delete", "config create", "config get", "config has", "config is-true", "config list", "config path", "config set", "config shuffle-salts"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["config-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.alainschlesser.com"}], "description": "Generates and reads the wp-config.php file.", "homepage": "https://github.com/wp-cli/config-command", "support": {"issues": "https://github.com/wp-cli/config-command/issues", "source": "https://github.com/wp-cli/config-command/tree/v2.3.8"}, "install-path": "../wp-cli/config-command"}, {"name": "wp-cli/core-command", "version": "v2.1.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/core-command.git", "reference": "83e4692784a815bb7f5df10b72204f237b5224b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/core-command/zipball/83e4692784a815bb7f5df10b72204f237b5224b9", "reference": "83e4692784a815bb7f5df10b72204f237b5224b9", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2 || ^3", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/checksum-command": "^1 || ^2", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-16T11:23:00+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["core", "core check-update", "core download", "core install", "core is-installed", "core multisite-convert", "core multisite-install", "core update", "core update-db", "core version"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["core-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Downloads, installs, updates, and manages a WordPress installation.", "homepage": "https://github.com/wp-cli/core-command", "support": {"issues": "https://github.com/wp-cli/core-command/issues", "source": "https://github.com/wp-cli/core-command/tree/v2.1.20"}, "install-path": "../wp-cli/core-command"}, {"name": "wp-cli/cron-command", "version": "v2.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/cron-command.git", "reference": "6f450028a75ebd275f12cad62959a0709bf3e7c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/cron-command/zipball/6f450028a75ebd275f12cad62959a0709bf3e7c1", "reference": "6f450028a75ebd275f12cad62959a0709bf3e7c1", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/eval-command": "^2.0", "wp-cli/server-command": "^2.0", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T11:55:20+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["cron", "cron test", "cron event", "cron event delete", "cron event list", "cron event run", "cron event schedule", "cron schedule", "cron schedule list", "cron event unschedule"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["cron-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Tests, runs, and deletes WP-Cron events; manages WP-Cron schedules.", "homepage": "https://github.com/wp-cli/cron-command", "support": {"issues": "https://github.com/wp-cli/cron-command/issues", "source": "https://github.com/wp-cli/cron-command/tree/v2.3.2"}, "install-path": "../wp-cli/cron-command"}, {"name": "wp-cli/db-command", "version": "v2.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/db-command.git", "reference": "f857c91454d7092fa672bc388512a51752d9264a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/db-command/zipball/f857c91454d7092fa672bc388512a51752d9264a", "reference": "f857c91454d7092fa672bc388512a51752d9264a", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-10T11:02:04+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["db", "db clean", "db create", "db drop", "db reset", "db check", "db optimize", "db prefix", "db repair", "db cli", "db query", "db export", "db import", "db search", "db tables", "db size", "db columns"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["db-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Performs basic database operations using credentials stored in wp-config.php.", "homepage": "https://github.com/wp-cli/db-command", "support": {"issues": "https://github.com/wp-cli/db-command/issues", "source": "https://github.com/wp-cli/db-command/tree/v2.1.3"}, "install-path": "../wp-cli/db-command"}, {"name": "wp-cli/embed-command", "version": "v2.0.18", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/embed-command.git", "reference": "52f59a1dacf1d4a1c68fd685f27909e1f493816b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/embed-command/zipball/52f59a1dacf1d4a1c68fd685f27909e1f493816b", "reference": "52f59a1dacf1d4a1c68fd685f27909e1f493816b", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-10T11:01:32+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["embed", "embed fetch", "embed provider", "embed provider list", "embed provider match", "embed handler", "embed handler list", "embed cache", "embed cache clear", "embed cache find", "embed cache trigger"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["embed-command.php"], "psr-4": {"WP_CLI\\Embeds\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://pascalbirchler.com/"}], "description": "Inspects oEmbed providers, clears embed cache, and more.", "homepage": "https://github.com/wp-cli/embed-command", "support": {"issues": "https://github.com/wp-cli/embed-command/issues", "source": "https://github.com/wp-cli/embed-command/tree/v2.0.18"}, "install-path": "../wp-cli/embed-command"}, {"name": "wp-cli/entity-command", "version": "v2.8.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/entity-command.git", "reference": "213611f8ab619ca137d983e9b987f7fbf1ac21d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/entity-command/zipball/213611f8ab619ca137d983e9b987f7fbf1ac21d4", "reference": "213611f8ab619ca137d983e9b987f7fbf1ac21d4", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/cache-command": "^1 || ^2", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/media-command": "^1.1 || ^2", "wp-cli/super-admin-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-05-06T16:12:49+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["comment", "comment approve", "comment count", "comment create", "comment delete", "comment exists", "comment generate", "comment get", "comment list", "comment meta", "comment meta add", "comment meta delete", "comment meta get", "comment meta list", "comment meta patch", "comment meta pluck", "comment meta update", "comment recount", "comment spam", "comment status", "comment trash", "comment unapprove", "comment unspam", "comment untrash", "comment update", "menu", "menu create", "menu delete", "menu item", "menu item add-custom", "menu item add-post", "menu item add-term", "menu item delete", "menu item list", "menu item update", "menu list", "menu location", "menu location assign", "menu location list", "menu location remove", "network meta", "network meta add", "network meta delete", "network meta get", "network meta list", "network meta patch", "network meta pluck", "network meta update", "option", "option add", "option delete", "option get", "option list", "option patch", "option pluck", "option update", "option set-autoload", "option get-autoload", "post", "post create", "post delete", "post edit", "post exists", "post generate", "post get", "post list", "post meta", "post meta add", "post meta clean-duplicates", "post meta delete", "post meta get", "post meta list", "post meta patch", "post meta pluck", "post meta update", "post term", "post term add", "post term list", "post term remove", "post term set", "post update", "post url-to-id", "post-type", "post-type get", "post-type list", "site", "site activate", "site archive", "site create", "site generate", "site deactivate", "site delete", "site empty", "site list", "site mature", "site meta", "site meta add", "site meta delete", "site meta get", "site meta list", "site meta patch", "site meta pluck", "site meta update", "site option", "site private", "site public", "site spam", "site unarchive", "site unmature", "site unspam", "taxonomy", "taxonomy get", "taxonomy list", "term", "term create", "term delete", "term generate", "term get", "term list", "term meta", "term meta add", "term meta delete", "term meta get", "term meta list", "term meta patch", "term meta pluck", "term meta update", "term recount", "term update", "user", "user add-cap", "user add-role", "user application-password", "user application-password create", "user application-password delete", "user application-password exists", "user application-password get", "user application-password list", "user application-password record-usage", "user application-password update", "user create", "user delete", "user exists", "user generate", "user get", "user import-csv", "user list", "user list-caps", "user meta", "user meta add", "user meta delete", "user meta get", "user meta list", "user meta patch", "user meta pluck", "user meta update", "user remove-cap", "user remove-role", "user reset-password", "user session", "user session destroy", "user session list", "user set-role", "user signup", "user signup activate", "user signup delete", "user signup get", "user signup list", "user spam", "user term", "user term add", "user term list", "user term remove", "user term set", "user unspam", "user update"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["entity-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Manage WordPress comments, menus, options, posts, sites, terms, and users.", "homepage": "https://github.com/wp-cli/entity-command", "support": {"issues": "https://github.com/wp-cli/entity-command/issues", "source": "https://github.com/wp-cli/entity-command/tree/v2.8.4"}, "install-path": "../wp-cli/entity-command"}, {"name": "wp-cli/eval-command", "version": "v2.2.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/eval-command.git", "reference": "20ec428a7b9bc604fab0bd33ee8fa20662650455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/eval-command/zipball/20ec428a7b9bc604fab0bd33ee8fa20662650455", "reference": "20ec428a7b9bc604fab0bd33ee8fa20662650455", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/wp-cli-tests": "^4"}, "time": "2024-11-24T17:28:06+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["eval", "eval-file"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["eval-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Executes arbitrary PHP code or files.", "homepage": "https://github.com/wp-cli/eval-command", "support": {"issues": "https://github.com/wp-cli/eval-command/issues", "source": "https://github.com/wp-cli/eval-command/tree/v2.2.6"}, "install-path": "../wp-cli/eval-command"}, {"name": "wp-cli/export-command", "version": "v2.1.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/export-command.git", "reference": "2af32bf12c1bccd6561a215dbbafc2f272647ee8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/export-command/zipball/2af32bf12c1bccd6561a215dbbafc2f272647ee8", "reference": "2af32bf12c1bccd6561a215dbbafc2f272647ee8", "shasum": ""}, "require": {"nb/oxymel": "~0.1.0", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/import-command": "^1 || ^2", "wp-cli/media-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T15:29:08+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["export"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["export-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Exports WordPress content to a WXR file.", "homepage": "https://github.com/wp-cli/export-command", "support": {"issues": "https://github.com/wp-cli/export-command/issues", "source": "https://github.com/wp-cli/export-command/tree/v2.1.14"}, "install-path": "../wp-cli/export-command"}, {"name": "wp-cli/extension-command", "version": "v2.1.24", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/extension-command.git", "reference": "d21a2f504ac43a86b6b08697669b5b0844748133"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/extension-command/zipball/d21a2f504ac43a86b6b08697669b5b0844748133", "reference": "d21a2f504ac43a86b6b08697669b5b0844748133", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2 || ^3", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/cache-command": "^2.0", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/language-command": "^2.0", "wp-cli/scaffold-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4.3.7"}, "time": "2025-05-06T19:17:53+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["plugin", "plugin activate", "plugin deactivate", "plugin delete", "plugin get", "plugin install", "plugin is-installed", "plugin list", "plugin path", "plugin search", "plugin status", "plugin toggle", "plugin uninstall", "plugin update", "theme", "theme activate", "theme delete", "theme disable", "theme enable", "theme get", "theme install", "theme is-installed", "theme list", "theme mod", "theme mod get", "theme mod set", "theme mod remove", "theme path", "theme search", "theme status", "theme update", "theme mod list"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["extension-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.alainschlesser.com"}], "description": "Manages plugins and themes, including installs, activations, and updates.", "homepage": "https://github.com/wp-cli/extension-command", "support": {"issues": "https://github.com/wp-cli/extension-command/issues", "source": "https://github.com/wp-cli/extension-command/tree/v2.1.24"}, "install-path": "../wp-cli/extension-command"}, {"name": "wp-cli/i18n-command", "version": "v2.6.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/i18n-command.git", "reference": "5e73d417398993625331a9f69f6c2ef60f234070"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/i18n-command/zipball/5e73d417398993625331a9f69f6c2ef60f234070", "reference": "5e73d417398993625331a9f69f6c2ef60f234070", "shasum": ""}, "require": {"eftec/bladeone": "3.52", "gettext/gettext": "^4.8", "mck89/peast": "^1.13.11", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/scaffold-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4.3.9"}, "suggest": {"ext-json": "Used for reading and generating JSON translation files", "ext-mbstring": "Used for calculating include/exclude matches in code extraction"}, "time": "2025-04-25T21:49:29+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["i18n", "i18n make-pot", "i18n make-json", "i18n make-mo", "i18n make-php", "i18n update-po"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["i18n-command.php"], "psr-4": {"WP_CLI\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://pascalbirchler.com/"}], "description": "Provides internationalization tools for WordPress projects.", "homepage": "https://github.com/wp-cli/i18n-command", "support": {"issues": "https://github.com/wp-cli/i18n-command/issues", "source": "https://github.com/wp-cli/i18n-command/tree/v2.6.5"}, "install-path": "../wp-cli/i18n-command"}, {"name": "wp-cli/import-command", "version": "v2.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/import-command.git", "reference": "b2c48f3e51683e825738df62bf8ccc7004c5f0f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/import-command/zipball/b2c48f3e51683e825738df62bf8ccc7004c5f0f9", "reference": "b2c48f3e51683e825738df62bf8ccc7004c5f0f9", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/export-command": "^1 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T16:47:25+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["import"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["import-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Imports content from a given WXR file.", "homepage": "https://github.com/wp-cli/import-command", "support": {"issues": "https://github.com/wp-cli/import-command/issues", "source": "https://github.com/wp-cli/import-command/tree/v2.0.14"}, "install-path": "../wp-cli/import-command"}, {"name": "wp-cli/language-command", "version": "v2.0.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/language-command.git", "reference": "7221cc39d2b14fd39e55aa7884889f26eec2f822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/language-command/zipball/7221cc39d2b14fd39e55aa7884889f26eec2f822", "reference": "7221cc39d2b14fd39e55aa7884889f26eec2f822", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-10T11:09:04+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["language", "language core", "language core activate", "language core is-installed", "language core install", "language core list", "language core uninstall", "language core update", "language plugin", "language plugin is-installed", "language plugin install", "language plugin list", "language plugin uninstall", "language plugin update", "language theme", "language theme is-installed", "language theme install", "language theme list", "language theme uninstall", "language theme update", "site switch-language"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["language-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Installs, activates, and manages language packs.", "homepage": "https://github.com/wp-cli/language-command", "support": {"issues": "https://github.com/wp-cli/language-command/issues", "source": "https://github.com/wp-cli/language-command/tree/v2.0.23"}, "install-path": "../wp-cli/language-command"}, {"name": "wp-cli/maintenance-mode-command", "version": "v2.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/maintenance-mode-command.git", "reference": "b947e094e00b7b68c6376ec9bd03303515864062"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/maintenance-mode-command/zipball/b947e094e00b7b68c6376ec9bd03303515864062", "reference": "b947e094e00b7b68c6376ec9bd03303515864062", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/wp-cli-tests": "^4"}, "time": "2024-11-24T17:26:30+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["maintenance-mode", "maintenance-mode activate", "maintenance-mode deactivate", "maintenance-mode status", "maintenance-mode is-active"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["maintenance-mode-command.php"], "psr-4": {"WP_CLI\\MaintenanceMode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://thrijith.com"}], "description": "Activates, deactivates or checks the status of the maintenance mode of a site.", "homepage": "https://github.com/wp-cli/maintenance-mode-command", "support": {"issues": "https://github.com/wp-cli/maintenance-mode-command/issues", "source": "https://github.com/wp-cli/maintenance-mode-command/tree/v2.1.3"}, "install-path": "../wp-cli/maintenance-mode-command"}, {"name": "wp-cli/media-command", "version": "v2.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/media-command.git", "reference": "a810ea0e68473fce6a234e67c6c5f19bb820a753"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/media-command/zipball/a810ea0e68473fce6a234e67c6c5f19bb820a753", "reference": "a810ea0e68473fce6a234e67c6c5f19bb820a753", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^2.0", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-11T09:28:29+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["media", "media import", "media regenerate", "media image-size"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["media-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Imports files as attachments, regenerates thumbnails, or lists registered image sizes.", "homepage": "https://github.com/wp-cli/media-command", "support": {"issues": "https://github.com/wp-cli/media-command/issues", "source": "https://github.com/wp-cli/media-command/tree/v2.2.2"}, "install-path": "../wp-cli/media-command"}, {"name": "wp-cli/mustache", "version": "v2.14.99", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/wp-cli/mustache.php.git", "reference": "ca23b97ac35fbe01c160549eb634396183d04a59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/mustache.php/zipball/ca23b97ac35fbe01c160549eb634396183d04a59", "reference": "ca23b97ac35fbe01c160549eb634396183d04a59", "shasum": ""}, "require": {"php": ">=5.6"}, "replace": {"mustache/mustache": "^2.14.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.19.3", "yoast/phpunit-polyfills": "^2.0"}, "time": "2025-05-06T16:15:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"source": "https://github.com/wp-cli/mustache.php/tree/v2.14.99"}, "install-path": "../wp-cli/mustache"}, {"name": "wp-cli/mustangostang-spyc", "version": "0.6.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/spyc.git", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/spyc/zipball/6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.3.*@dev"}, "time": "2017-04-25T11:26:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["includes/functions.php"], "psr-4": {"Mustangostang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mustangostang", "email": "<EMAIL>"}], "description": "A simple YAML loader/dumper class for PHP (WP-CLI fork)", "homepage": "https://github.com/mustangostang/spyc/", "support": {"source": "https://github.com/wp-cli/spyc/tree/autoload"}, "install-path": "../wp-cli/mustangostang-spyc"}, {"name": "wp-cli/package-command", "version": "v2.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/package-command.git", "reference": "682d8c6bb30c782c3b09c015478c7cbe1cc727a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/package-command/zipball/682d8c6bb30c782c3b09c015478c7cbe1cc727a9", "reference": "682d8c6bb30c782c3b09c015478c7cbe1cc727a9", "shasum": ""}, "require": {"composer/composer": "^2.2.25", "ext-json": "*", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/scaffold-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-11T09:28:45+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["package", "package browse", "package install", "package list", "package update", "package uninstall"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["package-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists, installs, and removes WP-CLI packages.", "homepage": "https://github.com/wp-cli/package-command", "support": {"issues": "https://github.com/wp-cli/package-command/issues", "source": "https://github.com/wp-cli/package-command/tree/v2.6.0"}, "install-path": "../wp-cli/package-command"}, {"name": "wp-cli/php-cli-tools", "version": "v0.12.5", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/php-cli-tools.git", "reference": "34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/php-cli-tools/zipball/34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da", "reference": "34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da", "shasum": ""}, "require": {"php": ">= 5.6.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-03-26T16:13:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["lib/cli/cli.php"], "psr-0": {"cli": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Console utilities for PHP", "homepage": "http://github.com/wp-cli/php-cli-tools", "keywords": ["cli", "console"], "support": {"issues": "https://github.com/wp-cli/php-cli-tools/issues", "source": "https://github.com/wp-cli/php-cli-tools/tree/v0.12.5"}, "install-path": "../wp-cli/php-cli-tools"}, {"name": "wp-cli/process", "version": "v5.9.99", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/process.git", "reference": "f0aec5ca26a702d3157e3a19982b662521ac2b81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/process/zipball/f0aec5ca26a702d3157e3a19982b662521ac2b81", "reference": "f0aec5ca26a702d3157e3a19982b662521ac2b81", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "replace": {"symfony/process": "^5.4.47"}, "time": "2025-05-06T21:26:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/wp-cli/process/tree/v5.9.99"}, "install-path": "../wp-cli/process"}, {"name": "wp-cli/rewrite-command", "version": "v2.0.15", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/rewrite-command.git", "reference": "277ec689b7c268680ff429f52558508622c9b34c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/rewrite-command/zipball/277ec689b7c268680ff429f52558508622c9b34c", "reference": "277ec689b7c268680ff429f52558508622c9b34c", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T12:09:09+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["rewrite", "rewrite flush", "rewrite list", "rewrite structure"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["rewrite-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists or flushes the site's rewrite rules, updates the permalink structure.", "homepage": "https://github.com/wp-cli/rewrite-command", "support": {"issues": "https://github.com/wp-cli/rewrite-command/issues", "source": "https://github.com/wp-cli/rewrite-command/tree/v2.0.15"}, "install-path": "../wp-cli/rewrite-command"}, {"name": "wp-cli/role-command", "version": "v2.0.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/role-command.git", "reference": "ed57fb5436b4d47954b07e56c734d19deb4fc491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/role-command/zipball/ed57fb5436b4d47954b07e56c734d19deb4fc491", "reference": "ed57fb5436b4d47954b07e56c734d19deb4fc491", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T12:24:15+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["role", "role create", "role delete", "role exists", "role list", "role reset", "cap", "cap add", "cap list", "cap remove"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["role-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Adds, removes, lists, and resets roles and capabilities.", "homepage": "https://github.com/wp-cli/role-command", "support": {"issues": "https://github.com/wp-cli/role-command/issues", "source": "https://github.com/wp-cli/role-command/tree/v2.0.16"}, "install-path": "../wp-cli/role-command"}, {"name": "wp-cli/scaffold-command", "version": "v2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/scaffold-command.git", "reference": "b4238ea12e768b3f15d10339a53a8642f82e1d2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/scaffold-command/zipball/b4238ea12e768b3f15d10339a53a8642f82e1d2b", "reference": "b4238ea12e768b3f15d10339a53a8642f82e1d2b", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-11T09:29:34+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["scaffold", "scaffold underscores", "scaffold block", "scaffold child-theme", "scaffold plugin", "scaffold plugin-tests", "scaffold post-type", "scaffold taxonomy", "scaffold theme-tests"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["scaffold-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Generates code for post types, taxonomies, blocks, plugins, child themes, etc.", "homepage": "https://github.com/wp-cli/scaffold-command", "support": {"issues": "https://github.com/wp-cli/scaffold-command/issues", "source": "https://github.com/wp-cli/scaffold-command/tree/v2.5.0"}, "install-path": "../wp-cli/scaffold-command"}, {"name": "wp-cli/search-replace-command", "version": "v2.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/search-replace-command.git", "reference": "65397a7bfdd5ba2cff26f3ab03ef0bcb916c0057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/search-replace-command/zipball/65397a7bfdd5ba2cff26f3ab03ef0bcb916c0057", "reference": "65397a7bfdd5ba2cff26f3ab03ef0bcb916c0057", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T13:07:50+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["search-replace"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["search-replace-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Searches/replaces strings in the database.", "homepage": "https://github.com/wp-cli/search-replace-command", "support": {"issues": "https://github.com/wp-cli/search-replace-command/issues", "source": "https://github.com/wp-cli/search-replace-command/tree/v2.1.8"}, "install-path": "../wp-cli/search-replace-command"}, {"name": "wp-cli/server-command", "version": "v2.0.15", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/server-command.git", "reference": "80a9243f94e0ac073f9bfdb516d2ac7e1fa01a71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/server-command/zipball/80a9243f94e0ac073f9bfdb516d2ac7e1fa01a71", "reference": "80a9243f94e0ac073f9bfdb516d2ac7e1fa01a71", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-10T11:03:13+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["server"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["server-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Launches PHP's built-in web server for a specific WordPress installation.", "homepage": "https://github.com/wp-cli/server-command", "support": {"issues": "https://github.com/wp-cli/server-command/issues", "source": "https://github.com/wp-cli/server-command/tree/v2.0.15"}, "install-path": "../wp-cli/server-command"}, {"name": "wp-cli/shell-command", "version": "v2.0.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/shell-command.git", "reference": "3af53a9f4b240e03e77e815b2ee10f229f1aa591"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/shell-command/zipball/3af53a9f4b240e03e77e815b2ee10f229f1aa591", "reference": "3af53a9f4b240e03e77e815b2ee10f229f1aa591", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-11T09:39:33+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["shell"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["shell-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Opens an interactive PHP console for running and testing PHP code.", "homepage": "https://github.com/wp-cli/shell-command", "support": {"issues": "https://github.com/wp-cli/shell-command/issues", "source": "https://github.com/wp-cli/shell-command/tree/v2.0.16"}, "install-path": "../wp-cli/shell-command"}, {"name": "wp-cli/super-admin-command", "version": "v2.0.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/super-admin-command.git", "reference": "54ac063c384743ee414806d42cb8c61c6aa1fa8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/super-admin-command/zipball/54ac063c384743ee414806d42cb8c61c6aa1fa8e", "reference": "54ac063c384743ee414806d42cb8c61c6aa1fa8e", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-02T13:07:32+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["super-admin", "super-admin add", "super-admin list", "super-admin remove"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["super-admin-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists, adds, or removes super admin users on a multisite installation.", "homepage": "https://github.com/wp-cli/super-admin-command", "support": {"issues": "https://github.com/wp-cli/super-admin-command/issues", "source": "https://github.com/wp-cli/super-admin-command/tree/v2.0.16"}, "install-path": "../wp-cli/super-admin-command"}, {"name": "wp-cli/widget-command", "version": "v2.1.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/widget-command.git", "reference": "73084053f7b32d92583e44d870b81f287beea6a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/widget-command/zipball/73084053f7b32d92583e44d870b81f287beea6a9", "reference": "73084053f7b32d92583e44d870b81f287beea6a9", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.12"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "time": "2025-04-11T09:29:37+00:00", "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["widget", "widget add", "widget deactivate", "widget delete", "widget list", "widget move", "widget reset", "widget update", "sidebar", "sidebar list"], "branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["widget-command.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Adds, moves, and removes widgets; lists sidebars.", "homepage": "https://github.com/wp-cli/widget-command", "support": {"issues": "https://github.com/wp-cli/widget-command/issues", "source": "https://github.com/wp-cli/widget-command/tree/v2.1.12"}, "install-path": "../wp-cli/widget-command"}, {"name": "wp-cli/wp-cli", "version": "v2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli.git", "reference": "03d30d4138d12b4bffd8b507b82e56e129e0523f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli/zipball/03d30d4138d12b4bffd8b507b82e56e129e0523f", "reference": "03d30d4138d12b4bffd8b507b82e56e129e0523f", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.6 || ^7.0 || ^8.0", "symfony/finder": ">2.7", "wp-cli/mustache": "^2.14.99", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.12.4"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4.3.10"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "time": "2025-05-07T01:16:12+00:00", "bin": ["bin/wp", "bin/wp.bat"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.12.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI framework", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli/issues", "source": "https://github.com/wp-cli/wp-cli"}, "install-path": "../wp-cli/wp-cli"}, {"name": "wp-cli/wp-cli-bundle", "version": "v2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli-bundle.git", "reference": "d639a3dab65f4b935b21c61ea3662bf3258a03a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli-bundle/zipball/d639a3dab65f4b935b21c61ea3662bf3258a03a5", "reference": "d639a3dab65f4b935b21c61ea3662bf3258a03a5", "shasum": ""}, "require": {"php": ">=5.6", "wp-cli/cache-command": "^2", "wp-cli/checksum-command": "^2.1", "wp-cli/config-command": "^2.1", "wp-cli/core-command": "^2.1", "wp-cli/cron-command": "^2", "wp-cli/db-command": "^2", "wp-cli/embed-command": "^2", "wp-cli/entity-command": "^2", "wp-cli/eval-command": "^2", "wp-cli/export-command": "^2", "wp-cli/extension-command": "^2.1", "wp-cli/i18n-command": "^2", "wp-cli/import-command": "^2", "wp-cli/language-command": "^2", "wp-cli/maintenance-mode-command": "^2", "wp-cli/media-command": "^2", "wp-cli/package-command": "^2.1", "wp-cli/process": "5.9.99", "wp-cli/rewrite-command": "^2", "wp-cli/role-command": "^2", "wp-cli/scaffold-command": "^2", "wp-cli/search-replace-command": "^2", "wp-cli/server-command": "^2", "wp-cli/shell-command": "^2", "wp-cli/super-admin-command": "^2", "wp-cli/widget-command": "^2", "wp-cli/wp-cli": "^2.12"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^4"}, "suggest": {"psy/psysh": "Enhanced `wp shell` functionality"}, "time": "2025-05-07T02:15:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.12.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI bundle package with default commands.", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli-bundle/issues", "source": "https://github.com/wp-cli/wp-cli-bundle"}, "install-path": "../wp-cli/wp-cli-bundle"}, {"name": "wp-cli/wp-config-transformer", "version": "v1.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-config-transformer.git", "reference": "b78cab1159b43eb5ee097e2cfafe5eab573d2a8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-config-transformer/zipball/b78cab1159b43eb5ee097e2cfafe5eab573d2a8a", "reference": "b78cab1159b43eb5ee097e2cfafe5eab573d2a8a", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"wp-cli/wp-cli-tests": "^4.0"}, "time": "2025-03-31T08:37:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/WPConfigTransformer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Programmatically edit a wp-config.php file.", "homepage": "https://github.com/wp-cli/wp-config-transformer", "support": {"issues": "https://github.com/wp-cli/wp-config-transformer/issues", "source": "https://github.com/wp-cli/wp-config-transformer/tree/v1.4.2"}, "install-path": "../wp-cli/wp-config-transformer"}], "dev": true, "dev-package-names": ["antecedent/patchwork", "brain/monkey", "composer/ca-bundle", "composer/class-map-generator", "composer/composer", "composer/metadata-minifier", "composer/pcre", "composer/semver", "composer/spdx-licenses", "composer/xdebug-handler", "doctrine/instantiator", "eftec/bladeone", "gettext/gettext", "gettext/languages", "hamcrest/hamcrest-php", "justin<PERSON><PERSON>/json-schema", "marc-mabe/php-enum", "mck89/peast", "mockery/mockery", "nb/oxymel", "nikic/php-parser", "phar-io/manifest", "phar-io/version", "phpunit/php-code-coverage", "phpunit/php-file-iterator", "phpunit/php-invoker", "phpunit/php-text-template", "phpunit/php-timer", "phpunit/phpunit", "psr/container", "react/promise", "sebastian/cli-parser", "sebastian/code-unit", "sebastian/code-unit-reverse-lookup", "sebastian/comparator", "sebastian/complexity", "sebastian/diff", "sebastian/environment", "sebastian/exporter", "sebastian/global-state", "sebastian/lines-of-code", "sebastian/object-enumerator", "sebastian/object-reflector", "sebastian/recursion-context", "sebastian/resource-operations", "sebastian/type", "sebastian/version", "seld/jsonlint", "seld/phar-utils", "seld/signal-handler", "symfony/console", "symfony/deprecation-contracts", "symfony/filesystem", "symfony/finder", "symfony/polyfill-ctype", "symfony/polyfill-intl-grapheme", "symfony/polyfill-intl-normalizer", "symfony/polyfill-php73", "symfony/polyfill-php80", "symfony/polyfill-php81", "symfony/service-contracts", "symfony/string", "theseer/tokenizer", "wp-cli/cache-command", "wp-cli/checksum-command", "wp-cli/config-command", "wp-cli/core-command", "wp-cli/cron-command", "wp-cli/db-command", "wp-cli/embed-command", "wp-cli/entity-command", "wp-cli/eval-command", "wp-cli/export-command", "wp-cli/extension-command", "wp-cli/i18n-command", "wp-cli/import-command", "wp-cli/language-command", "wp-cli/maintenance-mode-command", "wp-cli/media-command", "wp-cli/mustache", "wp-cli/mustangostang-spyc", "wp-cli/package-command", "wp-cli/php-cli-tools", "wp-cli/process", "wp-cli/rewrite-command", "wp-cli/role-command", "wp-cli/scaffold-command", "wp-cli/search-replace-command", "wp-cli/server-command", "wp-cli/shell-command", "wp-cli/super-admin-command", "wp-cli/widget-command", "wp-cli/wp-cli", "wp-cli/wp-cli-bundle", "wp-cli/wp-config-transformer"]}