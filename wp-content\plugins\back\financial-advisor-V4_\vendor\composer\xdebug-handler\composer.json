{"name": "composer/xdebug-handler", "description": "Restarts a process without Xdebug.", "type": "library", "license": "MIT", "keywords": ["xdebug", "performance"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues"}, "require": {"php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3", "composer/pcre": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\XdebugHandler\\Tests\\": "tests"}}, "scripts": {"test": "@php vendor/bin/phpunit", "phpstan": "@php vendor/bin/phpstan analyse"}}