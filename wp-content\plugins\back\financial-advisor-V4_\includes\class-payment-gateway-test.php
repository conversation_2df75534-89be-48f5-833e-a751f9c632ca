<?php
/**
 * Payment Gateway Test Class
 * 
 * Provides enhanced testing functionality for payment gateways
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Payment_Gateway_Test {
    
    /**
     * Run enhanced payment gateway tests
     * 
     * @return array Test results organized by category
     */
    public function run_enhanced_payment_gateway_tests() {
        $test_results = array();
        
        // System Environment Tests
        $test_results['system'] = array(
            'title' => __('System Environment', 'document-viewer-plugin'),
            'tests' => array(
                'php_version' => array(
                    'name' => __('PHP Version', 'document-viewer-plugin'),
                    'result' => version_compare(PHP_VERSION, '7.0.0', '>='),
                    'message' => sprintf(__('PHP %s - Recommended: 7.0 or higher', 'document-viewer-plugin'), PHP_VERSION),
                    'critical' => true,
                ),
                'curl' => array(
                    'name' => __('cURL Extension', 'document-viewer-plugin'),
                    'result' => function_exists('curl_version'),
                    'message' => function_exists('curl_version') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for API connections', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'json' => array(
                    'name' => __('JSON Extension', 'document-viewer-plugin'),
                    'result' => function_exists('json_encode') && function_exists('json_decode'),
                    'message' => function_exists('json_encode') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for API data processing', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'ssl' => array(
                    'name' => __('SSL Support', 'document-viewer-plugin'),
                    'result' => extension_loaded('openssl'),
                    'message' => extension_loaded('openssl') 
                        ? __('Installed', 'document-viewer-plugin') 
                        : __('Not installed - Required for secure connections', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'memory_limit' => array(
                    'name' => __('Memory Limit', 'document-viewer-plugin'),
                    'result' => $this->check_memory_limit(),
                    'message' => sprintf(__('Current memory limit: %s - Recommended: 128M or higher', 'document-viewer-plugin'), 
                        ini_get('memory_limit')),
                    'critical' => false,
                )
            )
        );
        
        // Database Tests
        global $wpdb;
        $test_results['database'] = array(
            'title' => __('Database Configuration', 'document-viewer-plugin'),
            'tests' => array(
                'paypal_table' => array(
                    'name' => __('PayPal Config Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}paypal_config'") === $wpdb->prefix . 'paypal_config',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}paypal_config'") === $wpdb->prefix . 'paypal_config'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'stripe_table' => array(
                    'name' => __('Stripe Config Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}stripe_config'") === $wpdb->prefix . 'stripe_config',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}stripe_config'") === $wpdb->prefix . 'stripe_config'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => true,
                ),
                'logs_table' => array(
                    'name' => __('Payment Gateway Logs Table', 'document-viewer-plugin'),
                    'result' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}payment_gateway_logs'") === $wpdb->prefix . 'payment_gateway_logs',
                    'message' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}payment_gateway_logs'") === $wpdb->prefix . 'payment_gateway_logs'
                        ? __('Exists', 'document-viewer-plugin') 
                        : __('Missing - Run database setup', 'document-viewer-plugin'),
                    'critical' => false,
                )
            )
        );
        
        return $test_results;
    }
    
    /**
     * Display enhanced test results in a formatted way
     *
     * @param array $test_results The results from run_enhanced_payment_gateway_tests
     */
    public function display_enhanced_test_results($test_results) {
        if (empty($test_results) || !is_array($test_results)) {
            echo '<div class="notice notice-error"><p>' . __('No test results available.', 'document-viewer-plugin') . '</p></div>';
            return;
        }
        
        echo '<div class="enhanced-test-results">';
        
        foreach ($test_results as $category => $data) {
            echo '<div class="test-category">';
            echo '<h4>' . esc_html($data['title']) . '</h4>';
            echo '<table class="widefat test-results-table">';
            echo '<thead><tr>
                <th>' . __('Test', 'document-viewer-plugin') . '</th>
                <th>' . __('Status', 'document-viewer-plugin') . '</th>
                <th>' . __('Details', 'document-viewer-plugin') . '</th>
            </tr></thead><tbody>';
            
            foreach ($data['tests'] as $test_id => $test) {
                $status_class = $test['result'] ? 'success' : ($test['critical'] ? 'error' : 'warning');
                $status_icon = $test['result'] ? 'âœ…' : ($test['critical'] ? 'âŒ' : 'âš ï¸');
                
                echo '<tr class="test-row test-status-' . esc_attr($status_class) . '">';
                echo '<td class="test-name">' . esc_html($test['name']) . '</td>';
                echo '<td class="test-status"><span class="status-indicator status-' . esc_attr($status_class) . '">' . $status_icon . '</span></td>';
                echo '<td class="test-message">' . esc_html($test['message']) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
            echo '</div>';
        }
        
        // Generate summary
        $total_tests = $passed_tests = $failed_critical = $warnings = 0;
        
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test) {
                $total_tests++;
                if ($test['result']) {
                    $passed_tests++;
                } else {
                    if ($test['critical']) {
                        $failed_critical++;
                    } else {
                        $warnings++;
                    }
                }
            }
        }
        
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100) : 0;
        $summary_class = $failed_critical > 0 ? 'error' : ($warnings > 0 ? 'warning' : 'success');
        
        echo '<div class="test-summary test-status-' . esc_attr($summary_class) . '">';
        echo '<h4>' . __('Test Summary', 'document-viewer-plugin') . '</h4>';
        echo '<p>' . sprintf(
            __('Passed: %1$d of %2$d tests (%3$d%%) | Critical issues: %4$d | Warnings: %5$d', 'document-viewer-plugin'),
            $passed_tests, $total_tests, $success_rate, $failed_critical, $warnings
        ) . '</p>';
        echo '<div class="progress-bar-container">';
        echo '<div class="progress-bar progress-' . esc_attr($summary_class) . '" style="width: ' . esc_attr($success_rate) . '%"></div>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>'; // End .enhanced-test-results
        
        // Log test results to the database
        $this->log_test_results($test_results);
    }
    
    /**
     * Log test results to database
     * 
     * @param array $test_results Test results to log
     */
    private function log_test_results($test_results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return;
        }
        
        // Only log if there are issues
        $has_issues = false;
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test) {
                if (!$test['result']) {
                    $has_issues = true;
                    break 2;
                }
            }
        }
        
        if (!$has_issues) {
            return;
        }
        
        // Create a summary of failed tests
        $failed_tests = array();
        foreach ($test_results as $category => $data) {
            foreach ($data['tests'] as $test_id => $test) {
                if (!$test['result']) {
                    $failed_tests[] = $category . ': ' . $test['name'] . ' - ' . $test['message'];
                }
            }
        }
        
        $error_message = 'Automated test failures detected: ' . count($failed_tests) . ' issues found';
        $error_data = json_encode([
            'failed_tests' => $failed_tests,
            'timestamp' => current_time('mysql'),
            'test_type' => 'automated'
        ]);
        
        $wpdb->insert(
            $table_name,
            array(
                'gateway' => 'system',
                'error_type' => 'test_failure',
                'error_message' => $error_message,
                'error_data' => $error_data,
                'log_level' => 'warning',
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
    
    /**
     * Check if memory limit meets requirements
     *
     * @return bool True if memory limit is 128M or higher
     */
    private function check_memory_limit() {
        $memory_limit = ini_get('memory_limit');
        
        // Convert memory limit to bytes
        $memory_limit = $this->convert_to_bytes($memory_limit);
        
        // 128MB in bytes
        $min_memory = 134217728;
        
        return ($memory_limit >= $min_memory);
    }
    
    /**
     * Convert PHP memory limit value to bytes
     *
     * @param string $memory_value Memory value (e.g. '128M')
     * @return int Value in bytes
     */
    private function convert_to_bytes($memory_value) {
        if (preg_match('/^(\d+)(.)$/', $memory_value, $matches)) {
            if ($matches[2] == 'G') {
                return $matches[1] * 1024 * 1024 * 1024;
            } else if ($matches[2] == 'M') {
                return $matches[1] * 1024 * 1024;
            } else if ($matches[2] == 'K') {
                return $matches[1] * 1024;
            }
        }
        
        return (int)$memory_value;
    }
    
    /**
     * Check API connectivity
     *
     * @param string $url URL to check
     * @return bool True if URL is accessible
     */
    private function check_api_connectivity($url) {
        // If wp_remote_get is available, use it
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($url, array(
                'timeout' => 5,
                'sslverify' => false
            ));
            
            return !is_wp_error($response) && wp_remote_retrieve_response_code($response) < 500;
        }
        
        // Fall back to cURL
        if (function_exists('curl_init')) {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $http_code < 500;
        }
        
        // If no methods available, assume failure
        return false;
    }
      /**
     * Get PayPal configuration
     * 
     * @return array PayPal configuration settings
     */
    public function get_paypal_config() {
        global $wpdb;
        
        // Default config using wp_options
        $config = array(
            'client_id' => get_option('payment_gateway_paypal_client_id', ''),
            'client_secret' => get_option('payment_gateway_paypal_client_secret', ''),
            'sandbox' => get_option('payment_gateway_paypal_sandbox', true),
            'webhook_id' => get_option('payment_gateway_paypal_webhook_id', '')
        );
        
        // Try to get config from database table if it exists
        $table_name = $wpdb->prefix . 'paypal_config';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            if ($row) {
                // Override with table-based config
                $config['client_id'] = $row['client_id'];
                $config['client_secret'] = $row['client_secret'];
                $config['sandbox'] = ($row['environment'] === 'sandbox');
                if (!empty($row['webhook_id'])) {
                    $config['webhook_id'] = $row['webhook_id'];
                }
            }
        }
        
        return $config;
    }
    
    /**
     * Get Stripe configuration
     * 
     * @return array Stripe configuration settings
     */
    public function get_stripe_config() {
        global $wpdb;
        
        // Default config using wp_options
        $config = array(
            'publishable_key' => get_option('payment_gateway_stripe_publishable_key', ''),
            'secret_key' => get_option('payment_gateway_stripe_secret_key', ''),
            'test_mode' => get_option('payment_gateway_stripe_test_mode', true),
            'webhook_secret' => get_option('payment_gateway_stripe_webhook_secret', '')
        );
        
        // Try to get config from database table if it exists
        $table_name = $wpdb->prefix . 'stripe_config';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
            $row = $wpdb->get_row("SELECT * FROM {$table_name} WHERE is_active = 1 LIMIT 1", ARRAY_A);
            if ($row) {
                // Override with table-based config
                $config['publishable_key'] = $row['public_key'];
                $config['secret_key'] = $row['secret_key'];
                $config['test_mode'] = ($row['environment'] === 'test');
                if (!empty($row['webhook_endpoint_secret'])) {
                    $config['webhook_secret'] = $row['webhook_endpoint_secret'];
                }
            }
        }
        
        return $config;
    }
      /**
     * Get payment logs (basic version)
     *
     * @param int $limit Number of logs to retrieve
     * @param string $gateway Filter by gateway name
     * @param string $log_level Filter by log level
     * @return array Array of log entries
     */
    private function get_basic_payment_logs($limit = 50, $gateway = null, $log_level = null) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
            return array();
        }
        
        // Build query
        $query = "SELECT * FROM {$logs_table} WHERE 1=1";
        $where_conditions = array();
        
        if ($gateway) {
            $where_conditions[] = $wpdb->prepare("gateway = %s", $gateway);
        }
        
        if ($log_level) {
            $where_conditions[] = $wpdb->prepare("log_level = %s", $log_level);
        }
        
        if (!empty($where_conditions)) {
            $query .= " AND " . implode(" AND ", $where_conditions);
        }
        
        $query .= " ORDER BY created_at DESC LIMIT %d";
        $query = $wpdb->prepare($query, $limit);
        
        return $wpdb->get_results($query);
    }/**
     * Clear payment logs
     *
     * @return bool True if logs were cleared, false otherwise
     */
    public function clear_payment_logs() {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
            return false;
        }
        
        // Delete logs
        return $wpdb->query("TRUNCATE TABLE {$logs_table}") !== false;
    }
    
    /**
     * Test PayPal API transaction flow
     * 
     * @param array $config PayPal configuration
     * @return array Test results
     */
    public function test_paypal_transaction_flow($config) {
        $results = array(
            'title' => __('Transaction Flow Tests', 'document-viewer-plugin'),
            'tests' => array(),
            'timing' => array(),
            'start_time' => microtime(true)
        );
        
        if (empty($config['client_id']) || empty($config['client_secret'])) {
            $results['tests']['missing_config'] = array(
                'name' => __('Configuration Check', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Missing PayPal API credentials', 'document-viewer-plugin'),
                'critical' => true
            );
            return $results;
        }
        
        // Step 1: Test getting access token
        $start_time = microtime(true);
        $token_result = $this->test_paypal_access_token($config);
        $results['timing']['access_token'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['access_token'] = array(
            'name' => __('Access Token', 'document-viewer-plugin'),
            'result' => $token_result['success'],
            'message' => $token_result['message'],
            'critical' => true,
            'timing' => $results['timing']['access_token'] . 's'
        );
        
        // Skip other tests if token generation failed
        if (!$token_result['success']) {
            $results['error_type'] = 'authentication';
            $results['total_time'] = round(microtime(true) - $results['start_time'], 2);
            return $results;
        }
        
        // Step 2: Test order creation
        $start_time = microtime(true);
        $order_data = array(
            'intent' => 'CAPTURE',
            'purchase_units' => array(
                array(
                    'amount' => array(
                        'currency_code' => 'USD',
                        'value' => '0.01'
                    ),
                    'description' => 'Test transaction from Financial Advisor Plugin'
                )
            ),            'application_context' => array(
                'return_url' => ($this->get_test_site_url()) . '/payment-success',
                'cancel_url' => ($this->get_test_site_url()) . '/payment-cancel'
            )
        );
        
        $order_result = $this->test_paypal_create_order($config, $token_result['token'], $order_data);
        $results['timing']['create_order'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['create_order'] = array(
            'name' => __('Create Order', 'document-viewer-plugin'),
            'result' => $order_result['success'],
            'message' => $order_result['message'],
            'critical' => true,
            'order_id' => isset($order_result['order_id']) ? $order_result['order_id'] : '',
            'timing' => $results['timing']['create_order'] . 's',
            'response_data' => isset($order_result['response_data']) ? $order_result['response_data'] : ''
        );
        
        // Step 3: Test getting order details
        if ($order_result['success'] && isset($order_result['order_id'])) {
            $start_time = microtime(true);
            $get_order_result = $this->test_paypal_get_order($config, $token_result['token'], $order_result['order_id']);
            $results['timing']['get_order'] = round(microtime(true) - $start_time, 2);
            
            $results['tests']['get_order'] = array(
                'name' => __('Get Order Details', 'document-viewer-plugin'),
                'result' => $get_order_result['success'],
                'message' => $get_order_result['message'],
                'critical' => false,
                'timing' => $results['timing']['get_order'] . 's',
                'response_data' => isset($get_order_result['response_data']) ? $get_order_result['response_data'] : ''
            );
            
            // Step 4: Test payment source validation (optional test)
            if ($get_order_result['success']) {
                $start_time = microtime(true);
                $payment_source_result = $this->test_paypal_payment_source_validation($config, $token_result['token'], $order_result['order_id']);
                $results['timing']['payment_source'] = round(microtime(true) - $start_time, 2);
                
                $results['tests']['payment_source'] = array(
                    'name' => __('Payment Source Validation', 'document-viewer-plugin'),
                    'result' => $payment_source_result['success'],
                    'message' => $payment_source_result['message'],
                    'critical' => false,
                    'timing' => $results['timing']['payment_source'] . 's'
                );
            }
        }
        
        // Step 5: Test webhooks validation
        $start_time = microtime(true);
        $webhook_result = $this->test_paypal_webhook_validation($config, $token_result['token']);
        $results['timing']['webhooks'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['webhooks'] = array(
            'name' => __('Webhook Validation', 'document-viewer-plugin'),
            'result' => $webhook_result['success'],
            'message' => $webhook_result['message'],
            'critical' => false,
            'timing' => $results['timing']['webhooks'] . 's',
            'webhook_id' => isset($webhook_result['webhook_id']) ? $webhook_result['webhook_id'] : '',
            'webhook_url' => isset($webhook_result['webhook_url']) ? $webhook_result['webhook_url'] : ''
        );
        
        // Calculate success rate and total time
        $total_tests = count($results['tests']);
        $success_count = 0;
        foreach ($results['tests'] as $test) {
            if ($test['result']) {
                $success_count++;
            }
        }
        
        $results['success_rate'] = $total_tests ? round(($success_count / $total_tests) * 100, 1) : 0;
        $results['total_time'] = round(microtime(true) - $results['start_time'], 2);
        
        // Log transaction flow test
        $this->log_test_result('paypal', 'transaction_flow', $results);
        
        return $results;
    }
    
    /**
     * Test Stripe API transaction flow
     * 
     * @param array $config Stripe configuration
     * @return array Test results
     */
    public function test_stripe_transaction_flow($config) {
        $results = array(
            'title' => __('Stripe Transaction Flow Tests', 'document-viewer-plugin'),
            'tests' => array(),
            'timing' => array(),
            'start_time' => microtime(true)
        );
        
        if (empty($config['public_key']) || empty($config['secret_key'])) {
            $results['tests']['missing_config'] = array(
                'name' => __('Configuration Check', 'document-viewer-plugin'),
                'result' => false,
                'message' => __('Missing Stripe API credentials', 'document-viewer-plugin'),
                'critical' => true
            );
            return $results;
        }
        
        // Step 1: Test API key validation
        $start_time = microtime(true);
        $api_key_result = $this->test_stripe_api_key($config);
        $results['timing']['api_key'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['api_key'] = array(
            'name' => __('API Key Validation', 'document-viewer-plugin'),
            'result' => $api_key_result['success'],
            'message' => $api_key_result['message'],
            'critical' => true,
            'timing' => $results['timing']['api_key'] . 's'
        );
        
        // Skip other tests if API key validation failed
        if (!$api_key_result['success']) {
            $results['error_type'] = 'authentication';
            $results['total_time'] = round(microtime(true) - $results['start_time'], 2);
            return $results;
        }
        
        // Step 2: Test payment intent creation
        $start_time = microtime(true);
        $payment_intent_data = array(
            'amount' => 100, // Amount in cents (1.00)
            'currency' => 'usd',
            'payment_method_types' => ['card'],
            'description' => 'Test payment intent from Financial Advisor Plugin',
            'metadata' => array(
                'test' => true,
                'diagnostic_run' => current_time('mysql')
            )
        );
        
        $payment_intent_result = $this->test_stripe_create_payment_intent($config, $payment_intent_data);
        $results['timing']['payment_intent'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['payment_intent'] = array(
            'name' => __('Create Payment Intent', 'document-viewer-plugin'),
            'result' => $payment_intent_result['success'],
            'message' => $payment_intent_result['message'],
            'critical' => true,
            'payment_intent_id' => isset($payment_intent_result['payment_intent_id']) ? $payment_intent_result['payment_intent_id'] : '',
            'timing' => $results['timing']['payment_intent'] . 's',
            'response_data' => isset($payment_intent_result['response_data']) ? $payment_intent_result['response_data'] : ''
        );
        
        // Step 3: Test retrieving payment intent
        if ($payment_intent_result['success'] && isset($payment_intent_result['payment_intent_id'])) {
            $start_time = microtime(true);
            $retrieve_intent_result = $this->test_stripe_retrieve_payment_intent(
                $config, 
                $payment_intent_result['payment_intent_id']
            );
            $results['timing']['retrieve_intent'] = round(microtime(true) - $start_time, 2);
            
            $results['tests']['retrieve_intent'] = array(
                'name' => __('Retrieve Payment Intent', 'document-viewer-plugin'),
                'result' => $retrieve_intent_result['success'],
                'message' => $retrieve_intent_result['message'],
                'critical' => false,
                'timing' => $results['timing']['retrieve_intent'] . 's',
                'response_data' => isset($retrieve_intent_result['response_data']) ? $retrieve_intent_result['response_data'] : ''
            );
        }
        
        // Step 4: Test webhook endpoint configuration
        $start_time = microtime(true);
        $webhook_result = $this->test_stripe_webhook_endpoint($config);
        $results['timing']['webhook'] = round(microtime(true) - $start_time, 2);
        
        $results['tests']['webhook'] = array(
            'name' => __('Webhook Endpoint', 'document-viewer-plugin'),
            'result' => $webhook_result['success'],
            'message' => $webhook_result['message'],
            'critical' => false,
            'timing' => $results['timing']['webhook'] . 's',
            'webhook_url' => isset($webhook_result['webhook_url']) ? $webhook_result['webhook_url'] : ''
        );
        
        // Calculate success rate and total time
        $total_tests = count($results['tests']);
        $success_count = 0;
        foreach ($results['tests'] as $test) {
            if ($test['result']) {
                $success_count++;
            }
        }
        
        $results['success_rate'] = $total_tests ? round(($success_count / $total_tests) * 100, 1) : 0;
        $results['total_time'] = round(microtime(true) - $results['start_time'], 2);
        
        // Log transaction flow test
        $this->log_test_result('stripe', 'transaction_flow', $results);
        
        return $results;
    }
    
    /**
     * Test PayPal access token generation
     *
     * @param array $config PayPal configuration
     * @return array Result with success status and message
     */
    private function test_paypal_access_token($config) {
        $result = array(
            'success' => false,
            'message' => __('Failed to retrieve access token', 'document-viewer-plugin')
        );
        
        // Determine API URL based on environment
        $api_url = ($config['environment'] === 'sandbox') 
            ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token' 
            : 'https://api-m.paypal.com/v1/oauth2/token';
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Basic ' . base64_encode($config['client_id'] . ':' . $config['client_secret']),
                'Content-Type'  => 'application/x-www-form-urlencoded',
            ),
            'body'        => 'grant_type=client_credentials'
        );
        
        // Make the request if wp_remote_post is available
        if (function_exists('wp_remote_post')) {
            $response = wp_remote_post($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check if access token exists
            if (isset($data['access_token'])) {
                $result = array(
                    'success' => true,
                    'message' => __('Successfully retrieved access token', 'document-viewer-plugin'),
                    'token'   => $data['access_token'],
                    'expires' => isset($data['expires_in']) ? $data['expires_in'] : 3600
                );
            }
        } else {
            // Fall back to cURL if wp_remote_post is not available
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Basic ' . base64_encode($config['client_id'] . ':' . $config['client_secret']),
                    'Content-Type: application/x-www-form-urlencoded'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    if (isset($data['access_token'])) {
                        $result = array(
                            'success' => true,
                            'message' => __('Successfully retrieved access token', 'document-viewer-plugin'),
                            'token'   => $data['access_token'],
                            'expires' => isset($data['expires_in']) ? $data['expires_in'] : 3600
                        );
                    }
                } else {
                    $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $http_code);
                }
            } else {
                $result['message'] = __('Neither wp_remote_post nor cURL is available', 'document-viewer-plugin');
            }
        }
        
        return $result;
    }
    
    /**
     * Test creating a PayPal order
     *
     * @param array $config PayPal configuration
     * @param string $token Access token
     * @param array $order_data Optional order data
     * @return array Result with success status and message
     */
    private function test_paypal_create_order($config, $token, $order_data = array()) {
        $result = array(
            'success' => false,
            'message' => __('Failed to create order', 'document-viewer-plugin')
        );
        
        // Use provided order data or create default test order
        if (empty($order_data)) {
            $order_data = array(
                'intent' => 'CAPTURE',
                'purchase_units' => array(
                    array(
                        'amount' => array(
                            'currency_code' => 'USD',
                            'value' => '0.01'
                        ),
                        'description' => 'Test transaction from Financial Advisor Plugin'
                    )
                ),
                'application_context' => array(
                    'return_url' => ($this->get_test_site_url()) . '/payment-success',
                    'cancel_url' => ($this->get_test_site_url()) . '/payment-cancel'
                )
            );
        }
        
        // Determine API URL based on environment
        $api_url = ($config['environment'] === 'sandbox') 
            ? 'https://api-m.sandbox.paypal.com/v2/checkout/orders' 
            : 'https://api-m.paypal.com/v2/checkout/orders';
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $token,
                'Content-Type'  => 'application/json',
                'Prefer'        => 'return=representation'
            ),
            'body'        => json_encode($order_data)
        );
        
        // Make the request if wp_remote_post is available
        if (function_exists('wp_remote_post')) {
            $response = wp_remote_post($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 201) {
                $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
                $result['response_data'] = wp_remote_retrieve_body($response);
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check if order ID exists
            if (isset($data['id'])) {
                $result = array(
                    'success'      => true,
                    'message'      => __('Successfully created order', 'document-viewer-plugin'),
                    'order_id'     => $data['id'],
                    'status'       => isset($data['status']) ? $data['status'] : '',
                    'response_data' => $data
                );
            }
        } else {
            // Fall back to cURL if wp_remote_post is not available
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Content-Type: application/json',
                    'Prefer: return=representation'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 201) {
                    $data = json_decode($response, true);
                    if (isset($data['id'])) {
                        $result = array(
                            'success'      => true,
                            'message'      => __('Successfully created order', 'document-viewer-plugin'),
                            'order_id'     => $data['id'],
                            'status'       => isset($data['status']) ? $data['status'] : '',
                            'response_data' => $data
                        );
                    }
                } else {
                    $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $http_code);
                    $result['response_data'] = $response;
                }
            } else {
                $result['message'] = __('Neither wp_remote_post nor cURL is available', 'document-viewer-plugin');
            }
        }
        
        return $result;
    }
    
    /**
     * Test getting PayPal order details
     *
     * @param array $config PayPal configuration
     * @param string $token Access token
     * @param string $order_id Order ID
     * @return array Result with success status and message
     */
    private function test_paypal_get_order($config, $token, $order_id) {
        $result = array(
            'success' => false,
            'message' => __('Failed to retrieve order details', 'document-viewer-plugin')
        );
        
        // Determine API URL based on environment
        $api_url = ($config['environment'] === 'sandbox') 
            ? 'https://api-m.sandbox.paypal.com/v2/checkout/orders/' . $order_id 
            : 'https://api-m.paypal.com/v2/checkout/orders/' . $order_id;
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $token,
                'Content-Type'  => 'application/json'
            )
        );
        
        // Make the request if wp_remote_get is available
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $response_code);
                $result['response_data'] = wp_remote_retrieve_body($response);
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check if order ID exists
            if (isset($data['id']) && $data['id'] === $order_id) {
                $result = array(
                    'success'      => true,
                    'message'      => __('Successfully retrieved order details', 'document-viewer-plugin'),
                    'status'       => isset($data['status']) ? $data['status'] : '',
                    'response_data' => $data
                );
            }
        } else {
            // Fall back to cURL if wp_remote_get is not available
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Content-Type: application/json'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    if (isset($data['id']) && $data['id'] === $order_id) {
                        $result = array(
                            'success'      => true,
                            'message'      => __('Successfully retrieved order details', 'document-viewer-plugin'),
                            'status'       => isset($data['status']) ? $data['status'] : '',
                            'response_data' => $data
                        );
                    }
                } else {
                    $result['message'] = sprintf(__('Invalid response code: %s', 'document-viewer-plugin'), $http_code);
                    $result['response_data'] = $response;
                }
            } else {
                $result['message'] = __('Neither wp_remote_get nor cURL is available', 'document-viewer-plugin');
            }
        }
        
        return $result;
    }
    
    /**
     * Test PayPal payment source validation
     *
     * @param array $config PayPal configuration
     * @param string $token Access token
     * @param string $order_id Order ID
     * @return array Result with success status and message
     */
    private function test_paypal_payment_source_validation($config, $token, $order_id) {
        return array(
            'success' => true,
            'message' => __('Payment source validation skipped in testing mode', 'document-viewer-plugin'),
            'details' => __('This is a simulation only, as actual payment source validation requires user interaction', 'document-viewer-plugin')
        );
    }
    
    /**
     * Test PayPal webhook validation
     *
     * @param array $config PayPal configuration
     * @param string $token Access token
     * @return array Result with success status and message
     */
    private function test_paypal_webhook_validation($config, $token) {
        $result = array(
            'success' => false,
            'message' => __('Failed to validate webhooks', 'document-viewer-plugin')
        );
        
        // If webhook_id is not set, try to get webhooks
        if (empty($config['webhook_id'])) {
            // Determine API URL based on environment
            $api_url = ($config['environment'] === 'sandbox') 
                ? 'https://api-m.sandbox.paypal.com/v1/notifications/webhooks' 
                : 'https://api-m.paypal.com/v1/notifications/webhooks';
            
            // Set up request arguments
            $args = array(
                'method'      => 'GET',
                'timeout'     => 45,
                'redirection' => 5,
                'httpversion' => '1.1',
                'headers'     => array(
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type'  => 'application/json'
                )
            );
            
            // Make the request if wp_remote_get is available
            if (function_exists('wp_remote_get')) {
                $response = wp_remote_get($api_url, $args);
                
                // Check for errors
                if (is_wp_error($response)) {
                    $result['message'] = $response->get_error_message();
                    return $result;
                }
                
                // Get response code
                $response_code = wp_remote_retrieve_response_code($response);
                if ($response_code === 200) {
                    // Get body and decode JSON
                    $body = wp_remote_retrieve_body($response);
                    $data = json_decode($body, true);
                    
                    // Check if webhooks exist
                    if (isset($data['webhooks']) && !empty($data['webhooks'])) {
                        $webhook_url = ($this->get_test_site_url()) . '/wp-json/financial-advisor/v1/paypal-webhook';
                        foreach ($data['webhooks'] as $webhook) {
                            if (isset($webhook['url']) && $webhook['url'] === $webhook_url) {
                                return array(
                                    'success' => true,
                                    'message' => __('Webhook already exists and is correctly configured', 'document-viewer-plugin'),
                                    'webhook_id' => $webhook['id'],
                                    'webhook_url' => $webhook['url']
                                );
                            }
                        }
                    }
                }
            }
            
            // No existing webhook found, simulate webhook creation (just for testing)
            $result = array(
                'success' => true,
                'message' => __('Webhook simulation successful', 'document-viewer-plugin'),
                'webhook_id' => 'test_webhook_' . time(),
                'webhook_url' => ($this->get_test_site_url()) . '/wp-json/financial-advisor/v1/paypal-webhook',
                'note' => __('This is a simulation only, as actual webhook creation may require domain verification', 'document-viewer-plugin')
            );
        } else {
            // Webhook ID exists, consider it valid
            $result = array(
                'success' => true,
                'message' => __('Webhook ID exists in configuration', 'document-viewer-plugin'),
                'webhook_id' => $config['webhook_id'],
                'webhook_url' => ($this->get_test_site_url()) . '/wp-json/financial-advisor/v1/paypal-webhook'
            );
        }
        
        return $result;
    }
    
    /**
     * Log test result to database
     * 
     * @param string $gateway Gateway name
     * @param string $test_type Test type
     * @param array $results Test results
     * @return bool Whether the log was saved successfully
     */
    private function log_test_result($gateway, $test_type, $results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
            return false;
        }
        
        // Count failures
        $failures = 0;
        $critical_failures = 0;
        
        foreach ($results['tests'] as $test) {
            if (!$test['result']) {
                $failures++;
                if (isset($test['critical']) && $test['critical']) {
                    $critical_failures++;
                }
            }
        }
        
        // Create log entry
        $insert_data = array(
            'gateway' => $gateway,
            'error_type' => $test_type . '_test',
            'error_message' => sprintf(
                __('%s test completed with %d failures (%d critical)', 'document-viewer-plugin'),
                ucfirst($test_type),
                $failures,
                $critical_failures
            ),
            'error_data' => wp_json_encode($results),
            'log_level' => $critical_failures > 0 ? 'error' : ($failures > 0 ? 'warning' : 'info'),
            'user_id' => get_current_user_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert($table_name, $insert_data);
        
        return $result !== false;    }
      /**
     * Get the test site URL
     * 
     * @return string Site URL for testing purposes
     */    private function get_test_site_url() {
        // Use the site URL as the base for testing
        if (function_exists('site_url')) {
            $site_url = site_url();
            
            // Check if it's a staging/test site
            $is_staging = (strpos($site_url, 'staging') !== false || 
                        strpos($site_url, 'test') !== false || 
                        strpos($site_url, 'dev') !== false);
            
            // If using a development environment, make sure the URL has proper protocol
            if (defined('WP_DEBUG') && WP_DEBUG === true) {
                // Ensure we have https:// for API tests if we're on localhost or dev
                if (strpos($site_url, 'localhost') !== false && strpos($site_url, 'https') === false) {
                    // For local testing, we'll use a mock service URL to avoid protocol issues
                    return 'https://example.com';
                }
            }
            
            return $site_url;
        }
        
        // Fallback for when site_url() is not available
        if (isset($_SERVER['HTTP_HOST'])) {
            $protocol = ((!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') || 
                      (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)) ? "https://" : "http://";
            return $protocol . $_SERVER['HTTP_HOST'];
        }
        
        // Last resort fallback
        return 'https://example.com';
    }    /**
     * Get basic payment logs with filtering (internal use)
     * 
     * @param string $gateway Filter by gateway type     * @param string $log_level Filter by log level
     * @param int $limit Maximum number of logs to retrieve
     * @param string $date_from Filter logs from this date (Y-m-d format)
     * @param string $date_to Filter logs to this date (Y-m-d format)
     * @param string $sort_by Column to sort by
     * @param string $sort_order Sort order (ASC or DESC)
     * @return array Log entries
     */
    public function get_payment_logs($gateway = '', $log_level = '', $limit = 50, $date_from = '', $date_to = '', $sort_by = 'created_at', $sort_order = 'DESC') {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
            return array();
        }
        
        // Start building query
        $query = "SELECT * FROM {$logs_table} WHERE 1=1";
        $where_conditions = array();
        
        if ($gateway) {
            $where_conditions[] = $wpdb->prepare("gateway = %s", $gateway);
        }
        
        if ($log_level) {
            $where_conditions[] = $wpdb->prepare("log_level = %s", $log_level);
        }
        
        if ($date_from) {
            $where_conditions[] = $wpdb->prepare("created_at >= %s", $date_from . ' 00:00:00');
        }
        
        if ($date_to) {
            $where_conditions[] = $wpdb->prepare("created_at <= %s", $date_to . ' 23:59:59');
        }
        
        if (!empty($where_conditions)) {
            $query .= " AND " . implode(" AND ", $where_conditions);
        }
        
        // Validate and sanitize sort parameters
        $allowed_sort_columns = array('id', 'gateway', 'error_type', 'log_level', 'created_at');
        if (!in_array($sort_by, $allowed_sort_columns)) {
            $sort_by = 'created_at';
        }
        
        $sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';
        
        $query .= " ORDER BY {$sort_by} {$sort_order}";
        
        if ($limit > 0) {
            $query .= $wpdb->prepare(" LIMIT %d", $limit);
        }
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Test Stripe API key validity
     *
     * @param array $config Stripe configuration
     * @return array Result with success status and message
     */
    private function test_stripe_api_key($config) {
        $result = array(
            'success' => false,
            'message' => __('Failed to validate Stripe API key', 'document-viewer-plugin')
        );
        
        if (empty($config['secret_key'])) {
            $result['message'] = __('Stripe secret key is missing', 'document-viewer-plugin');
            return $result;
        }
        
        // Set up the API URL for balance check (simple test)
        $api_url = 'https://api.stripe.com/v1/balance';
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded',
            )
        );
        
        // Make the request if wp_remote_get is available
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                
                $result['message'] = sprintf(
                    __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                    $response_code,
                    $error_message
                );
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check if we have available balance data (indicates valid API key)
            if (isset($data['available'])) {
                $result = array(
                    'success' => true,
                    'message' => __('Stripe API key is valid', 'document-viewer-plugin')
                );
            }
        } else {
            // Fall back to cURL if wp_remote_get is not available
            if (function_exists('curl_init')) {
                $ch = curl_init($api_url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $config['secret_key'],
                    'Content-Type: application/x-www-form-urlencoded'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    if (isset($data['available'])) {
                        $result = array(
                            'success' => true,
                            'message' => __('Stripe API key is valid', 'document-viewer-plugin')
                        );
                    }
                } else {
                    $data = json_decode($response, true);
                    $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                    
                    $result['message'] = sprintf(
                        __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                        $http_code,
                        $error_message
                    );
                }
            } else {
                $result['message'] = __('Neither wp_remote_get nor cURL is available', 'document-viewer-plugin');
            }
        }
        
        return $result;
    }
    
    /**
     * Test creating a Stripe payment intent
     *
     * @param array $config Stripe configuration
     * @param array $intent_data Payment intent data
     * @return array Result with success status and message
     */
    private function test_stripe_create_payment_intent($config, $intent_data = array()) {
        $result = array(
            'success' => false,
            'message' => __('Failed to create payment intent', 'document-viewer-plugin')
        );
        
        if (empty($config['secret_key'])) {
            $result['message'] = __('Stripe secret key is missing', 'document-viewer-plugin');
            return $result;
        }
        
        // Default payment intent data
        $default_data = array(
            'amount' => 100, // $1.00
            'currency' => 'usd',
            'payment_method_types' => array('card'),
            'description' => 'Test payment from Financial Advisor Plugin',
        );
        
        $data = wp_parse_args($intent_data, $default_data);
        
        // Ensure payment_method_types is sent as expected
        $payment_method_types = $data['payment_method_types'];
        unset($data['payment_method_types']);
        
        // Build the body
        $body = http_build_query($data);
        foreach ($payment_method_types as $type) {
            $body .= '&payment_method_types[]=' . urlencode($type);
        }
        
        // Set up request arguments
        $args = array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded',
            ),
            'body'        => $body
        );
        
        // Make the request if wp_remote_post is available
        if (function_exists('wp_remote_post')) {
            $response = wp_remote_post('https://api.stripe.com/v1/payment_intents', $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                
                $result['message'] = sprintf(
                    __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                    $response_code,
                    $error_message
                );
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check for the payment intent id
            if (isset($data['id'])) {
                $result = array(
                    'success' => true,
                    'message' => __('Successfully created payment intent', 'document-viewer-plugin'),
                    'payment_intent_id' => $data['id'],
                    'response_data' => $data
                );
            }
        } else {
            // Fall back to cURL if wp_remote_post is not available
            if (function_exists('curl_init')) {
                $ch = curl_init('https://api.stripe.com/v1/payment_intents');
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $config['secret_key'],
                    'Content-Type: application/x-www-form-urlencoded'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    if (isset($data['id'])) {
                        $result = array(
                            'success' => true,
                            'message' => __('Successfully created payment intent', 'document-viewer-plugin'),
                            'payment_intent_id' => $data['id'],
                            'response_data' => $data
                        );
                    }
                } else {
                    $data = json_decode($response, true);
                    $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                    
                    $result['message'] = sprintf(
                        __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                        $http_code,
                        $error_message
                    );
                }
            }
        }
        
        return $result;
    }

    /**
     * Test Stripe retrieve payment intent
     *
     * @param array $config Stripe configuration
     * @param string $payment_intent_id Payment intent ID to retrieve
     * @return array Result with success status and message
     */
    private function test_stripe_retrieve_payment_intent($config, $payment_intent_id) {
        $result = array(
            'success' => false,
            'message' => __('Failed to retrieve payment intent', 'document-viewer-plugin')
        );
        
        if (empty($config['secret_key'])) {
            $result['message'] = __('Stripe secret key is missing', 'document-viewer-plugin');
            return $result;
        }
        
        if (empty($payment_intent_id)) {
            $result['message'] = __('Payment intent ID is required', 'document-viewer-plugin');
            return $result;
        }
        
        // Set up the API URL
        $api_url = 'https://api.stripe.com/v1/payment_intents/' . urlencode($payment_intent_id);
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded',
            )
        );
        
        // Make the request if wp_remote_get is available
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                
                $result['message'] = sprintf(
                    __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                    $response_code,
                    $error_message
                );
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check for the payment intent id
            if (isset($data['id']) && $data['id'] === $payment_intent_id) {
                $result = array(
                    'success' => true,
                    'message' => __('Successfully retrieved payment intent', 'document-viewer-plugin'),
                    'response_data' => $data
                );
            }
        } else {
            // Fall back to cURL if wp_remote_get is not available
            if (function_exists('curl_init')) {
                $ch = curl_init($api_url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $config['secret_key'],
                    'Content-Type: application/x-www-form-urlencoded'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    if (isset($data['id']) && $data['id'] === $payment_intent_id) {
                        $result = array(
                            'success' => true,
                            'message' => __('Successfully retrieved payment intent', 'document-viewer-plugin'),
                            'response_data' => $data
                        );
                    }
                } else {
                    $data = json_decode($response, true);
                    $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                    
                    $result['message'] = sprintf(
                        __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                        $http_code,
                        $error_message
                    );
                }
            }
        }
        
        return $result;
    }

    /**
     * Test Stripe webhook endpoint configuration
     *
     * @param array $config Stripe configuration
     * @return array Result with success status and message
     */
    private function test_stripe_webhook_endpoint($config) {
        $result = array(
            'success' => false,
            'message' => __('Failed to validate webhook endpoint', 'document-viewer-plugin')
        );
        
        if (empty($config['secret_key'])) {
            $result['message'] = __('Stripe secret key is missing', 'document-viewer-plugin');
            return $result;
        }
        
        // Set up the API URL for webhooks
        $api_url = 'https://api.stripe.com/v1/webhook_endpoints';
        
        // Set up request arguments
        $args = array(
            'method'      => 'GET',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers'     => array(
                'Authorization' => 'Bearer ' . $config['secret_key'],
                'Content-Type'  => 'application/x-www-form-urlencoded',
            )
        );
        
        $webhook_url = ($this->get_test_site_url()) . '/wp-json/financial-advisor/v1/stripe-webhook';
        $webhook_exists = false;
        $webhook_id = '';
        
        // Make the request if wp_remote_get is available
        if (function_exists('wp_remote_get')) {
            $response = wp_remote_get($api_url, $args);
            
            // Check for errors
            if (is_wp_error($response)) {
                $result['message'] = $response->get_error_message();
                return $result;
            }
            
            // Get response code
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                
                $result['message'] = sprintf(
                    __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                    $response_code,
                    $error_message
                );
                return $result;
            }
            
            // Get body and decode JSON
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            // Check if our webhook exists
            if (isset($data['data']) && is_array($data['data'])) {
                foreach ($data['data'] as $webhook) {
                    if (isset($webhook['url']) && $webhook['url'] === $webhook_url) {
                        $webhook_exists = true;
                        $webhook_id = $webhook['id'];
                        break;
                    }
                }
            }
            
            // If webhook doesn't exist, create it
            if (!$webhook_exists) {
                // Event types to listen for
                $events = array(
                    'payment_intent.succeeded',
                    'payment_intent.payment_failed',
                    'charge.succeeded',
                    'charge.failed'
                );
                
                // Build the body
                $body = http_build_query(array(
                    'url' => $webhook_url,
                    'description' => 'Financial Advisor Plugin webhook',
                    'enabled_events' => 'default',
                ));
                
                // Set up request arguments for POST
                $args['method'] = 'POST';
                $args['body'] = $body;
                
                // Make the request
                $response = wp_remote_post($api_url, $args);
                
                // Check for errors
                if (is_wp_error($response)) {
                    $result['message'] = $response->get_error_message();
                    return $result;
                }
                
                // Get response code
                $response_code = wp_remote_retrieve_response_code($response);
                if ($response_code !== 200) {
                    $body = wp_remote_retrieve_body($response);
                    $data = json_decode($body, true);
                    $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                    
                    $result['message'] = sprintf(
                        __('Failed to create webhook: %s. %s', 'document-viewer-plugin'), 
                        $response_code,
                        $error_message
                    );
                    return $result;
                }
                
                // Get body and decode JSON
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                
                if (isset($data['id'])) {
                    $webhook_id = $data['id'];
                    $webhook_exists = true;
                }
            }
            
            // Final result
            if ($webhook_exists) {
                $result = array(
                    'success' => true,
                    'message' => __('Webhook endpoint validated successfully', 'document-viewer-plugin'),
                    'webhook_id' => $webhook_id,
                    'webhook_url' => $webhook_url
                );
            }
        } else {
            // Fall back to cURL if wp_remote_get is not available
            if (function_exists('curl_init')) {
                $ch = curl_init($api_url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $config['secret_key'],
                    'Content-Type: application/x-www-form-urlencoded'
                ));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code === 200) {
                    $data = json_decode($response, true);
                    
                    // Check if our webhook exists
                    if (isset($data['data']) && is_array($data['data'])) {
                        foreach ($data['data'] as $webhook) {
                            if (isset($webhook['url']) && $webhook['url'] === $webhook_url) {
                                $webhook_exists = true;
                                $webhook_id = $webhook['id'];
                                break;
                            }
                        }
                    }
                    
                    // If webhook doesn't exist, create it
                    if (!$webhook_exists) {
                        // Event types to listen for
                        $events = array(
                            'payment_intent.succeeded',
                            'payment_intent.payment_failed',
                            'charge.succeeded',
                            'charge.failed'
                        );
                        
                        // Build the body
                        $body = http_build_query(array(
                            'url' => $webhook_url,
                            'description' => 'Financial Advisor Plugin webhook',
                            'enabled_events' => 'default',
                        ));
                        
                        // Create a new cURL request for POST
                        $ch = curl_init($api_url);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                            'Authorization: Bearer ' . $config['secret_key'],
                            'Content-Type: application/x-www-form-urlencoded'
                        ));
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
                        
                        $response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($http_code === 200) {
                            $data = json_decode($response, true);
                            if (isset($data['id'])) {
                                $webhook_id = $data['id'];
                                $webhook_exists = true;
                            }
                        }
                    }
                    
                    // Final result
                    if ($webhook_exists) {
                        $result = array(
                            'success' => true,
                            'message' => __('Webhook endpoint validated successfully', 'document-viewer-plugin'),
                            'webhook_id' => $webhook_id,
                            'webhook_url' => $webhook_url
                        );
                    }
                } else {
                    $data = json_decode($response, true);
                    $error_message = isset($data['error']['message']) ? $data['error']['message'] : '';
                    
                    $result['message'] = sprintf(
                        __('Invalid response code: %s. %s', 'document-viewer-plugin'), 
                        $http_code,
                        $error_message
                    );
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Initialize real-time payment gateway monitoring
     * 
     * @return array Initial monitoring status
     */
    public function initialize_gateway_monitoring() {
        $monitoring_data = array(
            'active' => true,
            'last_check' => current_time('mysql'),
            'gateways' => array(
                'stripe' => $this->get_gateway_health_status('stripe'),
                'paypal' => $this->get_gateway_health_status('paypal')
            ),
            'system_status' => $this->get_system_health_status(),
            'recent_errors' => $this->get_recent_gateway_errors(5)
        );
        
        // Store monitoring state in option
        if (function_exists('update_option')) {
            update_option('fa_payment_gateway_monitoring', $monitoring_data);
        }
        
        return $monitoring_data;
    }
    
    /**
     * Get gateway health status
     * 
     * @param string $gateway_type Gateway type ('stripe' or 'paypal')
     * @return array Gateway health status data
     */
    public function get_gateway_health_status($gateway_type) {
        global $wpdb;
        $status = array(
            'status' => 'unknown',
            'last_check' => current_time('mysql'),
            'success_rate' => 0,
            'avg_response_time' => 0,
            'last_success' => '',
            'last_error' => '',
            'error_count' => 0
        );
        
        if (!in_array($gateway_type, array('stripe', 'paypal'))) {
            return $status;
        }
        
        // Get logs for status calculation
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
            return $status;
        }
        
        // Get logs from the last 24 hours
        $query = $wpdb->prepare(
            "SELECT * FROM {$logs_table} 
            WHERE gateway = %s 
            AND created_at >= DATE_SUB(%s, INTERVAL 24 HOUR)
            ORDER BY created_at DESC",
            $gateway_type,
            current_time('mysql')
        );
        
        $logs = $wpdb->get_results($query);
        
        if (empty($logs)) {
            // No recent logs, check older logs for last success/error
            $last_log = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$logs_table} 
                WHERE gateway = %s 
                ORDER BY created_at DESC 
                LIMIT 1",
                $gateway_type
            ));
            
            if ($last_log) {
                $status['last_check'] = $last_log->created_at;
                $status['status'] = ($last_log->log_level === 'error') ? 'error' : 'warning';
                $status['last_error'] = ($last_log->log_level === 'error') ? $last_log->created_at : '';
            }
            
            return $status;
        }
        
        // Calculate metrics
        $total_logs = count($logs);
        $error_logs = 0;
        $success_logs = 0;
        $response_times = array();
        $last_success = null;
        $last_error = null;
        
        foreach ($logs as $log) {
            // Track errors vs successes
            if ($log->log_level === 'error') {
                $error_logs++;
                if ($last_error === null) {
                    $last_error = $log->created_at;
                }
            } else {
                $success_logs++;
                if ($last_success === null) {
                    $last_success = $log->created_at;
                }
            }
            
            // Extract response time if available
            $log_data = json_decode($log->log_data, true);
            if (isset($log_data['total_time'])) {
                $response_times[] = floatval($log_data['total_time']);
            }
        }
        
        // Calculate success rate
        $success_rate = ($total_logs > 0) ? round(($success_logs / $total_logs) * 100, 1) : 0;
        
        // Calculate average response time
        $avg_response_time = !empty($response_times) ? round(array_sum($response_times) / count($response_times), 2) : 0;
        
        // Determine status
        if ($success_rate >= 95) {
            $status['status'] = 'success';
        } elseif ($success_rate >= 80) {
            $status['status'] = 'warning';
        } else {
            $status['status'] = 'error';
        }
        
        // Update status array
        $status['success_rate'] = $success_rate;
        $status['avg_response_time'] = $avg_response_time;
        $status['last_success'] = $last_success;
        $status['last_error'] = $last_error;
        $status['error_count'] = $error_logs;
        
        return $status;
    }
    
    /**
     * Get system health status
     * 
     * @return array System health metrics
     */
    public function get_system_health_status() {
        $system_status = array();
        
        // PHP Version check
        $php_version = PHP_VERSION;
        $php_version_status = version_compare($php_version, '7.0.0', '>=') ? 'success' : 'error';
        $system_status['php_version'] = $php_version;
        $system_status['php_version_status'] = $php_version_status;
        
        // SSL Status
        $ssl_status = extension_loaded('openssl') ? 'success' : 'error';
        $system_status['ssl_status'] = extension_loaded('openssl') ? __('Enabled', 'document-viewer-plugin') : __('Disabled', 'document-viewer-plugin');
        $system_status['ssl_status_status'] = $ssl_status;
        
        // cURL Status
        $curl_status = function_exists('curl_version') ? 'success' : 'error';
        $system_status['curl_status'] = function_exists('curl_version') ? __('Enabled', 'document-viewer-plugin') : __('Disabled', 'document-viewer-plugin');
        $system_status['curl_status_status'] = $curl_status;
        
        // Memory Usage
        $memory_limit = ini_get('memory_limit');
        $memory_usage = function_exists('memory_get_usage') ? round(memory_get_usage() / 1024 / 1024, 2) . 'MB' : __('Unknown', 'document-viewer-plugin');
        $system_status['memory_usage'] = sprintf(__('%s / %s', 'document-viewer-plugin'), $memory_usage, $memory_limit);
        $memory_limit_bytes = $this->return_bytes($memory_limit);
        $system_status['memory_usage_status'] = ($memory_limit_bytes >= 128 * 1024 * 1024) ? 'success' : 'warning';
        
        return $system_status;
    }
    
    /**
     * Get recent gateway errors
     * 
     * @param int $limit Maximum number of errors to retrieve
     * @return array Recent errors
     */
    public function get_recent_gateway_errors($limit = 5) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
            return array();
        }
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$logs_table} 
            WHERE log_level = 'error' 
            ORDER BY created_at DESC 
            LIMIT %d",
            $limit
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Convert PHP memory limit string to bytes
     * 
     * @param string $val Memory limit string (e.g. '128M')
     * @return int Memory limit in bytes
     */
    private function return_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int) $val;
        
        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }
      /**
     * Render the gateway monitoring UI
     * 
     * @param array $monitoring_data Optional. Data to render. If not provided, it will be loaded from options.
     * @return string HTML content for the monitoring dashboard
     */
    public function render_gateway_monitoring_ui($monitoring_data = null) {
        if ($monitoring_data === null && function_exists('get_option')) {
            $monitoring_data = get_option('fa_payment_gateway_monitoring', array());
        }
        
        if (empty($monitoring_data) || !is_array($monitoring_data)) {
            $monitoring_data = $this->initialize_gateway_monitoring();
        }
        
        ob_start();
        
        $html = '<div class="gateway-monitoring-dashboard">';
        $html .= '    <div class="dashboard-header">';
        $html .= '        <h2>' . __('Payment Gateway Monitoring', 'document-viewer-plugin') . '</h2>';
        $html .= '        <div class="last-check">';
        $html .= '            ' . __('Last check:', 'document-viewer-plugin') . ' ';
        $html .= '            <span class="timestamp">' . (isset($monitoring_data['last_check']) ? esc_html($monitoring_data['last_check']) : '') . '</span>';
        $html .= '            <button class="button refresh-status" data-nonce="' . wp_create_nonce('fa_gateway_status_check') . '">';
        $html .= '                ' . __('Refresh', 'document-viewer-plugin');
        $html .= '            </button>';
        $html .= '        </div>';
        $html .= '    </div>';
        
        $html .= '    <div class="gateway-status-cards">';
        
        foreach (array('stripe', 'paypal') as $gateway) {
            $status = isset($monitoring_data['gateways'][$gateway]) ? $monitoring_data['gateways'][$gateway] : array();
            $status_class = isset($status['status']) ? $status['status'] : 'unknown';
            $last_success = isset($status['last_success']) ? $status['last_success'] : '';
            $human_time = !empty($last_success) ? $this->get_human_time_diff(strtotime($last_success)) : __('Never', 'document-viewer-plugin');
            
            $html .= '    <div class="gateway-card gateway-' . esc_attr($gateway) . ' status-' . esc_attr($status_class) . '">';
            $html .= '        <div class="gateway-header">';
            $html .= '            <h3>' . esc_html(ucfirst($gateway)) . '</h3>';
            $html .= '            <span class="status-indicator status-' . esc_attr($status_class) . '">';
            $html .= '                ' . $this->get_status_icon($status_class);
            $html .= '            </span>';
            $html .= '        </div>';
            $html .= '        <div class="gateway-metrics">';
            $html .= '            <div class="metric">';
            $html .= '                <span class="metric-label">' . __('Status', 'document-viewer-plugin') . '</span>';
            $html .= '                <span class="metric-value">' . esc_html($this->get_status_label($status_class)) . '</span>';
            $html .= '            </div>';
            $html .= '            <div class="metric">';
            $html .= '                <span class="metric-label">' . __('Success Rate', 'document-viewer-plugin') . '</span>';
            $html .= '                <span class="metric-value">' . (isset($status['success_rate']) ? esc_html($status['success_rate'] . '%') : '0%') . '</span>';
            $html .= '            </div>';
            $html .= '            <div class="metric">';
            $html .= '                <span class="metric-label">' . __('Last Successful Test', 'document-viewer-plugin') . '</span>';
            $html .= '                <span class="metric-value">' . esc_html($human_time) . ' ' . __('ago', 'document-viewer-plugin') . '</span>';
            $html .= '            </div>';
            $html .= '            <div class="metric">';
            $html .= '                <span class="metric-label">' . __('Response Time', 'document-viewer-plugin') . '</span>';
            $html .= '                <span class="metric-value">' . (isset($status['avg_response_time']) ? esc_html($status['avg_response_time'] . 's') : 'N/A') . '</span>';
            $html .= '            </div>';
            $html .= '        </div>';
            $html .= '        <div class="gateway-actions">';
            $html .= '            <button class="button run-test-button" data-gateway="' . esc_attr($gateway) . '" data-nonce="' . wp_create_nonce('fa_gateway_status_check') . '">';
            $html .= '                ' . __('Run Test', 'document-viewer-plugin');
            $html .= '            </button>';
            $html .= '            <a href="' . esc_url(admin_url('admin.php?page=financial-advisor-diagnostics&gateway=' . $gateway)) . '" class="button">';
            $html .= '                ' . __('View Details', 'document-viewer-plugin');
            $html .= '            </a>';
            $html .= '        </div>';
            $html .= '    </div>';
        }
        
        $html .= '    </div>';
        
        $html .= '    <div class="system-status-panel">';
        $html .= '        <h3>' . __('System Status', 'document-viewer-plugin') . '</h3>';
        $html .= '        <div class="system-metrics">';
        
        $system_status = isset($monitoring_data['system_status']) ? $monitoring_data['system_status'] : array();
        $metrics = array(
            'php_version' => __('PHP Version', 'document-viewer-plugin'),
            'ssl_status' => __('SSL Status', 'document-viewer-plugin'),
            'curl_status' => __('cURL Status', 'document-viewer-plugin'),
            'memory_usage' => __('Memory Usage', 'document-viewer-plugin')
        );
        
        foreach ($metrics as $key => $label) {
            $value = isset($system_status[$key]) ? $system_status[$key] : 'N/A';
            $status = isset($system_status[$key . '_status']) ? $system_status[$key . '_status'] : 'unknown';
            
            $html .= '            <div class="system-metric status-' . esc_attr($status) . '">';
            $html .= '                <span class="metric-label">' . esc_html($label) . '</span>';
            $html .= '                <span class="metric-value">' . esc_html($value) . '</span>';
            $html .= '                <span class="status-dot status-' . esc_attr($status) . '"></span>';
            $html .= '            </div>';
        }
        
        $html .= '        </div>';
        $html .= '    </div>';
        
        if (!empty($monitoring_data['recent_errors'])) {
            $html .= '    <div class="recent-errors-panel">';
            $html .= '        <h3>' . __('Recent Errors', 'document-viewer-plugin') . '</h3>';
            $html .= '        <table class="widefat errors-table">';
            $html .= '            <thead>';
            $html .= '                <tr>';
            $html .= '                    <th>' . __('Time', 'document-viewer-plugin') . '</th>';
            $html .= '                    <th>' . __('Gateway', 'document-viewer-plugin') . '</th>';
            $html .= '                    <th>' . __('Error', 'document-viewer-plugin') . '</th>';
            $html .= '                </tr>';
            $html .= '            </thead>';
            $html .= '            <tbody>';
            
            foreach ($monitoring_data['recent_errors'] as $error) {
                $html .= '                <tr>';
                $html .= '                    <td>' . esc_html($error['created_at']) . '</td>';
                $html .= '                    <td>' . esc_html(ucfirst($error['gateway'])) . '</td>';
                $html .= '                    <td>' . esc_html($error['message']) . '</td>';
                $html .= '                </tr>';
            }
            
            $html .= '            </tbody>';
            $html .= '        </table>';
            $html .= '    </div>';
        }
        
        $html .= '</div>';
        
        echo $html;
    }
    
    /**
     * Get status icon based on status code
     * 
     * @param string $status_code Status code (success, warning, error, unknown)
     * @return string HTML for the status icon
     */
    private function get_status_icon($status_code) {
        switch ($status_code) {
            case 'success':
                return '<span class="dashicons dashicons-yes-alt"></span>';
            case 'warning':
                return '<span class="dashicons dashicons-warning"></span>';
            case 'error':
                return '<span class="dashicons dashicons-no-alt"></span>';
            case 'unknown':
            default:
                return '<span class="dashicons dashicons-warning"></span>';
        }
    }

    /**
     * Get status label based on status code
     * 
     * @param string $status_code Status code (success, warning, error, unknown)
     * @return string User-friendly status label
     */
    private function get_status_label($status_code) {
        switch ($status_code) {
            case 'success':
                return __('Operational', 'document-viewer-plugin');
            case 'warning':
                return __('Degraded Performance', 'document-viewer-plugin');
            case 'error':
                return __('Service Disruption', 'document-viewer-plugin');
            case 'unknown':
            default:
                return __('Unknown Status', 'document-viewer-plugin');
        }
    }
    
    /**
     * AJAX handler for checking gateway status
     */
    public function ajax_check_gateway_status() {
        // Verify nonce and permissions for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'fa_gateway_status_check')) {
            wp_send_json_error(array(
                'message' => __('Security verification failed', 'document-viewer-plugin')
            ));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action', 'document-viewer-plugin')
            ));
            return;
        }
        
        // Refresh monitoring data
        $monitoring_data = $this->initialize_gateway_monitoring();
        
        // Return success with monitoring data
        wp_send_json_success(array(
            'monitoring' => $monitoring_data,
            'html' => $this->render_gateway_monitoring_ui($monitoring_data)
        ));
    }
    
    /**
     * Register AJAX handlers for gateway monitoring
     */
    public function register_ajax_handlers() {
        if (function_exists('add_action')) {
            // Add AJAX handlers for logged-in users
            add_action('wp_ajax_fa_check_gateway_status', array($this, 'ajax_check_gateway_status'));
            add_action('wp_ajax_fa_run_gateway_test', array($this, 'ajax_run_gateway_test'));
        }
    }
}

