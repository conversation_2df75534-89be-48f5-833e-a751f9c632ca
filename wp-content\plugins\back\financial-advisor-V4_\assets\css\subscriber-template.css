/**
 * Styles for Financial Advisor subscriber pages
 */

/* Main container */
.subscriber-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2em;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Header section */
.subscriber-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 1px solid #eaeaea;
}

.subscriber-page-title {
    margin: 0;
    color: #333;
    font-size: 2.5em;
}

/* Subscriber info box */
.subscriber-info-box {
    padding: 1.5em;
    background: #f7f7f7;
    border-radius: 6px;
    border-left: 4px solid #2271b1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.subscriber-greeting h4 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: #2271b1;
    font-size: 1.2em;
}

.subscriber-greeting p {
    margin: 0.25em 0;
    color: #555;
}

/* Main content area */
.subscriber-page-content {
    min-height: 400px;
    margin-bottom: 2em;
    line-height: 1.6;
}

/* Footer section */
.subscriber-page-footer {
    padding-top: 1em;
    border-top: 1px solid #eaeaea;
    color: #666;
    font-size: 0.9em;
    text-align: center;
}

.subscription-note {
    color: #2271b1;
}

.wp-admin-note {
    color: #d63638;
}

/* Style shortcode content */
.fa-login-required,
.fa-subscription-required,
.fa-access-denied {
    padding: 1em;
    margin: 1em 0;
    border-radius: 4px;
    background-color: #f8f8f8;
    border-left: 4px solid #dc3232;
}

.fa-login-required {
    border-left-color: #ffb900;
}

.fa-login-required a.button,
.fa-subscription-required a.button {
    display: inline-block;
    padding: 0.5em 1em;
    background: #2271b1;
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    font-weight: 500;
}

.fa-login-required a.button:hover,
.fa-subscription-required a.button:hover {
    background: #135e96;
}

.fa-subscriber-info {
    background: #f8f8f8;
    padding: 1em;
    border-radius: 4px;
    margin: 1em 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .subscriber-page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .subscriber-info-box {
        margin-top: 1em;
        width: 100%;
    }
    
    .subscriber-page-title {
        font-size: 2em;
    }
}
