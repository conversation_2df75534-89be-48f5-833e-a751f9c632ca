/**
 * Help Line JS - Gestisce la funzionalità a comparsa/scomparsa dell'Help Line
 * Versione 1.0
 */

jQuery(document).ready(function($) {
    
    // Gestisce il click sul pulsante di toggle per aprire/chiudere l'help line
    $('.help-line-toggle').on('click', function() {
        $('.help-line-panel').toggleClass('open');
    });
    
    // Chiude l'help line quando si fa clic al di fuori di essa
    $(document).on('click', function(event) {
        if (!$(event.target).closest('.help-line-panel').length) {
            $('.help-line-panel').removeClass('open');
        }
    });
    
    // Impedisce che i clic all'interno dell'help line la chiudano
    $('.help-line-content').on('click', function(event) {
        event.stopPropagation();
    });
    
    // Gestisce i tasti di scelta rapida (ESC per chiudere)
    $(document).on('keydown', function(event) {
        if (event.key === 'Escape') {
            $('.help-line-panel').removeClass('open');
        }
    });
    
    // Salva la posizione aperta/chiusa dell'help line nel localStorage
    function saveHelpLineState() {
        localStorage.setItem('help_line_open', $('.help-line-panel').hasClass('open'));
    }
    
    // Carica lo stato dell'help line dal localStorage all'avvio
    function loadHelpLineState() {
        var isOpen = localStorage.getItem('help_line_open') === 'true';
        if (isOpen) {
            $('.help-line-panel').addClass('open');
        }
    }
    
    // Salva lo stato quando cambia
    $('.help-line-toggle').on('click', saveHelpLineState);
    
    // Carica lo stato all'avvio
    loadHelpLineState();
});