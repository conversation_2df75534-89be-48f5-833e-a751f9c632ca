<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Login Widget
 * 
 * Widget per permettere agli utenti di accedere al servizio (sia utenti WordPress che subscriber)
 */
class Unified_Login_Widget extends WP_Widget {
    public function __construct() {
        parent::__construct(
            'unified_login_widget',
            __('Unified Login Widget', 'document-viewer-plugin'),
            array('description' => __('Un widget per permettere agli utenti WordPress e subscriber di accedere al servizio.', 'document-viewer-plugin'))
        );

        // Aggiungi script e stili
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Register AJAX handlers
        add_action('wp_ajax_nopriv_unified_login', array($this, 'handle_login'));
        add_action('wp_ajax_unified_login', array($this, 'handle_login')); // For users already logged in but switching accounts
        
        // Add reset password functionality
        add_action('wp_ajax_nopriv_request_password_reset', array($this, 'handle_password_reset_request'));
    }

    /**
     * Carica gli script e gli stili necessari
     */    public function enqueue_scripts() {
        wp_enqueue_style('unified-login-widget-css', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/login-widget.css', array(), '1.0.0');
        wp_enqueue_script('unified-login-widget-js', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/login-widget.js', array('jquery'), '1.0.0', true);
        
        // Utilizziamo un nonce unificato per tutti i tipi di utenti
        $unified_nonce = wp_create_nonce('unified_access_nonce');
        
        wp_localize_script('unified-login-widget-js', 'loginWidgetParams', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => $unified_nonce,
            'documentViewerNonce' => $unified_nonce, // Aggiungiamo il nonce anche con il nome utilizzato dal document viewer
            'messages' => array(
                'requiredFields' => __('Tutti i campi sono obbligatori', 'document-viewer-plugin'),
                'invalidEmail' => __('Email non valida', 'document-viewer-plugin'),
                'loginSuccess' => __('Accesso effettuato con successo!', 'document-viewer-plugin'),
                'loginError' => __('Errore durante l\'accesso. Verifica le credenziali.', 'document-viewer-plugin'),
                'resetSent' => __('Email di recupero inviata con successo. Controlla la tua casella di posta.', 'document-viewer-plugin'),
                'resetError' => __('Errore nell\'invio dell\'email di recupero. Riprova.', 'document-viewer-plugin'),
                'passwordWeak' => __('Password debole', 'document-viewer-plugin'),
                'passwordMedium' => __('Password media', 'document-viewer-plugin'),
                'passwordStrong' => __('Password forte', 'document-viewer-plugin'),
                'passwordVeryStrong' => __('Password molto forte', 'document-viewer-plugin'),
                'passwordMismatch' => __('Le password non corrispondono', 'document-viewer-plugin'),
                'passwordTooShort' => __('La password deve contenere almeno 8 caratteri', 'document-viewer-plugin'),
                'privacyRequired' => __('Devi accettare la privacy policy', 'document-viewer-plugin')
            )
        ));
    }    /**
     * Front-end display del widget
     */
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        // Check for activation message
        $activation_message = $this->maybe_get_activation_message();
        if ($activation_message) {
            echo $activation_message;
        }
        
        // Verifica se l'utente è loggato (WP o non WP)
        $is_wp_user_logged_in = is_user_logged_in();
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']);
        
        // Mostra il form di login se l'utente non è già loggato (né come WP né come subscriber)
        if (!$is_wp_user_logged_in && !$is_subscriber_logged_in) {
            $this->display_login_form();
        } else {
            $this->display_logged_in_user();
        }
        
        echo $args['after_widget'];
    }
    
    /**
     * Check if there's an activation message to display
     * 
     * @return string|false The activation message HTML or false if none
     */
    private function maybe_get_activation_message() {
        // Check if we're in an activation context
        if (!isset($_GET['activation']) || !isset($_GET['uid'])) {
            return false;
        }
        
        $user_id = absint($_GET['uid']);
        $activation_status = sanitize_text_field($_GET['activation']);
        
        // Get the activation result from the transient
        $result = get_transient('account_activation_result_' . $user_id);
        
        if (!$result) {
            return false;
        }
        
        $result_class = $result['success'] ? 'success' : 'error';
        
        ob_start();
        ?>
        <div class="activation-message <?php echo $result_class; ?>">
            <p><?php echo esc_html($result['message']); ?></p>
            
            <?php if ($result['success']): ?>
            <p class="login-link">
                <a href="#login-tab" class="start-login-button" data-tab="login">
                    <?php _e('Accedi ora', 'document-viewer-plugin'); ?>
                </a>
            </p>
            <?php endif; ?>
        </div>
        <?php
        
        // Add script to handle the "Accedi ora" button click
        ?>
        <script>
            jQuery(document).ready(function($) {
                $('.start-login-button').on('click', function(e) {
                    e.preventDefault();
                    const tabId = $(this).data('tab');
                    $('.login-tabs .tab[data-tab="' + tabId + '"]').click();
                });
            });
        </script>
        <?php
        
        return ob_get_clean();
    }

    /**
     * Visualizza il form di login
     */    private function display_login_form() {
        ?>
        <div class="login-widget-container">
            <h3><?php _e('Accedi al tuo account', 'document-viewer-plugin'); ?></h3>
            
            <!-- Tab per switchare tra login e recupero password -->
            <div class="login-tabs">
                <div class="tab active" data-tab="login"><?php _e('Accedi', 'document-viewer-plugin'); ?></div>
                <div class="tab" data-tab="reset"><?php _e('Recupera Password', 'document-viewer-plugin'); ?></div>
            </div>
            
            <!-- Form di login -->
            <div id="login-tab" class="tab-content active">
                <form id="login-form" class="login-form">
                    <div class="form-group">
                        <label for="username"><?php _e('Email o Username', 'document-viewer-plugin'); ?></label>
                        <input type="text" id="username" name="username" required>
                        <span class="error-message" id="username-error"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="password"><?php _e('Password', 'document-viewer-plugin'); ?></label>
                        <input type="password" id="password" name="password" required>
                        <span class="error-message" id="password-error"></span>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <input type="checkbox" id="remember-me" name="remember_me">
                        <label for="remember-me"><?php _e('Ricordami', 'document-viewer-plugin'); ?></label>
                    </div>
                    
                    <div class="form-submit">
                        <button type="submit" class="submit-button login-button"><?php _e('Accedi', 'document-viewer-plugin'); ?></button>
                    </div>
                    
                    <div class="form-message" style="display:none;"></div>
                </form>
            </div>
            
            <!-- Form per il recupero password -->
            <div id="reset-tab" class="tab-content">
                <form id="reset-form" class="reset-form">
                    <div class="form-group">
                        <label for="reset-email"><?php _e('Email', 'document-viewer-plugin'); ?></label>
                        <input type="email" id="reset-email" name="reset_email" required>
                        <span class="error-message" id="reset-email-error"></span>
                    </div>
                    
                    <div class="form-submit">
                        <button type="submit" class="submit-button reset-button"><?php _e('Recupera Password', 'document-viewer-plugin'); ?></button>
                    </div>
                    
                    <div class="form-message" style="display:none;"></div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Visualizza le informazioni dell'utente loggato
     */    private function display_logged_in_user() {
        // Verifica se è un utente non WordPress (subscriber)
        $is_subscriber = isset($_COOKIE['fa_subscriber_login']);
        $subscriber_data = null;
        
        if ($is_subscriber) {
            try {
                $subscriber_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
            } catch (Exception $e) {
                // In caso di errore, gestisci come utente WordPress
                $is_subscriber = false;
            }
        }
        
        // Se non è un subscriber, ottieni i dati dell'utente WordPress
        $current_user = null;
        if (!$is_subscriber) {
            $current_user = wp_get_current_user();
        }
        ?>
        <div class="login-widget-container">
            <h3><?php _e('Il tuo account', 'document-viewer-plugin'); ?></h3>
            
            <div class="logged-in-user-container">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php 
                        if ($is_subscriber) {
                            // For subscribers, use a standard user icon
                            echo '<svg class="user-avatar-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#4a5568"/>
                                <path d="M12 14.5C6.99 14.5 3 18.49 3 23.5C3 23.78 3.22 24 3.5 24H20.5C20.78 24 21 23.78 21 23.5C21 18.49 17.01 14.5 12 14.5Z" fill="#4a5568"/>
                            </svg>';
                        } else {
                            // For WordPress users, use a standard user icon
                            echo '<svg class="user-avatar-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#4a5568"/>
                                <path d="M12 14.5C6.99 14.5 3 18.49 3 23.5C3 23.78 3.22 24 3.5 24H20.5C20.78 24 21 23.78 21 23.5C21 18.49 17.01 14.5 12 14.5Z" fill="#4a5568"/>
                            </svg>';
                        }
                        ?>
                    </div>
                    <div class="user-details">
                        <div class="user-name">
                            <?php 
                            if ($is_subscriber) {
                                echo esc_html($subscriber_data['name'] . ' ' . $subscriber_data['surname']);
                            } else {
                                echo esc_html($current_user->display_name);
                            }
                            ?>
                        </div>
                        <div class="user-role">
                            <?php
                            if ($is_subscriber) {
                                echo esc_html($subscriber_data['type']);
                            } else if (in_array('administrator', $current_user->roles)) {
                                echo __('Amministratore', 'document-viewer-plugin');
                            } else {
                                // Check if user is a subscriber from the custom table
                                global $wpdb;
                                $subscriber_table = 'wpcd_user_subscription';
                                $subscriber = $wpdb->get_row($wpdb->prepare("SELECT * FROM $subscriber_table WHERE email = %s", $current_user->user_email));
                                
                                if ($subscriber) {
                                    echo esc_html($subscriber->tipo_subscription);
                                } else {
                                    echo __('Utente WordPress', 'document-viewer-plugin');
                                }
                            }
                            ?>
                        </div>
                        <div class="user-email">
                            <?php 
                            if ($is_subscriber) {
                                echo esc_html($subscriber_data['email']);
                            } else {
                                echo esc_html($current_user->user_email);
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="user-actions">
                    <?php if ($is_subscriber): ?>
                        <a href="#" class="logout-button subscriber-logout"><?php _e('Logout', 'document-viewer-plugin'); ?></a>
                        <script>
                        jQuery(document).ready(function($) {
                            $('.subscriber-logout').on('click', function(e) {
                                e.preventDefault();
                                // Elimina il cookie di login
                                document.cookie = 'fa_subscriber_login=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                                // Ricarica la pagina
                                window.location.reload();
                            });
                        });
                        </script>
                    <?php else: ?>
                        <a href="<?php echo wp_logout_url(get_permalink()); ?>" class="logout-button"><?php _e('Logout', 'document-viewer-plugin'); ?></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Handle login for both WordPress users and subscription users
     */
    public function handle_login() {
        // Verify nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
            return;
        }
        
        // Get credentials
        $username = isset($_POST['username']) ? sanitize_text_field($_POST['username']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $remember = isset($_POST['remember']) ? (bool)$_POST['remember'] : false;
        
        if (empty($username) || empty($password)) {
            wp_send_json_error(['message' => __('Username and password are required.', 'document-viewer-plugin')]);
            return;
        }
        
        // STEP 1: First check if it's a subscriber user
        global $wpdb;
        $subscriber_table = 'wpcd_user_subscription';
        
        // Check for email or username
        $is_email = strpos($username, '@') !== false;
        $field = $is_email ? 'email' : 'username';
        
        $subscriber = $wpdb->get_row($wpdb->prepare("SELECT * FROM $subscriber_table WHERE $field = %s", $username));
        
        if ($subscriber) {
            // Verify password
            if (!wp_check_password($password, $subscriber->password, $subscriber->id)) {
                wp_send_json_error(['message' => __('Invalid password for subscriber.', 'document-viewer-plugin')]);
                return;
            }
            
            // Get subscription type to determine redirect URL
            $subscription_type = $subscriber->tipo_subscription;
            $subscription_types_table = $wpdb->prefix . 'type_subscription';
            $redirect_info = $wpdb->get_row($wpdb->prepare("SELECT link_redirect FROM $subscription_types_table WHERE type_sub = %s", $subscription_type));
            
            $redirect_url = '';
            if ($redirect_info && !empty($redirect_info->link_redirect)) {
                $redirect_url = $redirect_info->link_redirect;
            }
            
            // Set a temporary cookie to maintain "subscriber" login state
            $cookie_name = 'fa_subscriber_login';
            $cookie_value = base64_encode(json_encode([
                'id' => $subscriber->id,
                'username' => $subscriber->username,
                'name' => $subscriber->name,
                'surname' => $subscriber->surname,
                'email' => $subscriber->email,
                'type' => $subscription_type
            ]));
            setcookie($cookie_name, $cookie_value, time() + ($remember ? 14 * DAY_IN_SECONDS : DAY_IN_SECONDS), COOKIEPATH, COOKIE_DOMAIN);
            
            // Return success with redirect URL
            wp_send_json_success([
                'message' => __('Login successful as subscriber.', 'document-viewer-plugin'),
                'redirect_url' => !empty($redirect_url) ? $redirect_url : home_url(),
                'user_type' => 'subscriber'
            ]);
            return;
        }
        
        // STEP 2: If not a subscriber, check WordPress user
        $wp_user = null;
        
        if ($is_email) {
            $wp_user = get_user_by('email', $username);
        }
        
        if (!$wp_user) {
            $wp_user = get_user_by('login', $username);
        }
        
        if (!$wp_user) {
            wp_send_json_error(['message' => __('User not found.', 'document-viewer-plugin')]);
            return;
        }
        
        // Check password
        $user = wp_authenticate($wp_user->user_login, $password);
        if (is_wp_error($user)) {
            wp_send_json_error(['message' => $user->get_error_message()]);
            return;
        }
        
        // Log the user in
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, $remember);
        
        // Determine redirect URL based on admin setting
        $global_redirect_url = get_option('login_global_redirect_url', '');
        $redirect_url = !empty($global_redirect_url) ? $global_redirect_url : home_url();
        
        // If user is admin, redirect to admin dashboard
        if (in_array('administrator', $user->roles)) {
            $redirect_url = admin_url();
        }
        
        // Apply filter for third-party customization
        $redirect_url = apply_filters('fa_login_redirect', $redirect_url);
        
        wp_send_json_success([
            'message' => __('Login successful as WordPress user.', 'document-viewer-plugin'),
            'redirect_url' => $redirect_url,
            'user_type' => 'wp_user'
        ]);
    }
    
    /**
     * Handle password reset request
     */
    public function handle_password_reset_request() {
        // Verify nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
            return;
        }
        
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        
        if (empty($email) || !is_email($email)) {
            wp_send_json_error(['message' => __('Please enter a valid email address.', 'document-viewer-plugin')]);
            return;
        }
        
        // First check if it's a subscriber
        global $wpdb;
        $subscriber_table = 'wpcd_user_subscription';
        $subscriber = $wpdb->get_row($wpdb->prepare("SELECT * FROM $subscriber_table WHERE email = %s", $email));
        
        if ($subscriber) {
            // Generate reset token
            $token = wp_generate_password(32, false);
            $token_expiry = time() + 24 * HOUR_IN_SECONDS; // 24 hours
            
            // Store token with subscriber
            $wpdb->update(
                $subscriber_table,
                [
                    'reset_token' => $token,
                    'reset_token_expiry' => $token_expiry
                ],
                ['email' => $email],
                ['%s', '%d'],
                ['%s']
            );
            
            // Send email with reset link
            $reset_url = add_query_arg([
                'action' => 'reset_subscriber_password',
                'email' => urlencode($email),
                'token' => $token
            ], home_url());
            
            $subject = __('Recupero Password', 'document-viewer-plugin');
            $message = sprintf(
                __('Ciao %s, hai richiesto il reset della tua password. Clicca sul link seguente per impostare una nuova password: %s', 'document-viewer-plugin'),
                $subscriber->name,
                $reset_url
            );
            $headers = ['Content-Type: text/html; charset=UTF-8'];
            
            $sent = wp_mail($email, $subject, $message, $headers);
            
            if ($sent) {
                wp_send_json_success(['message' => __('Email per il reset della password inviata. Controlla la tua casella di posta.', 'document-viewer-plugin')]);
            } else {
                wp_send_json_error(['message' => __('Errore nell\'invio dell\'email. Riprova più tardi.', 'document-viewer-plugin')]);
            }
            return;
        }
        
        // If not a subscriber, check if it's a WordPress user
        $user = get_user_by('email', $email);
        
        if ($user) {
            $sent = retrieve_password($email);
            
            if ($sent) {
                wp_send_json_success(['message' => __('Email per il reset della password inviata. Controlla la tua casella di posta.', 'document-viewer-plugin')]);
            } else {
                wp_send_json_error(['message' => __('Errore nell\'invio dell\'email. Riprova più tardi.', 'document-viewer-plugin')]);
            }
        } else {
            wp_send_json_error(['message' => __('Nessun account trovato con questo indirizzo email.', 'document-viewer-plugin')]);
        }
    }

    /**
     * Back-end widget form
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Accesso Utenti', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Titolo:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }

    /**
     * Processa il salvataggio delle opzioni del widget
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}

/**
 * Registra il widget
 */
function register_unified_login_widget() {
    register_widget('Unified_Login_Widget');
}
add_action('widgets_init', 'register_unified_login_widget');

/**
 * Shortcode per il login
 */
function unified_login_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => __('Accesso Utenti', 'document-viewer-plugin'),
        'layout' => 'full', // Opzioni: 'full' o 'compact'
    ), $atts);

    // Inizia l'output del buffer
    ob_start();
    
    // Crea un'istanza del widget
    $widget = new Unified_Login_Widget();
    
    // Ottieni la classe CSS appropriata in base al layout
    $widget_class = ($atts['layout'] === 'compact') ? 'login-widget-compact' : 'document-viewer-widget';
    
    // Visualizza il contenuto del widget (simula il metodo widget)
    echo '<div class="' . $widget_class . ' unified-login-shortcode">';
    
    // Per il layout compatto, aggiungiamo direttamente il titolo
    if ($atts['layout'] === 'compact') {
        if (!empty($atts['title'])) {
            echo '<h3>' . esc_html($atts['title']) . '</h3>';
        }
        echo '<div class="document-form-column" style="width:100%; flex: 1;">';
        $widget->widget(array('before_widget' => '', 'after_widget' => '', 'before_title' => '', 'after_title' => ''), $atts);
        echo '</div>';
    } else {
        // Layout completo - lasciamo che il widget gestisca il titolo
        $widget->widget(
            array(
                'before_widget' => '',
                'after_widget' => '',
                'before_title' => '<h3>',
                'after_title' => '</h3>'
            ),
            array('title' => $atts['title'])
        );
    }
    
    echo '</div>';
    
    // Restituisci il contenuto dal buffer
    return ob_get_clean();
}
add_shortcode('unified_login', 'unified_login_shortcode');
