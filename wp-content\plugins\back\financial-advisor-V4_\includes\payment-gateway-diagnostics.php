<?php
/**
 * Payment Gateway Diagnostics AJAX Handlers
 *
 * Provides AJAX handlers for payment gateway diagnostics and testing
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Add AJAX actions
add_action('wp_ajax_run_payment_gateway_tests', 'payment_gateway_run_tests');
add_action('wp_ajax_run_payment_gateway_diagnostic', 'payment_gateway_run_diagnostic');
add_action('wp_ajax_get_diagnostic_history', 'payment_gateway_get_diagnostic_history');
add_action('wp_ajax_run_endpoint_test', 'payment_gateway_run_endpoint_test');
add_action('wp_ajax_run_transaction_flow_test', 'payment_gateway_run_transaction_flow_test');
add_action('wp_ajax_check_gateway_performance', 'payment_gateway_check_performance');
add_action('wp_ajax_export_diagnostic_report', 'payment_gateway_export_diagnostic');
add_action('wp_ajax_check_gateway_status', 'payment_gateway_check_status');

// Add scheduled event for automatic diagnostic checks
add_action('init', 'register_payment_gateway_cron_events');
add_action('payment_gateway_automatic_diagnostic', 'payment_gateway_automatic_diagnostic');

/**
 * Register scheduled cron events for gateway diagnostics
 */
function register_payment_gateway_cron_events() {
    if (!wp_next_scheduled('payment_gateway_automatic_diagnostic')) {
        // Schedule daily checks at midnight
        wp_schedule_event(strtotime('midnight'), 'daily', 'payment_gateway_automatic_diagnostic');
    }
}

/**
 * Run automatic diagnostic via cron
 */
function payment_gateway_automatic_diagnostic() {
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    // Run diagnostics for both gateways
    $results = $diagnostic->run_gateway_diagnostic();
    
    // Check for critical failures
    $has_critical_failures = false;
    foreach ($results as $gateway => $gateway_results) {
        foreach ($gateway_results as $section_key => $section) {
            if (is_array($section) && isset($section['tests'])) {
                foreach ($section['tests'] as $test) {
                    if (!$test['result'] && isset($test['critical']) && $test['critical']) {
                        $has_critical_failures = true;
                        break 3;
                    }
                }
            }
        }
    }
    
    // Send notification if critical failures found
    if ($has_critical_failures) {
        payment_gateway_send_diagnostic_notification($results);
    }
    
    return $results;
}

/**
 * Send diagnostic notification to admin
 *
 * @param array $results Diagnostic results
 */
function payment_gateway_send_diagnostic_notification($results) {
    $admin_email = get_option('admin_email');
    if (!$admin_email) {
        return;
    }
    
    $subject = sprintf(
        __('[%s] Payment Gateway Diagnostic Alert - Critical Issues Detected', 'document-viewer-plugin'),
        get_bloginfo('name')
    );
    
    $critical_issues = array();
    foreach ($results as $gateway => $gateway_results) {
        foreach ($gateway_results as $section_key => $section) {
            if (is_array($section) && isset($section['tests'])) {
                foreach ($section['tests'] as $test_name => $test) {
                    if (!$test['result'] && isset($test['critical']) && $test['critical']) {
                        $critical_issues[] = sprintf(
                            '%s: %s - %s',
                            ucfirst($gateway),
                            $test['name'],
                            $test['message']
                        );
                    }
                }
            }
        }
    }
    
    $message = sprintf(
        __('The automatic payment gateway diagnostic check has detected critical issues that require attention:') . "\n\n%s\n\n" .
        __('Please visit the Payment Gateway admin page to run a complete diagnostic and resolve these issues.') . "\n\n" .
        __('Admin URL: %s'),
        implode("\n", $critical_issues),
        admin_url('admin.php?page=payment-gateways&tab=testing-tab')
    );
    
    wp_mail($admin_email, $subject, $message);
}

/**
 * Run payment gateway tests via AJAX
 */
function payment_gateway_run_tests() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
    $test_runner = new Payment_Gateway_Test();
    
    $test_results = $test_runner->run_enhanced_payment_gateway_tests();
    
    wp_send_json_success($test_results);
}

/**
 * Run payment gateway diagnostic via AJAX
 */
function payment_gateway_run_diagnostic() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $results = $diagnostic->run_gateway_diagnostic($gateway);
    
    wp_send_json_success($results);
}

/**
 * Get payment gateway diagnostic history via AJAX
 */
function payment_gateway_get_diagnostic_history() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : null;
    $limit = isset($_POST['limit']) ? absint($_POST['limit']) : 10;
    
    $history = $diagnostic->get_diagnostic_history($gateway, $limit);
    
    // Process history items
    $processed_history = array();
    foreach ($history as $item) {
        // Parse the details JSON
        $details = json_decode($item->details, true);
        
        // Count success and failures
        $success_count = $item->success_count;
        $fail_count = $item->fail_count;
        $critical_count = $item->critical_failures;
        
        // Create summary
        $status = ($critical_count > 0) ? 'critical' : (($fail_count > 0) ? 'warning' : 'success');
        
        $processed_history[] = array(
            'id' => $item->id,
            'gateway' => ucfirst($item->gateway),
            'time' => $item->diagnostic_time,
            'success_count' => $success_count,
            'fail_count' => $fail_count,
            'critical_count' => $critical_count,
            'status' => $status,
            'summary' => $item->summary,
            'has_details' => !empty($details)
        );
    }
    
    wp_send_json_success(array(
        'history' => $processed_history,
        'count' => count($history)
    ));
}

/**
 * Run specific endpoint test via AJAX
 */
function payment_gateway_run_endpoint_test() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $endpoint = isset($_POST['endpoint']) ? sanitize_text_field($_POST['endpoint']) : '';
    
    if (empty($gateway) || empty($endpoint)) {
        wp_send_json_error(array('message' => __('Missing gateway or endpoint information', 'document-viewer-plugin')));
        return;
    }
    
    $result = $diagnostic->test_specific_endpoint($gateway, $endpoint);
    
    wp_send_json_success($result);
}

/**
 * Run transaction flow test via AJAX
 */
function payment_gateway_run_transaction_flow_test() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
    $test_runner = new Payment_Gateway_Test();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    
    if ($gateway === 'paypal') {
        $config = $test_runner->get_paypal_config();
        $results = $test_runner->test_paypal_transaction_flow($config);
    } elseif ($gateway === 'stripe') {
        $config = $test_runner->get_stripe_config();
        $results = $test_runner->test_stripe_transaction_flow($config);
    } else {
        wp_send_json_error(array('message' => __('Invalid gateway specified', 'document-viewer-plugin')));
        return;
    }
    
    wp_send_json_success($results);
}

/**
 * Check payment gateway performance via AJAX
 */
function payment_gateway_check_performance() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $test_count = isset($_POST['test_count']) ? absint($_POST['test_count']) : 5;
    
    // Limit test count to reasonable value
    $test_count = min($test_count, 10);
    
    $performance_results = $diagnostic->measure_gateway_performance($gateway, $test_count);
    
    wp_send_json_success($performance_results);
}

/**
 * Export diagnostic report via AJAX
 */
function payment_gateway_export_diagnostic() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'json';
    $diagnostic_id = isset($_POST['diagnostic_id']) ? absint($_POST['diagnostic_id']) : 0;
    
    if ($diagnostic_id > 0) {
        // Export specific diagnostic result
        $single_diagnostic = $diagnostic->get_diagnostic_by_id($diagnostic_id);
        $export_data = $single_diagnostic;
    } else {
        // Run new diagnostic and export
        $results = $diagnostic->run_gateway_diagnostic($gateway);
        $export_data = $results;
    }
    
    // Create export content based on format
    if ($format === 'html') {
        $export_content = $diagnostic->generate_diagnostic_report($export_data);
        $mime_type = 'text/html';
        $file_ext = 'html';
    } else {
        $export_content = wp_json_encode($export_data, JSON_PRETTY_PRINT);
        $mime_type = 'application/json';
        $file_ext = 'json';
    }
    
    $gateway_name = empty($gateway) ? 'all' : $gateway;
    
    wp_send_json_success(array(
        'content' => $export_content,
        'filename' => 'payment_gateway_diagnostic_' . $gateway_name . '_' . date('Y-m-d') . '.' . $file_ext,
        'mime_type' => $mime_type
    ));
}

/**
 * AJAX handler for checking gateway status (real-time monitoring)
 */
function payment_gateway_check_status() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    
    if (!in_array($gateway, array('paypal', 'stripe'))) {
        wp_send_json_error(array('message' => __('Invalid gateway specified', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-diagnostic.php');
    $diagnostic = new Payment_Gateway_Diagnostic();
    
    // Get gateway configuration
    $config = $diagnostic->get_gateway_config($gateway);
    
    if (empty($config)) {
        wp_send_json_error(array(
            'message' => sprintf(__('%s is not configured', 'document-viewer-plugin'), ucfirst($gateway)),
            'status' => 'error'
        ));
        return;
    }
    
    // Perform a quick status check
    $check_result = $diagnostic->quick_status_check($gateway, $config);
    
    if ($check_result['success']) {
        // Get recent activity from logs
        global $wpdb;
        $logs_table = $wpdb->prefix . 'payment_gateway_logs';
        
        $recent_activity = array();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") === $logs_table) {
            $activity_query = $wpdb->prepare(
                "SELECT error_type, error_message, log_level, created_at FROM {$logs_table} 
                WHERE gateway = %s ORDER BY created_at DESC LIMIT 5",
                $gateway
            );
            
            $logs = $wpdb->get_results($activity_query);
            
            if ($logs) {
                foreach ($logs as $log) {
                    $recent_activity[] = array(
                        'time' => $log->created_at,
                        'type' => $log->log_level,
                        'message' => $log->error_message
                    );
                }
            }
        }
        
        wp_send_json_success(array(
            'status' => 'ok',
            'message' => sprintf(__('%s is functioning correctly', 'document-viewer-plugin'), ucfirst($gateway)),
            'response_time' => $check_result['response_time'] ?? 0,
            'recent_activity' => $recent_activity
        ));
    } else {
        wp_send_json_error(array(
            'status' => 'error',
            'message' => $check_result['message'] ?? sprintf(__('%s is not responding correctly', 'document-viewer-plugin'), ucfirst($gateway)),
            'response_time' => $check_result['response_time'] ?? 0
        ));
    }
}
