<?php
/**
 * Office Add-in Performance Monitor
 *
 * This class provides performance monitoring and metrics collection
 * for Office Add-in operations.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class Office_Addin_Performance_Monitor {
    
    /**
     * Performance metrics cache group
     */
    private $cache_group = 'office_addin_performance';
    
    /**
     * Metrics retention period (in seconds)
     */
    private $retention_period = 2592000; // 30 days
    
    /**
     * Performance thresholds (in milliseconds)
     */
    private $thresholds = [
        'api_request' => 5000,       // 5 seconds for API requests
        'database_query' => 1000,    // 1 second for database queries
        'cache_operation' => 100,    // 100ms for cache operations
        'total_request' => 10000     // 10 seconds for total request time
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize object cache group
        wp_cache_add_global_groups([$this->cache_group]);
        
        // Schedule cleanup of old metrics
        if (!wp_next_scheduled('office_addin_performance_cleanup')) {
            wp_schedule_event(time(), 'daily', 'office_addin_performance_cleanup');
        }
        
        add_action('office_addin_performance_cleanup', [$this, 'cleanup_old_metrics']);
    }
    
    /**
     * Start timing a performance metric
     *
     * @param string $metric_name Name of the metric
     * @param array $context Additional context data
     * @return string Timer ID
     */
    public function start_timer($metric_name, $context = []) {
        $timer_id = uniqid('timer_', true);
        
        $timer_data = [
            'metric_name' => sanitize_key($metric_name),
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'context' => $this->sanitize_context($context),
            'timer_id' => $timer_id
        ];
        
        // Store timer data temporarily
        wp_cache_set("timer_{$timer_id}", $timer_data, $this->cache_group, 300); // 5 minutes
        
        return $timer_id;
    }
    
    /**
     * Stop timing and record the metric
     *
     * @param string $timer_id Timer ID from start_timer
     * @param array $additional_context Additional context to add
     * @return array Performance metric data or false on error
     */
    public function stop_timer($timer_id, $additional_context = []) {
        try {
            // Get timer data
            $timer_data = wp_cache_get("timer_{$timer_id}", $this->cache_group);
            
            if (!$timer_data) {
                if (function_exists('dv_debug_log')) {
                    dv_debug_log("Timer not found: {$timer_id}", 'office_addin_performance');
                }
                return false;
            }
            
            $end_time = microtime(true);
            $end_memory = memory_get_usage(true);
            
            $metric_data = [
                'metric_name' => $timer_data['metric_name'],
                'duration_ms' => round(($end_time - $timer_data['start_time']) * 1000, 2),
                'memory_usage_bytes' => $end_memory - $timer_data['start_memory'],
                'timestamp' => time(),
                'context' => array_merge($timer_data['context'], $this->sanitize_context($additional_context)),
                'timer_id' => $timer_id
            ];
            
            // Clean up timer cache
            wp_cache_delete("timer_{$timer_id}", $this->cache_group);
            
            // Store the metric
            $this->store_metric($metric_data);
            
            // Check for performance threshold violations
            $this->check_performance_thresholds($metric_data);
            
            return $metric_data;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Stop timer exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return false;
        }
    }
    
    /**
     * Record a simple metric without timing
     *
     * @param string $metric_name Name of the metric
     * @param float $value Metric value
     * @param string $unit Unit of measurement
     * @param array $context Additional context data
     * @return bool Success status
     */
    public function record_metric($metric_name, $value, $unit = 'count', $context = []) {
        try {
            $metric_data = [
                'metric_name' => sanitize_key($metric_name),
                'value' => floatval($value),
                'unit' => sanitize_key($unit),
                'timestamp' => time(),
                'context' => $this->sanitize_context($context)
            ];
            
            return $this->store_metric($metric_data);
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Record metric exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return false;
        }
    }
    
    /**
     * Get performance metrics
     *
     * @param string $metric_name Filter by metric name
     * @param int $limit Number of metrics to retrieve
     * @param int $hours Look back this many hours
     * @return array Performance metrics
     */
    public function get_metrics($metric_name = null, $limit = 100, $hours = 24) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            // Create table if it doesn't exist
            $this->create_performance_table();
            
            $where_conditions = [];
            $where_values = [];
            
            // Time filter
            $since_timestamp = time() - ($hours * 3600);
            $where_conditions[] = 'timestamp >= %d';
            $where_values[] = $since_timestamp;
            
            if ($metric_name) {
                $where_conditions[] = 'metric_name = %s';
                $where_values[] = $metric_name;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $query = "SELECT * FROM $table_name $where_clause ORDER BY timestamp DESC LIMIT %d";
            $where_values[] = $limit;
            
            $prepared_query = $wpdb->prepare($query, $where_values);
            $metrics = $wpdb->get_results($prepared_query, ARRAY_A);
            
            // Decode context data
            foreach ($metrics as &$metric) {
                $metric['context'] = json_decode($metric['context'], true) ?: [];
                $metric['formatted_time'] = date('Y-m-d H:i:s', $metric['timestamp']);
            }
            
            return $metrics;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get metrics exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return [];
        }
    }
    
    /**
     * Get performance statistics
     *
     * @param int $hours Look back this many hours
     * @return array Performance statistics
     */
    public function get_performance_stats($hours = 24) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            $since_timestamp = time() - ($hours * 3600);
            
            // Average response times by metric
            $avg_times = $wpdb->get_results($wpdb->prepare(
                "SELECT metric_name, 
                        AVG(duration_ms) as avg_duration,
                        MIN(duration_ms) as min_duration,
                        MAX(duration_ms) as max_duration,
                        COUNT(*) as request_count
                 FROM $table_name 
                 WHERE timestamp >= %d AND duration_ms IS NOT NULL
                 GROUP BY metric_name",
                $since_timestamp
            ), ARRAY_A);
            
            // Memory usage statistics
            $memory_stats = $wpdb->get_results($wpdb->prepare(
                "SELECT metric_name,
                        AVG(memory_usage_bytes) as avg_memory,
                        MAX(memory_usage_bytes) as max_memory,
                        COUNT(*) as sample_count
                 FROM $table_name 
                 WHERE timestamp >= %d AND memory_usage_bytes IS NOT NULL
                 GROUP BY metric_name",
                $since_timestamp
            ), ARRAY_A);
            
            // Requests per hour
            $hourly_requests = $wpdb->get_results($wpdb->prepare(
                "SELECT FROM_UNIXTIME(timestamp, '%%Y-%%m-%%d %%H:00:00') as hour,
                        COUNT(*) as request_count
                 FROM $table_name 
                 WHERE timestamp >= %d
                 GROUP BY FROM_UNIXTIME(timestamp, '%%Y-%%m-%%d %%H:00:00')
                 ORDER BY hour DESC",
                $since_timestamp
            ), ARRAY_A);
            
            // Performance threshold violations
            $violations = $this->get_threshold_violations($hours);
            
            return [
                'response_times' => $avg_times,
                'memory_usage' => $memory_stats,
                'hourly_requests' => $hourly_requests,
                'threshold_violations' => $violations,
                'period_hours' => $hours,
                'thresholds' => $this->thresholds
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get performance stats exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return [
                'error' => 'Unable to get performance statistics',
                'response_times' => [],
                'memory_usage' => [],
                'hourly_requests' => [],
                'threshold_violations' => [],
                'period_hours' => $hours,
                'thresholds' => $this->thresholds
            ];
        }
    }
    
    /**
     * Get performance recommendations
     *
     * @param int $hours Analysis period in hours
     * @return array Performance recommendations
     */
    public function get_performance_recommendations($hours = 24) {
        try {
            $stats = $this->get_performance_stats($hours);
            $recommendations = [];
            
            // Analyze response times
            foreach ($stats['response_times'] as $metric) {
                if ($metric['avg_duration'] > $this->thresholds['api_request']) {
                    $recommendations[] = [
                        'type' => 'slow_api_requests',
                        'severity' => 'high',
                        'metric' => $metric['metric_name'],
                        'current_avg' => $metric['avg_duration'],
                        'threshold' => $this->thresholds['api_request'],
                        'suggestion' => 'Consider implementing caching or optimizing API calls for ' . $metric['metric_name']
                    ];
                }
                
                if ($metric['max_duration'] > ($this->thresholds['api_request'] * 2)) {
                    $recommendations[] = [
                        'type' => 'peak_response_time',
                        'severity' => 'medium',
                        'metric' => $metric['metric_name'],
                        'max_duration' => $metric['max_duration'],
                        'suggestion' => 'Peak response time is very high. Check for API timeout issues.'
                    ];
                }
            }
            
            // Analyze memory usage
            foreach ($stats['memory_usage'] as $metric) {
                if ($metric['max_memory'] > (50 * 1024 * 1024)) { // 50MB
                    $recommendations[] = [
                        'type' => 'high_memory_usage',
                        'severity' => 'medium',
                        'metric' => $metric['metric_name'],
                        'max_memory_mb' => round($metric['max_memory'] / 1024 / 1024, 2),
                        'suggestion' => 'High memory usage detected. Consider optimizing data processing.'
                    ];
                }
            }
            
            // Analyze request patterns
            $total_requests = array_sum(array_column($stats['hourly_requests'], 'request_count'));
            $avg_requests_per_hour = $total_requests / max(1, count($stats['hourly_requests']));
            
            if ($avg_requests_per_hour > 100) {
                $recommendations[] = [
                    'type' => 'high_request_volume',
                    'severity' => 'low',
                    'avg_requests_per_hour' => round($avg_requests_per_hour, 2),
                    'suggestion' => 'High request volume. Consider implementing additional rate limiting.'
                ];
            }
            
            // Check threshold violations
            if (count($stats['threshold_violations']) > 0) {
                $recommendations[] = [
                    'type' => 'threshold_violations',
                    'severity' => 'high',
                    'violation_count' => count($stats['threshold_violations']),
                    'suggestion' => 'Multiple performance threshold violations detected. Review system configuration.'
                ];
            }
            
            return $recommendations;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get performance recommendations exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return [
                ['type' => 'error', 'severity' => 'high', 'suggestion' => 'Unable to generate performance recommendations']
            ];
        }
    }
    
    /**
     * Store performance metric in database
     *
     * @param array $metric_data Metric data
     * @return bool Success status
     */
    private function store_metric($metric_data) {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            // Create table if it doesn't exist
            $this->create_performance_table();
            
            $result = $wpdb->insert(
                $table_name,
                [
                    'metric_name' => $metric_data['metric_name'],
                    'duration_ms' => isset($metric_data['duration_ms']) ? $metric_data['duration_ms'] : null,
                    'memory_usage_bytes' => isset($metric_data['memory_usage_bytes']) ? $metric_data['memory_usage_bytes'] : null,
                    'value' => isset($metric_data['value']) ? $metric_data['value'] : null,
                    'unit' => isset($metric_data['unit']) ? $metric_data['unit'] : null,
                    'timestamp' => $metric_data['timestamp'],
                    'context' => wp_json_encode($metric_data['context']),
                    'timer_id' => isset($metric_data['timer_id']) ? $metric_data['timer_id'] : null
                ],
                [
                    '%s', '%f', '%d', '%f', '%s', '%d', '%s', '%s'
                ]
            );
            
            // Limit number of stored metrics
            $this->limit_stored_metrics();
            
            return $result !== false;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Store metric exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return false;
        }
    }
    
    /**
     * Create performance metrics table
     *
     * @return bool Success status
     */
    private function create_performance_table() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE IF NOT EXISTS $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                metric_name varchar(100) NOT NULL,
                duration_ms decimal(10,2) DEFAULT NULL,
                memory_usage_bytes bigint DEFAULT NULL,
                value decimal(15,4) DEFAULT NULL,
                unit varchar(20) DEFAULT NULL,
                timestamp int(11) NOT NULL,
                context longtext,
                timer_id varchar(50) DEFAULT NULL,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY metric_name (metric_name),
                KEY timestamp (timestamp),
                KEY duration_ms (duration_ms)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            return true;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Create performance table exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return false;
        }
    }
    
    /**
     * Check for performance threshold violations
     *
     * @param array $metric_data Metric data
     */
    private function check_performance_thresholds($metric_data) {
        try {
            if (!isset($metric_data['duration_ms'])) {
                return;
            }
            
            $threshold_key = $metric_data['metric_name'];
            $threshold = isset($this->thresholds[$threshold_key]) ? $this->thresholds[$threshold_key] : $this->thresholds['api_request'];
            
            if ($metric_data['duration_ms'] > $threshold) {
                // Log performance violation
                if (function_exists('dv_debug_log')) {
                    dv_debug_log(
                        "Performance threshold violation: {$metric_data['metric_name']} took {$metric_data['duration_ms']}ms (threshold: {$threshold}ms)",
                        'office_addin_performance'
                    );
                }
                
                // Store violation for reporting
                $this->store_threshold_violation($metric_data, $threshold);
            }
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Check performance thresholds exception: ' . $e->getMessage(), 'office_addin_performance');
            }
        }
    }
    
    /**
     * Store threshold violation
     *
     * @param array $metric_data Metric data
     * @param float $threshold Violated threshold
     */
    private function store_threshold_violation($metric_data, $threshold) {
        $violations = get_option('office_addin_threshold_violations', []);
        
        $violation = [
            'metric_name' => $metric_data['metric_name'],
            'duration_ms' => $metric_data['duration_ms'],
            'threshold' => $threshold,
            'timestamp' => $metric_data['timestamp'],
            'context' => $metric_data['context']
        ];
        
        $violations[] = $violation;
        
        // Keep only last 100 violations
        if (count($violations) > 100) {
            $violations = array_slice($violations, -100);
        }
        
        update_option('office_addin_threshold_violations', $violations);
    }
    
    /**
     * Get threshold violations
     *
     * @param int $hours Look back this many hours
     * @return array Threshold violations
     */
    private function get_threshold_violations($hours) {
        $violations = get_option('office_addin_threshold_violations', []);
        $since_timestamp = time() - ($hours * 3600);
        
        return array_filter($violations, function($violation) use ($since_timestamp) {
            return $violation['timestamp'] >= $since_timestamp;
        });
    }
    
    /**
     * Limit number of stored metrics
     */
    private function limit_stored_metrics() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            $max_metrics = 10000; // Keep last 10,000 metrics
            
            if ($count > $max_metrics) {
                $excess = $count - $max_metrics;
                $wpdb->query($wpdb->prepare(
                    "DELETE FROM $table_name ORDER BY timestamp ASC LIMIT %d",
                    $excess
                ));
            }
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Limit stored metrics exception: ' . $e->getMessage(), 'office_addin_performance');
            }
        }
    }
    
    /**
     * Sanitize context data
     *
     * @param array $context Context data
     * @return array Sanitized context
     */
    private function sanitize_context($context) {
        if (!is_array($context)) {
            return [];
        }
        
        $sanitized = [];
        foreach ($context as $key => $value) {
            $key = sanitize_key($key);
            
            if (is_string($value)) {
                $sanitized[$key] = sanitize_text_field($value);
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitize_context($value);
            } elseif (is_scalar($value)) {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Clear old metrics (scheduled task)
     */
    public function cleanup_old_metrics() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'office_addin_performance';
            
            $cutoff_timestamp = time() - $this->retention_period;
            
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM $table_name WHERE timestamp < %d",
                $cutoff_timestamp
            ));
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Scheduled cleanup: deleted {$deleted} old performance metrics", 'office_addin_performance');
            }
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cleanup old metrics exception: ' . $e->getMessage(), 'office_addin_performance');
            }
        }
    }
    
    /**
     * Export performance data
     *
     * @param int $hours Hours of data to export
     * @return array Exported data
     */
    public function export_performance_data($hours = 24) {
        try {
            $metrics = $this->get_metrics(null, 1000, $hours);
            $stats = $this->get_performance_stats($hours);
            $recommendations = $this->get_performance_recommendations($hours);
            
            return [
                'export_timestamp' => time(),
                'export_period_hours' => $hours,
                'metrics' => $metrics,
                'statistics' => $stats,
                'recommendations' => $recommendations,
                'thresholds' => $this->thresholds
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Export performance data exception: ' . $e->getMessage(), 'office_addin_performance');
            }
            return ['error' => 'Failed to export performance data'];
        }
    }
}
