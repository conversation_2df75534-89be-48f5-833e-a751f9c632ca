{"name": "brain/monkey", "description": "Mocking utility for PHP functions and WordPress plugin API", "keywords": ["testing", "test", "mockery", "patchwork", "mock", "mock functions", "runkit", "redefinition", "monkey patching", "interception"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gmazzap.me", "role": "Developer"}], "support": {"issues": "https://github.com/Brain-WP/BrainMonkey/issues", "source": "https://github.com/Brain-WP/BrainMonkey"}, "license": "MIT", "require": {"php": ">=5.6.0", "mockery/mockery": "^1.3.5 || ^1.4.4", "antecedent/patchwork": "^2.1.17"}, "require-dev": {"phpunit/phpunit": "^5.7.26 || ^6.0 || ^7.0 || >=8.0 <8.5.12 || ^8.5.14 || ^9.0", "phpcompatibility/php-compatibility": "^9.3.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.1"}, "autoload": {"psr-4": {"Brain\\Monkey\\": "src/"}, "files": ["inc/api.php"]}, "autoload-dev": {"files": ["vendor/antecedent/patchwork/Patchwork.php"], "psr-4": {"Brain\\Monkey\\Tests\\": "tests/src/", "Brain\\Monkey\\Tests\\Unit\\": "tests/cases/unit/", "Brain\\Monkey\\Tests\\Functional\\": "tests/cases/functional/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "extra": {"branch-alias": {"dev-version/1": "1.x-dev", "dev-master": "2.x-dev"}}, "scripts": {"phpcompat": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcs -ps . --standard=PHPCompatibility --ignore=*/vendor/* --extensions=php --basepath=./ --runtime-set testVersion 5.6-"]}}