<?php
/**
 * Payment Gateway Log Management
 *
 * AJAX handlers for managing payment gateway logs and enhanced logging functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Add AJAX actions
add_action('wp_ajax_get_payment_logs', 'payment_gateway_get_logs');
add_action('wp_ajax_clear_payment_logs', 'payment_gateway_clear_logs');
add_action('wp_ajax_export_payment_logs', 'payment_gateway_export_logs');
add_action('wp_ajax_filter_payment_logs', 'payment_gateway_filter_logs');

/**
 * Get payment gateway logs via AJAX
 */
function payment_gateway_get_logs() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
    $test_runner = new Payment_Gateway_Test();
    
    // Get filters
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : null;
    $log_level = isset($_POST['log_level']) ? sanitize_text_field($_POST['log_level']) : null;
    $limit = isset($_POST['limit']) ? absint($_POST['limit']) : 50;
    
    // Get logs
    $logs = $test_runner->get_payment_logs($limit, $gateway, $log_level);
    
    // Format logs for display
    $formatted_logs = array();
    foreach ($logs as $log) {
        $formatted_logs[] = array(
            'id' => $log->id,
            'gateway' => ucfirst($log->gateway),
            'error_type' => ucfirst(str_replace('_', ' ', $log->error_type)),
            'error_message' => $log->error_message,
            'log_level' => $log->log_level,
            'timestamp' => $log->created_at,
            'user_id' => $log->user_id,
            'has_details' => !empty($log->error_data)
        );
    }
    
    wp_send_json_success(array(
        'logs' => $formatted_logs,
        'count' => count($logs)
    ));
}

/**
 * Clear payment gateway logs via AJAX
 */
function payment_gateway_clear_logs() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
    $test_runner = new Payment_Gateway_Test();
    
    if ($test_runner->clear_payment_logs()) {
        wp_send_json_success(array('message' => __('Logs cleared successfully', 'document-viewer-plugin')));
    } else {
        wp_send_json_error(array('message' => __('Failed to clear logs', 'document-viewer-plugin')));
    }
}

/**
 * Export payment gateway logs
 */
function payment_gateway_export_logs() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'json';
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $log_level = isset($_POST['log_level']) ? sanitize_text_field($_POST['log_level']) : '';
    $date_from = isset($_POST['date_from']) ? sanitize_text_field($_POST['date_from']) : '';
    $date_to = isset($_POST['date_to']) ? sanitize_text_field($_POST['date_to']) : '';
    
    require_once(dirname(__FILE__) . '/class-payment-gateway-test.php');
    $test_runner = new Payment_Gateway_Test();
    
    // Get filtered logs
    $logs = $test_runner->get_payment_logs(
        $gateway, 
        $log_level, 
        1000, // Export up to 1000 logs
        $date_from,
        $date_to
    );
    
    if (empty($logs)) {
        wp_send_json_error(array('message' => __('No logs found matching the criteria', 'document-viewer-plugin')));
        return;
    }
    
    // Format the logs according to requested format
    if ($format === 'csv') {
        $csv_data = "ID,Gateway,Type,Message,Level,Date\n";
        
        foreach ($logs as $log) {
            // Sanitize values for CSV
            $message = str_replace('"', '""', $log->error_message);
            $csv_data .= sprintf(
                "%d,%s,%s,\"%s\",%s,%s\n",
                $log->id,
                $log->gateway,
                $log->error_type,
                $message,
                $log->log_level,
                $log->created_at
            );
        }
        
        $export_data = $csv_data;
    } else {
        // Default to JSON format
        $export_data = wp_json_encode($logs, JSON_PRETTY_PRINT);
    }
    
    $filename = 'payment_gateway_logs_' . date('Y-m-d_H-i-s') . '.' . $format;
    
    wp_send_json_success(array(
        'data' => $export_data,
        'filename' => $filename,
        'format' => $format
    ));
}

/**
 * Filter payment gateway logs via AJAX with advanced filtering options
 */
function payment_gateway_filter_logs() {
    check_ajax_referer('payment_gateway_testing', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
        return;
    }
    
    // Basic filters
    $gateway = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';
    $log_level = isset($_POST['log_level']) ? sanitize_text_field($_POST['log_level']) : '';
    $date_from = isset($_POST['date_from']) ? sanitize_text_field($_POST['date_from']) : '';
    $date_to = isset($_POST['date_to']) ? sanitize_text_field($_POST['date_to']) : '';
    $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;
    
    // Advanced filters
    $error_type = isset($_POST['error_type']) ? sanitize_text_field($_POST['error_type']) : '';
    $search_term = isset($_POST['search_term']) ? sanitize_text_field($_POST['search_term']) : '';
    $sort_by = isset($_POST['sort_by']) ? sanitize_text_field($_POST['sort_by']) : 'created_at';
    $sort_order = isset($_POST['sort_order']) ? sanitize_text_field($_POST['sort_order']) : 'DESC';
    
    // Set up query parameters
    global $wpdb;
    $logs_table = $wpdb->prefix . 'payment_gateway_logs';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
        wp_send_json_success(array(
            'logs' => array(),
            'message' => __('Logs table not found', 'document-viewer-plugin'),
            'count' => 0
        ));
        return;
    }
    
    // Start building query
    $query = "SELECT * FROM {$logs_table} WHERE 1=1";
    $where_conditions = array();
    
    // Apply filters
    if (!empty($gateway)) {
        $where_conditions[] = $wpdb->prepare("gateway = %s", $gateway);
    }
    
    if (!empty($log_level)) {
        $where_conditions[] = $wpdb->prepare("log_level = %s", $log_level);
    }
    
    if (!empty($error_type)) {
        $where_conditions[] = $wpdb->prepare("error_type = %s", $error_type);
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = $wpdb->prepare("created_at >= %s", $date_from . ' 00:00:00');
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = $wpdb->prepare("created_at <= %s", $date_to . ' 23:59:59');
    }
    
    // Search term can match gateway, error_type, error_message
    if (!empty($search_term)) {
        $search_condition = $wpdb->prepare(
            "(gateway LIKE %s OR error_type LIKE %s OR error_message LIKE %s)",
            '%' . $wpdb->esc_like($search_term) . '%',
            '%' . $wpdb->esc_like($search_term) . '%',
            '%' . $wpdb->esc_like($search_term) . '%'
        );
        $where_conditions[] = $search_condition;
    }
    
    // Add WHERE clauses
    if (!empty($where_conditions)) {
        $query .= " AND " . implode(" AND ", $where_conditions);
    }
    
    // Validate and sanitize sort parameters
    $allowed_sort_columns = array('id', 'gateway', 'error_type', 'log_level', 'created_at');
    if (!in_array($sort_by, $allowed_sort_columns)) {
        $sort_by = 'created_at';
    }
    
    $sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';
    
    // Add ORDER BY
    $query .= " ORDER BY {$sort_by} {$sort_order}";
    
    // Add LIMIT
    if ($limit > 0) {
        $query .= $wpdb->prepare(" LIMIT %d", $limit);
    }
    
    // Run the query
    $logs = $wpdb->get_results($query);
    
    // Check if any logs were found
    if (empty($logs)) {
        wp_send_json_success(array(
            'logs' => array(),
            'message' => __('No logs found matching the criteria', 'document-viewer-plugin'),
            'count' => 0,
            'filter_applied' => true,
            'query_details' => array(
                'filters' => array(
                    'gateway' => $gateway,
                    'log_level' => $log_level,
                    'error_type' => $error_type,
                    'date_from' => $date_from,
                    'date_to' => $date_to,
                    'search_term' => $search_term
                ),
                'sort' => array(
                    'sort_by' => $sort_by,
                    'sort_order' => $sort_order
                )
            )
        ));
        return;
    }
    
    // Get total count without limit for pagination
    $count_query = "SELECT COUNT(*) FROM {$logs_table} WHERE 1=1";
    if (!empty($where_conditions)) {
        $count_query .= " AND " . implode(" AND ", $where_conditions);
    }
    $total_count = $wpdb->get_var($count_query);
    
    // Prepare logs for display with enhanced details
    $formatted_logs = array();
    foreach ($logs as $log) {
        // Check if error_data is JSON and decode it
        $error_data = null;
        if (!empty($log->error_data)) {
            $error_data = json_decode($log->error_data, true);
        }
        
        $formatted_logs[] = array(
            'id' => $log->id,
            'gateway' => $log->gateway,
            'type' => $log->error_type,
            'message' => $log->error_message,
            'level' => $log->log_level,
            'data' => $error_data,
            'date' => $log->created_at,
            'user_id' => $log->user_id,
            'ip' => $log->ip_address,
            'ua' => $log->user_agent
        );
    }
    
    wp_send_json_success(array(
        'logs' => $formatted_logs,
        'count' => count($formatted_logs),
        'filter_applied' => true
    ));
}

/**
 * Enhanced payment gateway logging function
 * 
 * @param string $gateway The gateway type (paypal, stripe)
 * @param string $error_type The type of error or event
 * @param string $error_message The error message
 * @param mixed $error_data Additional error data (array will be JSON encoded)
 * @param string $log_level The log level (info, warning, error)
 * @return bool|int False on failure, log ID on success
 */
function payment_gateway_log($gateway, $error_type, $error_message, $error_data = null, $log_level = 'info') {
    global $wpdb;
    $logs_table = $wpdb->prefix . 'payment_gateway_logs';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
        // Try to create the table if it doesn't exist
        if (file_exists(dirname(dirname(__FILE__)) . '/create-tables.php')) {
            require_once(dirname(dirname(__FILE__)) . '/create-tables.php');
            create_payment_gateway_logs_table();
            
            // Check again if table was created
            if ($wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") !== $logs_table) {
                return false;
            }
        } else {
            return false;
        }
    }
    
    // Prepare error data for storage
    if (is_array($error_data) || is_object($error_data)) {
        $error_data = wp_json_encode($error_data);
    }
    
    // Validate log level
    $valid_log_levels = array('info', 'warning', 'error', 'critical', 'debug');
    if (!in_array($log_level, $valid_log_levels)) {
        $log_level = 'info';
    }
    
    // Get current user ID if available
    $user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
    
    // Insert log entry
    $result = $wpdb->insert(
        $logs_table,
        array(
            'gateway' => $gateway,
            'error_type' => $error_type,
            'error_message' => $error_message,
            'error_data' => $error_data,
            'log_level' => $log_level,
            'user_id' => $user_id,
            'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s')
    );
    
    if ($result !== false) {
        // Return the ID of the inserted log
        return $wpdb->insert_id;
    }
    
    return false;
}
