/**
 * Document Stats JavaScript
 *
 * Gestisce le funzionalità per la visualizzazione delle statistiche utente
 * e delle analisi, inclusa l'interazione con le sezioni espandibili e
 * l'aggiornamento delle informazioni.
 */

jQuery(document).ready(function($) {
    // Debug log per tracciare inizializzazione
    console.log('Document stats JS inizializzato');

    // Variabile per il calcolo del costo (sarà aggiornata con il valore dal server)
    var COST_RATE = 0.01; // Valore predefinito, verrà aggiornato se disponibile
    var USER_SUBSCRIPTION_TYPE = 'standard'; // Tipo di abbonamento dell'utente, verrà aggiornato se disponibile

    /**
     * Resetta le statistiche quando il widget viene caricato
     */
    function resetStatsOnWidgetLoad() {
        console.log('Reset statistiche al caricamento del widget');

        // Effettua la chiamata AJAX per resettare le statistiche
        $.ajax({
            url: document_stats_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'reset_stats_on_widget_load',
                nonce: document_stats_ajax_object.nonce
            },
            success: function(response) {
                if (response && response.success) {
                    console.log('Statistiche resettate con successo al caricamento del widget');
                    // Aggiorna l'interfaccia per riflettere i valori resettati
                    if ($('#actual-cost').length) {
                        $('#actual-cost').text('€0,00');
                    }
                    // Non resetta più la spesa totale - mantenuta nel database per statistiche
                    // if ($('#tot-cost').length) {
                    //     $('#tot-cost').text('€0,00');
                    // }
                } else {
                    var errorMsg = (response && response.data && response.data.message) ? response.data.message : 'Errore sconosciuto nel reset delle statistiche.';
                    console.warn('Errore nel reset delle statistiche:', errorMsg);
                    // Mostra errore visibile nella UI
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-error">' +
                        'Errore nel reset delle statistiche: ' + errorMsg +
                        '<br><button class="refresh-stats-btn" id="retry-reset-btn">Riprova</button>' +
                        '</div>'
                    );
                    // Gestore per il pulsante riprova
                    $('#retry-reset-btn').on('click', function() {
                        resetStatsOnWidgetLoad();
                    });
                }
            },
            error: function(xhr, status, error) {
                var errorMsg = 'Errore di connessione: impossibile resettare le statistiche. (' + status + ')';
                console.error('Errore nella richiesta di reset statistiche:', status, error);
                // Mostra errore visibile nella UI
                $('#user-stats-container .stats-section-content').html(
                    '<div class="stats-error">' +
                    errorMsg +
                    '<br><button class="refresh-stats-btn" id="retry-reset-btn">Riprova</button>' +
                    '</div>'
                );
                // Gestore per il pulsante riprova
                $('#retry-reset-btn').on('click', function() {
                    resetStatsOnWidgetLoad();
                });
            }
        });
    }

    // Esegui il reset delle statistiche al caricamento della pagina/widget
    resetStatsOnWidgetLoad();

    // Variabile globale per l'accesso esterno alle funzioni di stats
    window.documentStats = {
        // Memorizza il token count corrente per uso futuro
        currentTokenCount: 0,

        // Funzione per il ripristino forzato delle statistiche in caso di errori persistenti
        forceResetStats: function() {
            console.log('Tentativo di ripristino forzato delle statistiche');

            // Usa gli oggetti AJAX disponibili
            var ajaxUrl = (window.documentViewerParams && window.documentViewerParams.ajaxUrl) ?
                            window.documentViewerParams.ajaxUrl :
                           (window.document_stats_ajax_object && window.document_stats_ajax_object.ajax_url ?
                             window.document_stats_ajax_object.ajax_url : ajaxurl);

            var nonce = (window.documentViewerParams && window.documentViewerParams.nonce) ?
                        window.documentViewerParams.nonce :
                       (window.document_stats_ajax_object && window.document_stats_ajax_object.nonce ?
                         window.document_stats_ajax_object.nonce : '');

            // Mostra messaggio di caricamento
            $('#user-stats-container .stats-section-content').html(
                '<div class="stats-loading">' +
                '<div class="stats-spinner"></div>' +
                'Ripristino forzato delle statistiche in corso...' +
                '</div>'
            );

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'force_reset_stats',
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Statistiche ripristinate con successo');
                        $('#user-stats-container .stats-section-content').html(
                            '<div class="stats-success">' +
                            'Statistiche ripristinate con successo. Ricaricamento in corso...' +
                            '</div>'
                        );
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        console.error('Errore nel ripristino delle statistiche:', response.data.message);
                        $('#user-stats-container .stats-section-content').html(
                            '<div class="stats-error">' +
                            'Errore nel ripristino delle statistiche: ' + (response.data.message || 'Errore sconosciuto') +
                            '<br><button class="refresh-stats-btn" id="retry-reset-full-btn">Riprova</button>' +
                            '</div>'
                        );
                        $('#retry-reset-full-btn').on('click', function() {
                            window.documentStats.forceResetStats();
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Errore AJAX nel ripristino delle statistiche:', status, error);
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-error">' +
                        'Errore di connessione: impossibile ripristinare le statistiche. (' + status + ')' +
                        '<br><button class="refresh-stats-btn" id="retry-reset-full-btn">Riprova</button>' +
                        '</div>'
                    );
                    $('#retry-reset-full-btn').on('click', function() {
                        window.documentStats.forceResetStats();
                    });
                }
            });
        },

        refreshStats: function() {
            console.log('Richiesta aggiornamento statistiche');
            loadStatistics(true, true); // secondo parametro indica refreshAfterAnalysis
        },

        // Esporta la funzione di calcolo costo per uso da altri script
        calculateCost: function(tokenCount) {
            return calculateEstimatedCost(tokenCount);
        },

        // Funzione per aggiornare la spesa stimata
        updateCostEstimate: function(tokenCount) {
            console.log('Aggiornamento spesa stimata per', tokenCount, 'token');

            // Se il tokenCount non è valido, imposta a 0,00
            if (!tokenCount || isNaN(tokenCount) || tokenCount <= 0) {
                console.log('Token count non valido o zero, imposto a 0,00');
                const $costElements = $('#cost-estimate, #estimated-cost');
                $costElements
                    .text('€0,00')
                    .removeClass('hidden')
                    .removeClass('cost-updated')
                    .css({
                        'display': 'inline-block',
                        'opacity': '1',
                        'visibility': 'visible'
                    });
                return;
            }

            // Memorizza il token count per uso futuro
            this.currentTokenCount = tokenCount;

            // Calcola la spesa stimata
            const estimatedCost = calculateEstimatedCost(tokenCount);

            console.log('Nuova spesa stimata calcolata:', estimatedCost, 'per', tokenCount, 'token');

            // Aggiorna gli elementi della spesa stimata
            const $costElements = $('#cost-estimate, #estimated-cost');

            // Forza la visualizzazione e aggiorna il testo
            $costElements
                .css({
                    'display': 'inline-block',
                    'opacity': '1',
                    'visibility': 'visible'
                })
                .text('€' + estimatedCost)
                .addClass('cost-updated');

            // Log per debug
            console.log('Elementi aggiornati:', $costElements.length, 'testo impostato a:', '€' + estimatedCost);

            // Rimuovi l'evidenziazione dopo 3 secondi
            setTimeout(() => {
                $costElements.removeClass('cost-updated');
            }, 3000);

            console.log('Spesa stimata aggiornata a €' + estimatedCost);
        },

        // Funzione: copia la spesa stimata in effettiva e aggiorna i contatori
        updateActualCostAfterAnalysis: function(tokenCount, analysisTitle = 'Analisi documento') {
            console.log('Aggiornamento spesa effettiva e contatori dopo analisi');

            // Usa il token count fornito o quello salvato
            const finalTokenCount = tokenCount || this.currentTokenCount;
            if (!finalTokenCount) {
                console.error('Token count non disponibile per aggiornare la spesa effettiva');
                return;
            }

            // Recupera la spesa stimata dal cookie o calcola nuovamente
            let estimatedCost = '';
            const cookieValue = getCookieValue('fa_estimated_cost');
            if (cookieValue) {
                estimatedCost = cookieValue;
                console.log('Costo stimato recuperato dal cookie:', estimatedCost);
            } else {
                estimatedCost = calculateEstimatedCost(finalTokenCount);
                console.log('Costo stimato calcolato:', estimatedCost, 'per', finalTokenCount, 'token');
            }

            // Genera un ID univoco per l'analisi
            const analysisId = Date.now();

            // Usa il costo stimato per impostare il costo effettivo
            updateActualCost(estimatedCost, finalTokenCount, analysisId, analysisTitle);
        },
        // Nuova funzione: aggiorna solo la sezione delle analisi recenti
        updateRecentAnalysisSection: function() {
            console.log('Aggiornamento sezione analisi recenti');
            fetchRecentAnalyses();
        },
        // Pulizia dati utente al logout
        clearUserData: function() {
            console.log('Pulizia dati utente al logout');
            $('#recent-analyses-list').html('<p class="no-analyses">Nessuna analisi effettuata.</p>');
            $('#analyses-count').text('0');
            $('#tokens-count').text('0');
            $('#cost-estimate').text('€0,00');
            $('#actual-cost').text('€0,00');
            $('#tot-cost').text('€0,00');
            $('#credits-available').text('€0,00');
            $('#first-analysis-date, #last-analysis-date').text('-');
        },
        // Nuova funzione per resettare la spesa effettiva quando si carica un nuovo documento
        clearActualCost: function() {
            console.log('Reset spesa effettiva dopo caricamento nuovo documento');
            if ($('#actual-cost').length) {
                $('#actual-cost').text('€0,00');
                // Evidenzia il cambiamento
                $('#actual-cost').addClass('cost-updated');
                setTimeout(() => {
                    $('#actual-cost').removeClass('cost-updated');
                }, 3000);
            }
        },
        // Nuova funzione per effetto blink sui campi token e menu domande preset
        applyBlinkEffectOnLoad: function() {
            console.log('Applicazione effetto blink sui campi token e menu domande preset dopo caricamento documento');

            // Effetto blink sul campo token con selettore più specifico e debug
            const tokenElem = $('#document-tokens-inline');
            if (tokenElem.length) {
                console.log('Campo token trovato, applico effetto blink');
                tokenElem.addClass('blink-effect');
                setTimeout(() => {
                    tokenElem.removeClass('blink-effect');
                }, 3000);
            } else {
                console.log('Campo token non trovato nel DOM');
            }

            // Effetto blink sul menu domande preset con selettore più specifico e debug
            const queryElem = $('.preset-queries-select');
            if (queryElem.length) {
                console.log('Menu domande preset trovato, applico effetto blink');
                queryElem.addClass('blink-effect');
                setTimeout(() => {
                    queryElem.removeClass('blink-effect');
                }, 3000);
            } else {
                console.log('Menu domande preset non trovato nel DOM');
            }
        }
    };

    // Funzione di calcolo costo stimato basata sul numero di token
    function calculateEstimatedCost(tokenCount) {
        if (!tokenCount || isNaN(tokenCount)) return '0,00';

        // Verifica che COST_RATE sia un numero valido, altrimenti usa il valore predefinito
        const effectiveCostRate = (typeof COST_RATE === 'number' && !isNaN(COST_RATE) && COST_RATE > 0)
            ? COST_RATE
            : 0.01;

        // Calcolo del costo stimato
        const estimatedCost = tokenCount * effectiveCostRate;

        console.log('Calcolo costo stimato:', {
            tokenCount: tokenCount,
            costRate: effectiveCostRate,
            subscriptionType: USER_SUBSCRIPTION_TYPE,
            estimatedCost: estimatedCost,
            formattedCost: estimatedCost.toFixed(2).replace('.', ',')
        });

        // Formatta il costo con 2 decimali
        return estimatedCost.toFixed(2).replace('.', ',');
    }

    // Rendi la funzione accessibile globalmente per il debug
    window.calculateEstimatedCost = calculateEstimatedCost;

    /**
     * Aggiorna la spesa effettiva e incrementa i contatori nel database
     *
     * @param {string} cost - Il costo stimato formattato
     * @param {number} tokenCount - Il numero di token utilizzati
     * @param {number} analysisId - ID univoco dell'analisi
     * @param {string} analysisTitle - Titolo dell'analisi
     */
    function updateActualCost(cost, tokenCount, analysisId, analysisTitle) {
        console.log('Aggiornamento spesa effettiva con valore:', cost, 'per', tokenCount, 'token');

        // Validazione più rigorosa dei parametri
        if (!cost || cost === '0,00' || cost === '0.00' || cost === '€0,00' || cost === '€0.00') {
            console.error('Aggiornamento spesa effettiva annullato: costo zero o non valido:', cost);
            return;
        }

        if (!tokenCount || isNaN(tokenCount) || tokenCount <= 0) {
            console.error('Aggiornamento spesa effettiva annullato: token non valido:', tokenCount);
            return;
        }

        if (!analysisId) {
            analysisId = Date.now();
            console.warn('ID analisi non specificato, generato automaticamente:', analysisId);
        }

        if (!analysisTitle) {
            analysisTitle = 'Analisi documento';
            console.warn('Titolo analisi non specificato, utilizzato valore di default:', analysisTitle);
        }

        // Rimuovi il simbolo dell'euro e converti le virgole in punti per il backend
        const cleanCost = cost.replace('€', '').replace(',', '.');

        // Aggiorna la UI immediatamente per feedback visivo
        $('#actual-cost').text('€' + cost);

        // Evidenzia il cambiamento
        $('#actual-cost').addClass('cost-updated');
        setTimeout(() => {
            $('#actual-cost').removeClass('cost-updated');
        }, 3000);

        // Aggiorna anche il costo totale nella UI
        updateTotalCost(cost);

        // Effettua la chiamata AJAX per aggiornare i contatori nel database
        const ajaxData = {
            action: 'update_actual_cost_after_analysis',
            nonce: documentViewerParams.nonce,
            cost: cleanCost,
            tokens: tokenCount,
            analysis_id: analysisId,
            analysis_title: analysisTitle
        };

        console.log('AJAX: Invio aggiornamento costi al server:', ajaxData);

        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: ajaxData,
            success: function(response) {
                if (response.success) {
                    console.log('Spesa effettiva aggiornata con successo nel database', response.data);

                    // Aggiorna i contatori nella UI con i valori restituiti dal server
                    if (response.data) {
                        if (response.data.tokens_used) {
                            $('#tokens-count').text(formatNumber(response.data.tokens_used));
                            console.log('UI aggiornata - tokens:', response.data.tokens_used);
                        }
                        if (response.data.tot_cost) {
                            $('#tot-cost').text('€' + response.data.tot_cost);
                            console.log('UI aggiornata - costo totale:', response.data.tot_cost);
                        }
                        if (response.data.credits_available) {
                            // Usa la funzione specifica per aggiornare i crediti disponibili
                            updateAvailableCredits(response.data.credits_available);
                            console.log('UI aggiornata - credito disponibile per utente:', response.data.credits_available);
                        }
                        if (response.data.analysis_count) {
                            $('#analyses-count').text(response.data.analysis_count);
                            console.log('UI aggiornata - numero analisi:', response.data.analysis_count);
                        }
                    }

                    // Aggiorna la sezione delle analisi recenti
                    fetchRecentAnalyses();

                    // Incrementa il contatore delle analisi
                    const currentCount = parseInt($('#analyses-count').text()) || 0;
                    $('#analyses-count').text(currentCount + 1);

                    // Aggiorna la data dell'ultima analisi
                    const now = new Date();
                    const formattedDate = now.getDate().toString().padStart(2, '0') + '/' +
                                        (now.getMonth() + 1).toString().padStart(2, '0') + '/' +
                                        now.getFullYear() + ' ' +
                                        now.getHours().toString().padStart(2, '0') + ':' +
                                        now.getMinutes().toString().padStart(2, '0');
                    $('#last-analysis-date').text(formattedDate);

                    // Se è la prima analisi, imposta anche la data della prima analisi
                    if (currentCount === 0) {
                        $('#first-analysis-date').text(formattedDate);
                    }

                    // Azzera la spesa stimata nel cookie dopo aver aggiornato la spesa effettiva
                    document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
                } else {
                    console.error('Errore nell\'aggiornamento della spesa effettiva:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Errore nella richiesta di aggiornamento spesa effettiva:', error);

                // For non-WordPress users, handle the error gracefully by updating the UI with local values
                if (window.externalUserInfo && window.externalUserInfo.id) {
                    console.log('Errore per utente esterno, aggiornamento UI con valori locali');

                    // Update analyses count
                    const currentCount = parseInt($('#analyses-count').text()) || 0;
                    $('#analyses-count').text(currentCount + 1);

                    // Update tokens count
                    if ($('#tokens-count').length && tokenCount) {
                        const currentTokens = parseInt($('#tokens-count').text().replace(/[^\d]/g, '')) || 0;
                        $('#tokens-count').text(formatNumber(currentTokens + tokenCount));
                    }

                    // Save stats in localStorage for external users as a fallback
                    try {
                        let statsData = {};
                        if (localStorage.getItem('fa_external_user_stats')) {
                            statsData = JSON.parse(localStorage.getItem('fa_external_user_stats'));
                        }

                        // Update the stats with the new values
                        statsData.analysis_count = (parseInt(statsData.analysis_count) || 0) + 1;
                        statsData.tokens_used = (parseInt(statsData.tokens_used) || 0) + tokenCount;
                        statsData.tot_cost = (parseFloat(statsData.tot_cost.toString().replace(',', '.')) || 0) + parseFloat(cleanCost);
                        statsData.actual_cost = cleanCost;

                        localStorage.setItem('fa_external_user_stats', JSON.stringify(statsData));
                        console.log('Statistiche salvate in localStorage per utente esterno:', statsData);
                    } catch (e) {
                        console.error('Errore nel salvataggio statistiche in localStorage:', e);
                    }
                }
            }
        });
    }

    /**
     * Funzione per aggiornare il costo totale della sessione
     *
     * @param {string} cost - Il costo da aggiungere al totale, nel formato italiano (con virgola)
     */
    function updateTotalCost(cost) {
        if (!$('#tot-cost').length) return;

        // Converti il costo da formato italiano (virgola) a formato numerico (punto)
        const numericCost = parseFloat(cost.replace(',', '.'));

        // Leggi il valore attuale del costo totale
        const currentTotalCostText = $('#tot-cost').text().replace('€', '').trim();
        const currentTotalCost = parseFloat(currentTotalCostText.replace(',', '.')) || 0;

        // Calcola il nuovo costo totale
        const newTotalCost = currentTotalCost + numericCost;

        // Formatta il nuovo costo totale con 2 decimali
        const formattedTotalCost = newTotalCost.toFixed(2).replace('.', ',');

        // Aggiorna il display
        $('#tot-cost').text('€' + formattedTotalCost);

        // Evidenzia il cambiamento
        $('#tot-cost').addClass('cost-updated');
        setTimeout(() => {
            $('#tot-cost').removeClass('cost-updated');
        }, 3000);

        console.log('Costo totale aggiornato:', formattedTotalCost, '(+' + cost + ')');
    }

    // Verifica se siamo in una pagina di logout e pulisci i cookie se necessario
    function checkForLogoutAndClean() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('loggedout') && urlParams.get('loggedout') === 'true') {
            // Rimuovi i cookie di sessione
            document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
            document.cookie = 'fa_actual_cost=; path=/; max-age=0';
        }
    }

    // Debug: gestore eventi per document_loaded
    $(document).on('document_loaded', function(e, data) {
        console.log('Evento document_loaded ricevuto con dati:', data);
    });

    // Inizializzazione con caricamento dei dati
    initializeStats();

    // Verifica lo stato di logout al caricamento della pagina
    checkForLogoutAndClean();

    // Aggiungi listener per l'evento di analisi documento completata
    $(document).on('document_analysis_completed', function(e, data) {
        console.log('Analisi documento completata, aggiornamento statistiche');

        // Aggiorna la spesa effettiva e i contatori usando la spesa stimata
        if (data && data.tokenCount) {
            window.documentStats.updateActualCostAfterAnalysis(data.tokenCount, data.title || 'Analisi documento');
        } else {
            // Se non abbiamo il token count dai dati dell'evento, usa quello memorizzato
            window.documentStats.updateActualCostAfterAnalysis(null, data && data.title ? data.title : 'Analisi documento');
        }

        // Forza un aggiornamento completo delle statistiche
        loadStatistics(true, true);
    });

    // Aggiungi listener per l'evento di salvataggio analisi documento
    $(document).on('document_analysis_saved', function(event, data) {
        console.log('Evento document_analysis_saved rilevato');
        // Rimosso aggiornamento automatico delle statistiche
    });

    /**
     * Inizializza le statistiche e imposta i gestori eventi
     */
    function initializeStats() {
        // Primo caricamento dei dati statistici
        loadStatistics();

        // Imposta i gestori per l'apertura/chiusura delle sezioni
        setupToggleSections();

        // Imposta tooltip per le icone info
        setupTooltips();
    }

    /**
     * Imposta i gestori di eventi per le sezioni espandibili
     */
    function setupToggleSections() {
        // Funzione disabilitata: nessun toggle per le sezioni stats/analisi recenti
        // Le sezioni rimangono sempre espanse
        $('.stats-section').each(function() {
            const sectionId = $(this).attr('id');
            if (sectionId) {
                const isExpanded = sessionStorage.getItem('stats_section_' + sectionId) === '1';
                const $content = $(this).find('.stats-section-content');
                const $icon = $(this).find('.toggle-icon');

                if (!isExpanded) {
                    $content.hide();
                } else {
                    $icon.addClass('expanded');
                }
            }
        });
    }

    /**
     * Configura i tooltip informativi
     */
    function setupTooltips() {
        $('.stats-info-icon').hover(
            function() {
                const $tooltip = $(this).next('.stats-tooltip');
                $tooltip.css('opacity', 1).css('visibility', 'visible');
            },
            function() {
                const $tooltip = $(this).next('.stats-tooltip');
                $tooltip.css('opacity', 0).css('visibility', 'hidden');
            }
        );
    }

    /**
     * Carica i dati statistici tramite AJAX
     *
     * @param {boolean} showLoading - Se mostrare l'indicatore di caricamento
     * @param {boolean} isAfterAnalysis - Se l'aggiornamento è dopo un'analisi completata
     */
    function loadStatistics(showLoading = false, isAfterAnalysis = false) {
        // Se l'utente non è loggato, non caricare nulla
        if ($('#stats-column').hasClass('not-logged-in')) {
            console.log('Utente non loggato, statistiche non caricate');
            return;
        }

        console.log('Inizio caricamento statistiche... (dopo analisi: ' + isAfterAnalysis + ')');

        // Variabile per tracciare un timeout di sicurezza
        let safetyTimeout;

        // Mostra loading state se richiesto
        if (showLoading) {
            // Mantieni la struttura originale del container
            if ($('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').length) {
                $('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').html(
                    '<div class="stats-loading">' +
                    '<div class="stats-spinner"></div>' +
                    'Aggiornamento statistiche in corso...' +
                    '</div>'
                );
            } else {
                // Fallback
                $('#user-stats-container .stats-section-content').html(
                    '<div class="stats-loading">' +
                    '<div class="stats-spinner"></div>' +
                    'Caricamento statistiche in corso...' +
                    '</div>'
                );
            }

            // Timeout di sicurezza per rimuovere il messaggio di caricamento se qualcosa va storto
            safetyTimeout = setTimeout(function() {
                console.log('Timeout di sicurezza attivato: ripristino interfaccia statistiche');
                // Ricarica statistiche senza mostrare l'indicatore
                tryRestoreStatsUI();
            }, 10000); // 10 secondi di timeout
        }

        // Effettua la richiesta AJAX per recuperare i dati statistici
        $.ajax({
            url: document_stats_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_document_stats',
                nonce: document_stats_ajax_object.nonce
            },
            success: function(response) {
                // Annulla il timeout di sicurezza
                if (safetyTimeout) clearTimeout(safetyTimeout);

                console.log('Risposta AJAX ricevuta:', response);

                if (response && response.success) {
                    console.log('Statistiche caricate con successo');

                    // Verifica se stiamo aggiornando dopo un'analisi
                    if (isAfterAnalysis) {
                        // Aggiornamento specifico dopo un'analisi
                        updateStatisticsAfterAnalysis(response.data);
                    } else {
                        // Aggiornamento normale
                        updateStatsWidget(response.data);
                    }

                    // Riapplica i gestori eventi per le nuove sezioni
                    setupToggleSections();
                    setupTooltips();

                    // Gestisce il click sugli elementi di analisi recenti
                    setupRecentAnalysesInteraction();
                } else {
                    console.error('Errore nel caricamento statistiche:', response);
                    // Mostra errore in caso di problemi
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-error">' +
                        'Errore nel caricamento delle statistiche: ' +
                        (response && response.data && response.data.message ? response.data.message : 'Errore sconosciuto') +
                        '<br><button class="refresh-stats-btn" id="retry-stats-btn">Riprova</button>' +
                        '</div>'
                    );

                    // Riapplica il gestore eventi al pulsante riprova
                }
            },
            error: function(xhr, status, error) {
                // Annulla il timeout di sicurezza
                if (safetyTimeout) clearTimeout(safetyTimeout);

                console.error('Errore AJAX:', status, error);

                // Verifica se c'è un cookie di utente esterno
                let isExternalUser = false;
                let externalUserName = '';
                try {
                    if (document.cookie.indexOf('fa_subscriber_login') !== -1) {
                        let cookieValue = getCookieValue('fa_subscriber_login');
                        if (cookieValue) {
                            try {
                                // Se è un JWT, prendi solo la parte payload (seconda parte, separata da '.')
                                let payload = cookieValue.split('.')[1] || cookieValue;
                                // Decodifica base64 (gestione url-safe)
                                let decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
                                let userData = JSON.parse(decoded);
                                externalUserName = userData.name || '';
                                isExternalUser = true;
                            } catch (e) {
                                console.error('Errore nella decodifica/parsing del cookie:', e);
                            }
                        }
                    }
                } catch (e) {
                    console.error('Errore nella lettura del cookie:', e);
                }

        // Verifica se l'errore è dovuto a un utente non identificato
                if (status === 'error' && xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message === 'No valid user found') {
                    // Utente non identificato correttamente, mostra messaggio appropriato
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-widget-login-message">' +
                        '<p>Impossibile identificare l\'utente. Effettua nuovamente il login.</p>' +
                        '</div>'
                    );
                } else if (isExternalUser) {
                    // Per utenti esterni con altri tipi di errori, mostra statistiche alternative
                    console.log('Errore nel caricamento delle statistiche per utente esterno, utilizzo fallback locale');
                    showAlternativeStatsForExternalUser(externalUserName);
                } else {
                    // Per utenti WordPress o non autenticati, mostra messaggio di errore standard
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-error">' +
                        'Errore di connessione: Impossibile caricare le statistiche. (' + status + ')' +
                        '</div>'
                    );
                }
            }
        });

        // Funzione per tentare di ripristinare l'interfaccia utente in caso di problemi
        function tryRestoreStatsUI() {
            // Verifica se c'è ancora il messaggio di caricamento
            if ($('.stats-loading').length > 0) {
                console.log('Ripristino interfaccia statistiche dopo timeout');

                // Mostra un messaggio di errore leggero
                if ($('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').length) {
                    $('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').html(
                        '<div class="stats-error">' +
                        'Impossibile aggiornare le statistiche.' +
                        '<br><button class="refresh-stats-btn" id="retry-stats-btn">Riprova</button>' +
                        '</div>'
                    );
                } else {
                    $('#user-stats-container .stats-section-content').html(
                        '<div class="stats-error">' +
                        'Impossibile aggiornare le statistiche.' +
                        '<br><button class="refresh-stats-btn" id="retry-stats-btn">Riprova</button>' +
                        '</div>'
                    );
                }

                // Riapplica il gestore eventi al pulsante riprova
                $('#retry-stats-btn').on('click', function() {
                    loadStatistics(true);
                });
            }
        }
    }

    /**
     * Recupera solo le analisi recenti tramite AJAX
     * Utile per aggiornare questa sezione senza ricaricare tutte le statistiche
     */
    function fetchRecentAnalyses() {
        // Se l'utente non è loggato, non caricare nulla
        if ($('#stats-column').hasClass('not-logged-in')) {
            console.log('Utente non loggato, analisi recenti non caricate');
            return;
        }

        console.log('Inizio caricamento analisi recenti...');

        // Mostra indicatore di caricamento nelle analisi recenti
        $('#recent-analyses-list').html('<div class="stats-loading">' +
            '<div class="stats-spinner"></div>' +
            'Caricamento analisi recenti...' +
            '</div>');

        // Effettua la richiesta AJAX per recuperare le analisi recenti
        $.ajax({
            url: document_stats_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_recent_analyses',
                nonce: document_stats_ajax_object.nonce
            },
            success: function(response) {
                console.log('Risposta ricevuta per analisi recenti:', response);

                if (response && response.success) {
                    console.log('Analisi recenti caricate con successo, numero analisi:',
                        response.data && response.data.recent_analyses ? response.data.recent_analyses.length : 0);

                    // Aggiorna la UI con le analisi recenti
                    if (response.data && response.data.recent_analyses && response.data.recent_analyses.length > 0) {
                        console.log('Aggiorno UI con analisi recenti ricevute');
                        updateRecentAnalyses(response.data.recent_analyses, false); // Non mostrare animazioni
                    } else {
                        console.log('Nessuna analisi recente trovata nella risposta');
                        $('#recent-analyses-list').html('<p class="no-analyses">Nessuna analisi effettuata.</p>');
                    }

                    // Gestisce il click sugli elementi di analisi recenti
                    setupRecentAnalysesInteraction();
                } else {
                    console.error('Errore nel caricamento delle analisi recenti:', response);

                    // Estrarre il messaggio di errore se disponibile
                    let errorMessage = 'Impossibile caricare le analisi recenti.';
                    let detailsMessage = '';

                    if (response.data) {
                        if (response.data.message) {
                            errorMessage = response.data.message;
                        }
                        if (response.data.sql_error) {
                            detailsMessage = 'Errore SQL: ' + response.data.sql_error;
                        }
                        console.log('Dettagli errore - is_subscriber:', response.data.is_subscriber, 'user_id:', response.data.user_id);
                    }

                    $('#recent-analyses-list').html('<div class="stats-error">' +
                        errorMessage +
                        (detailsMessage ? '<br><small>' + detailsMessage + '</small>' : '') +
                        '<br><button class="refresh-stats-btn" id="retry-recent-btn">Riprova</button>' +
                        '<button class="force-reset-btn" id="force-reset-recent-btn">Ripristina</button>' +
                        '</div>');

                    // Riapplica il gestore eventi al pulsante riprova
                    $('#retry-recent-btn').on('click', function() {
                        fetchRecentAnalyses();
                    });

                    // Aggiungi un gestore al pulsante di ripristino forzato
                    $('#force-reset-recent-btn').on('click', function() {
                        if (window.documentStats && window.documentStats.forceResetStats) {
                            window.documentStats.forceResetStats();
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Errore AJAX:', status, error);
                console.error('Dettagli risposta:', xhr.responseText);
                $('#recent-analyses-list').html('<div class="stats-error">' +
                    'Errore di connessione: Impossibile caricare le analisi recenti.' +
                    '<br><button class="refresh-stats-btn" id="retry-recent-btn">Riprova</button>' +
                    '</div>');

                // Riapplica il gestore eventi al pulsante riprova
                $('#retry-recent-btn').on('click', function() {
                    fetchRecentAnalyses();
                });
            }
        });
    }

    /**
     * Aggiornamento specifico per le statistiche dopo un'analisi completata
     *
     * @param {Object} data - Dati statistici ricevuti dal server
     */
    function updateStatisticsAfterAnalysis(data) {
        // Se l'utente non è loggato, verifica se è in modalità non-WordPress prima di procedere
        if ($('#stats-column').hasClass('not-logged-in')) {
            console.log('Utente non loggato (WordPress), verifico se è utente esterno');
            // Se non ci sono elementi di statistiche da aggiornare, non procedere
            if ($('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').length === 0) {
                console.log('Nessun contenitore statistiche trovato per utente non loggato, aggiornamento saltato');
                return;
            }
        }

        if (!data || !data.stats) {
            console.error('Dati statistiche non validi per aggiornamento post-analisi:', data);
            return;
        }

        console.log('Aggiornamento statistiche post-analisi:', data);

        // Aggiorna il tasso di costo se fornito dal server
        if (data.stats && data.stats.cost_per_token !== undefined) {
            // Verifica che sia un numero valido
            var newCostRate = parseFloat(data.stats.cost_per_token);
            if (!isNaN(newCostRate) && newCostRate > 0) {
                // Aggiorna la variabile globale per il calcolo del costo
                var oldRate = COST_RATE;
                COST_RATE = newCostRate;
                console.log('COST_RATE aggiornato dopo analisi:', COST_RATE, '(valore precedente:', oldRate, ')');
            } else {
                console.warn('Ricevuto cost_per_token non valido dopo analisi:', data.stats.cost_per_token, 'mantengo il valore attuale:', COST_RATE);
            }

            // Aggiorna anche il tipo di abbonamento
            if (data.user && data.user.role) {
                USER_SUBSCRIPTION_TYPE = data.user.role;
                console.log('Tipo di abbonamento utente aggiornato dopo analisi:', USER_SUBSCRIPTION_TYPE, 'con costo per token:', COST_RATE);
            }
        }

        try {
            // Verifica che la griglia delle statistiche esista
            if ($('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').length === 0) {
                console.error('Container delle statistiche non trovato, impossibile aggiornare');
                return;
            }

            // Aggiorna i valori nelle caselle esistenti mantenendo la struttura originale
            if ($('#analyses-count').length) {
                const currentVal = parseInt($('#analyses-count').text().replace(/[^\d]/g, '') || '0');
                animateCounter($('#analyses-count'), currentVal, data.stats.analysis_count);
            }

            if ($('#tokens-count').length) {
                const currentVal = parseInt($('#tokens-count').text().replace(/[^\d.]/g, '') || '0');
                animateCounter($('#tokens-count'), currentVal, data.stats.tokens_used, true);
            }

            // NON aggiorniamo la spesa stimata qui - la spesa stimata viene calcolata e visualizzata

            // Aggiorna il tasso di costo se fornito dal server
            if (data.stats && data.stats.cost_per_token !== undefined) {
                // Aggiorna la variabile globale per il calcolo del costo
                COST_RATE = parseFloat(data.stats.cost_per_token);
                console.log('Aggiornato COST_RATE in updateStatisticsAfterAnalysis:', COST_RATE);
            }
            // solo quando viene caricato un documento e vengono calcolati i token richiesti

            // Aggiorna il credito disponibile
            if ($('#credits-available').length && data.stats.credits_available !== undefined) {
                // Usa la funzione specifica per aggiornare i crediti disponibili
                updateAvailableCredits(data.stats.credits_available);

                // Log per debug
                console.log('Credito aggiornato (utente: ' + (data.is_subscriber ? 'esterno' : 'WordPress') + ')');
            }

            // Aggiorna la spesa effettiva dopo il completamento dell'analisi
            if ($('#actual-cost').length && data.stats.actual_cost !== undefined) {
                const currentActualCost = parseFloat($('#actual-cost').text().replace(/[^0-9,.]/g, '').replace(',', '.') || '0');
                const newActualCost = parseFloat(data.stats.actual_cost.replace(',', '.'));
                animateCost($('#actual-cost'), currentActualCost, newActualCost);

                // Evidenzia il cambiamento con una classe speciale
                $('#actual-cost').addClass('highlight-update');
                setTimeout(() => {
                    $('#actual-cost').removeClass('highlight-update');
                }, 3000);
            }

            // Aggiorna la spesa totale
            if ($('#tot-cost').length && data.stats.tot_cost !== undefined) {
                const currentTotalCost = parseFloat($('#tot-cost').text().replace(/[^0-9,.]/g, '').replace(',', '.') || '0');
                const newTotalCost = parseFloat(data.stats.tot_cost.replace(',', '.'));
                animateCost($('#tot-cost'), currentTotalCost, newTotalCost);

                // Evidenzia il cambiamento con una classe speciale
                $('#tot-cost').addClass('highlight-update');
                setTimeout(() => {
                    $('#tot-cost').removeClass('highlight-update');
                }, 3000);
            }

            // Aggiorna anche le date
            if ($('#first-analysis-date').length && data.dates && data.dates.first) {
                $('#first-analysis-date').text(data.dates.first);
            }

            if ($('#last-analysis-date').length && data.dates && data.dates.last) {
                $('#last-analysis-date').text(data.dates.last);
            }

            // Aggiorna anche le analisi recenti se disponibili
            if (data.recent_analyses && data.recent_analyses.length > 0) {
                updateRecentAnalyses(data.recent_analyses);
            }

            console.log('Aggiornamento post-analisi completato con successo');
        } catch (e) {
            console.error('Errore durante l\'aggiornamento delle statistiche post-analisi:', e);

            // Se c'è un errore, ricostruisci preservando il layout originale
            rebuildStatsContainerWithOriginalLayout(data);
        }
    }

    /**
     * Ricostruisce il container delle statistiche con il nuovo layout richiesto
     *
     * @param {Object} data - Dati statistici ricevuti dal server
     */
    function rebuildStatsContainerWithOriginalLayout(data) {
        console.log('Ricostruzione container statistiche con layout migliorato');

        if (!data || !data.stats) {
            console.error('Dati mancanti per la ricostruzione del container');
            return;
        }

        // Ottieni il contenitore della griglia di statistiche
        const $gridContainer = $('.stats-grid, .document-viewer-stats-container .stats-grid');

        if ($gridContainer.length === 0) {
            console.error('Container delle statistiche (.stats-grid) non trovato');
            return;
        }

        // Aggiorna solo il nome utente
        if ($('#user-name').length) {
            $('#user-name').text(data.user.name);
        }

        // Rimuovi il ruolo utente se esiste (non aggiungerlo anche se non esiste)
        $('#user-role').remove();

        // Nuovo layout con statistiche riorganizzate usando classi CSS
        $gridContainer.html(`
            <div class="stats-row usage-row">
                <div class="stats-item">
                    <div class="stats-label">
                        Analisi
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di analisi effettuate</div>
                    </div>
                    <div class="stats-value highlight-update" id="analyses-count">${data.stats.analysis_count}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">
                        Token
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di token utilizzati in tutte le analisi</div>
                    </div>
                    <div class="stats-value highlight-update" id="tokens-count">${formatCharCount(data.stats.tokens_used)}</div>
                </div>
            </div>

            <div class="stats-row costs-row">
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Stimata
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Stima del costo basata sul numero di token utilizzati</div>
                    </div>
                    <div class="stats-value cost-highlight" id="cost-estimate">${$('#cost-estimate').text() || '€0,00'}</div>
                </div>
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Effettiva
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo effettivo dell'analisi completata</div>
                    </div>
                    <div class="stats-value cost-highlight highlight-update" id="actual-cost">€${data.stats.actual_cost || '0.00'}</div>
                </div>
            </div>

            <!-- Costo totale nascosto - mantenuto solo nel database per statistiche -->
            <div class="stats-row total-cost-row" style="display: none;">
                <div class="stats-item total-cost-item">
                    <div class="stats-label">
                        Spesa Totale
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo totale di tutte le analisi (dato statistico)</div>
                    </div>
                    <div class="stats-value total-cost-highlight highlight-update" id="tot-cost">€${data.stats.tot_cost || '0.00'}</div>
                </div>
            </div>

            <div class="stats-row credit-row">
                <div class="stats-item credit-item">
                    <div class="stats-label">
                        Credito Disponibile
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Credito disponibile per l'esecuzione di nuove analisi</div>
                    </div>
                    <div class="stats-value credit-highlight highlight-update" id="credits-available">€${data.stats.credits_available}</div>
                </div>
            </div>
        `);

        // Aggiorna anche le date se presenti
        if (data.dates) {
            $('.date-info').html(`
                <div class="date-group">
                    <span class="date-label">Prima analisi:</span>
                    <span class="date-value" id="first-analysis-date">${data.dates.first || '-'}</span>
                </div>
                <div class="date-group">
                    <span class="date-label">Ultima analisi:</span>
                    <span class="date-value" id="last-analysis-date">${data.dates.last || '-'}</span>
                </div>
            `);
        }

        // Aggiorna le analisi recenti
        if (data.recent_analyses && data.recent_analyses.length > 0) {
            updateRecentAnalyses(data.recent_analyses);
        }

        // Adattamento specifico per updateStatsWidget quando non siamo dopo un'analisi
        // Assicuriamoci che anche la funzione updateStatsWidget rimuova il ruolo utente
        const originalUpdateStatsWidget = updateStatsWidget;
        updateStatsWidget = function(data) {
            // Rimuovi il ruolo prima di aggiornare
            $('#user-role').remove();

            // Chiama la funzione originale
            originalUpdateStatsWidget(data);

            // Rimuovi nuovamente il ruolo dopo l'aggiornamento
            $('#user-role').remove();
        };

        // Ripristina tooltip e interazioni
        setupTooltips();

        // Rimuovi l'evidenziazione dopo un po'
        setTimeout(() => {
            $('.highlight-update').removeClass('highlight-update');
        }, 2000);

        console.log('Ricostruzione container con layout migliorato completata');
    }

    /**
     * Aggiorna il widget con i dati ricevuti dal server
     *
     * @param {Object} data - Dati statisticici ricevuti dal server
     */
    function updateStatsWidget(data) {
        if (!data || !data.user) {
            console.error('Dati statistiche non validi:', data);
            return;
        }

        console.log('Aggiornamento UI con i dati statistici');

        try {
            // Aggiorna il tasso di costo se fornito dal server
            if (data.stats && data.stats.cost_per_token !== undefined) {
                // Verifica che il costo sia un numero valido
                var newCostRate = parseFloat(data.stats.cost_per_token);
                if (!isNaN(newCostRate) && newCostRate > 0) {
                    // Aggiorna la variabile globale per il calcolo del costo
                    var oldRate = COST_RATE;
                    COST_RATE = newCostRate;
                    console.log('COST_RATE aggiornato dal server:', COST_RATE, '(valore precedente:', oldRate, ')');
                } else {
                    console.warn('Ricevuto cost_per_token non valido:', data.stats.cost_per_token, 'mantengo il valore attuale:', COST_RATE);
                }

                // Aggiorna il tipo di abbonamento se disponibile
                if (data.user && data.user.role) {
                    USER_SUBSCRIPTION_TYPE = data.user.role;
                    console.log('Tipo di abbonamento utente:', USER_SUBSCRIPTION_TYPE, 'con costo per token:', COST_RATE);
                }
            }

            // Verifica l'esistenza degli elementi prima di aggiornare
            if ($('#user-name').length) {
                $('#user-name').text(data.user.name);
            } else {
                $('#user-stats-container .user-details').append('<div class="user-name" id="user-name">' + data.user.name + '</div>');
            }

            // Rimuovi il ruolo utente se esiste (non aggiungerlo anche se non esiste)
            $('#user-role').remove();

            // Avatar utente (se presente)
            if ($('#user-avatar').length && data.user.avatar) {
                $('#user-avatar').html('<img src="' + data.user.avatar + '" alt="Avatar" />');
            }

            // Verifica se la griglia delle statistiche esiste
            if ($('.document-viewer-stats-container .stats-grid, #user-stats-container .stats-grid').length) {
                // Aggiorna i valori usando la struttura esistente
                if ($('#analyses-count').length) {
                    $('#analyses-count').text(data.stats.analysis_count);
                }

                if ($('#tokens-count').length) {
                    $('#tokens-count').text(formatCharCount(data.stats.tokens_used));
                }

                // NON aggiorniamo la spesa stimata qui - deve essere aggiornata solo quando viene caricato un documento
                // La spesa stimata è stata preservata nel client e non dovrebbe essere sovrascritta durante il caricamento delle statistiche

                // Aggiorna il credito disponibile
                if ($('#credits-available').length && data.stats.credits_available !== undefined) {
                    $('#credits-available').text('€' + data.stats.credits_available);
                }

                // Aggiorna la spesa effettiva
                if ($('#actual-cost').length) {
                    $('#actual-cost').text('€' + (data.stats.actual_cost || '0.00'));
                }

                // Aggiorna la spesa totale
                if ($('#tot-cost').length && data.stats.tot_cost !== undefined) {
                    $('#tot-cost').text('€' + data.stats.tot_cost);
                    console.log('Spesa totale aggiornata a: €' + data.stats.tot_cost);
                }

                // Aggiorna anche le analisi recenti se disponibili
                if (data.recent_analyses && Array.isArray(data.recent_analyses)) {
                    console.log('Aggiornamento analisi recenti al caricamento della pagina:', data.recent_analyses.length, 'analisi trovate');
                    updateRecentAnalyses(data.recent_analyses, false); // Non mostrare animazioni

                    // Gestisce il click sugli elementi di analisi recenti
                    setupRecentAnalysesInteraction();
                } else {
                    console.log('Nessuna analisi recente trovata nei dati ricevuti');
                    $('#recent-analyses-list').html('<p class="no-analyses">Nessuna analisi effettuata.</p>');
                }
            } else {
                // Se la griglia non esiste, ricostruisci preservando la struttura originale
                rebuildStatsContainerWithOriginalLayout(data);
            }
        } catch (e) {
            console.error('Errore durante l\'aggiornamento della UI:', e);
        }
    }

    /**
     * Aggiorna la lista delle analisi recenti
     *
     * @param {Array} analyses - Lista delle analisi recenti
     * @param {boolean} showHighlight - Se mostrare l'effetto di evidenziazione (default: true)
     */
    function updateRecentAnalyses(analyses, showHighlight = true) {
        const $container = $('#recent-analyses-list');

        // Svuota il contenitore
        $container.empty();

        if (analyses && analyses.length > 0) {
            // Aggiungi le analisi recenti (limitate a 5)
            analyses.slice(0, 5).forEach(function(analysis) {
                const $item = $('<div class="recent-analysis-item" data-id="' + analysis.id + '">' +
                    '<div class="analysis-item-title">' + analysis.title + '</div>' +
                    '<div class="analysis-item-meta">' +
                    '<span class="analysis-date">' + analysis.date + '</span>' +
                    '<span class="analysis-tokens">' + formatNumber(analysis.tokens) + ' token</span>' +
                    '</div>' +
                    '</div>');

                $container.append($item);
            });

            // Espandi la sezione delle analisi recenti se è chiusa
            const $recentSection = $('#recent-analyses-container');
            const $content = $recentSection.find('.stats-section-content');
            const $icon = $recentSection.find('.toggle-icon');

            if ($content.is(':hidden')) {
                $icon.addClass('expanded');
                $content.slideDown(300);
            }

            // Evidenzia temporaneamente la sezione delle analisi recenti solo se richiesto
            if (showHighlight) {
                $recentSection.addClass('highlight-section');
                setTimeout(() => {
                    $recentSection.removeClass('highlight-section');
                }, 2000);
            }
        } else {
            // Nessuna analisi
            $container.html('<p class="no-analyses">Nessuna analisi effettuata.</p>');
        }
    }

    /**
     * Formatta un numero con separatore delle migliaia
     *
     * @param {number} number - Numero da formattare
     * @returns {string} - Numero formattato
     */
    function formatNumber(number) {
        return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    /**
     * Formatta un conteggio caratteri/token in formato compatto (1.0k)
     *
     * @param {number} count - Conteggio da formattare
     * @returns {string} - Conteggio formattato
     */
    function formatCharCount(count) {
        if (count < 1000) {
            return count.toString();
        } else {
            return (count / 1000).toFixed(1) + 'k';
        }
    }

    /**
     * Configura l'interazione con gli elementi di analisi recenti
     */
    function setupRecentAnalysesInteraction() {
        $('.recent-analysis-item').on('click', function() {
            const analysisId = $(this).data('id');

            // Se c'è un ID valido, carica l'analisi
            if (analysisId) {
                loadSavedAnalysis(analysisId);
            }
        });
    }

    /**
     * Carica un'analisi salvata tramite AJAX
     *
     * @param {number} analysisId - ID dell'analisi da caricare
     */
    function loadSavedAnalysis(analysisId) {
        // Mostra messaggio di caricamento
        showDocumentNotification('<div class="spinner"></div> Caricamento analisi salvata...', 'processing');

        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'load_saved_analysis',
                nonce: documentViewerParams.nonce,
                analysis_id: analysisId
            },
            success: function(response) {
                if (response.success) {
                    // Aggiorna UI con l'analisi caricata
                    $('#analysis-title').val(response.data.title || '');
                    $('#document-description').val(response.data.query || '');

                    // Aggiorna il contenuto dell'analisi
                    if (response.data.analysis_results) {
                        $('#analysis-results').html(response.data.analysis_results);
                        // Ensure the analysis results container is scrollable
                        $('#analysis-results').css({
                            'overflow-y': 'auto',
                            'max-height': '500px' // Adjust this value as needed
                        });
                        // Imposta flag globali se esistono
                        if (typeof currentAnalysisResults !== 'undefined') {
                            currentAnalysisResults = response.data.analysis_results;
                        }
                    }

                    // Carica il documento se disponibile
                    if (response.data.document_url) {
                        $('#document-frame').attr('src', response.data.document_url).show();
                        $('#document-display').show();
                        $('#zoom-in, #zoom-out').show();
                    }

                    // Avvisa l'utente del caricamento completato
                    showDocumentNotification('<strong>✓ Analisi caricata con successo!</strong>', 'success');
                } else {
                    // Mostra errore
                    showDocumentNotification('Errore nel caricamento dell\'analisi: ' +
                        (response.data && response.data.message ? response.data.message : 'Errore sconosciuto'), 'error');
                }
            },
            error: function() {
                showDocumentNotification('Errore di connessione: Impossibile caricare l\'analisi.', 'error');
            }
        });
    }

    /**
     * Mostra una notifica all'utente
     *
     * @param {string} message - Messaggio da mostrare
     * @param {string} type - Tipo di notifica (success, error, processing)
     */
    function showDocumentNotification(message, type) {
        // Verifica se la funzione è già definita globalmente
        if (typeof window.showDocumentNotification === 'function') {
            window.showDocumentNotification(message, type);
        } else {
            // Funzione di fallback se quella globale non è disponibile
            const $notificationArea = $('#document-notification-area');
            const $content = $notificationArea.find('.notification-content');

            $content.html(message);
            $notificationArea.removeClass('success error processing').addClass(type).fadeIn();

            if (type !== 'processing') {
                setTimeout(function() {
                    $notificationArea.fadeOut();
                }, 5000);
            }
        }
    }

    /**
     * Anima il contatore numerico con effetto di incremento/decremento
     *
     * @param {jQuery} $element - Elemento DOM che contiene il contatore
     * @param {number} start - Valore iniziale
     * @param {number} end - Valore finale
     * @param {boolean} isFormatted - Se il numero deve essere formattato con separatori delle migliaia
     */
    function animateCounter($element, start, end, isFormatted = false) {
        // Se start e end sono uguali, non fare nulla
        if (start === end) return;

        // Durata dell'animazione in millisecondi
        const duration = 1000;
        // Numero di step dell'animazione
        const steps = 20;
        // Intervallo tra gli step
        const stepTime = duration / steps;
        // Incremento per ogni step
        const increment = (end - start) / steps;

        // Valore corrente
        let current = start;
        // Contatore di step
        let step = 0;

        // Evidenzia l'elemento
        $element.addClass('highlight-animation');

        // Funzione di animazione
        const animate = () => {
            step++;
            current += increment;

            // Assicurati che non superi il valore finale
            if ((increment > 0 && current > end) || (increment < 0 && current < end)) {
                current = end;
            }

            // Aggiorna il valore dell'elemento
            if (isFormatted) {
                $element.text(formatNumber(Math.round(current)));
            } else {
                $element.text(Math.round(current));
            }

            // Continua l'animazione se non è finita
            if (step < steps) {
                setTimeout(animate, stepTime);
            } else {
                // Rimuovi l'evidenziazione alla fine dell'animazione
                setTimeout(() => {
                    $element.removeClass('highlight-animation');
                }, 500);
            }
        };

        // Avvia l'animazione
        animate();
    }

    /**
     * Anima il valore di costo con effetto di incremento/decremento
     *
     * @param {jQuery} $element - Elemento DOM che contiene il valore di costo
     * @param {number} start - Valore iniziale
     * @param {number} end - Valore finale
     */
    function animateCost($element, start, end) {
        // Se start e end sono uguali, non fare nulla
        if (start === end) return;

        // Durata dell'animazione in millisecondi
        const duration = 1500;
        // Numero di step dell'animazione
        const steps = 30;
        // Intervallo tra gli step
        const stepTime = duration / steps;
        // Incremento per ogni step
        const increment = (end - start) / steps;

        // Valore corrente
        let current = start;
        // Contatore di step
        let step = 0;

        // Evidenzia l'elemento
        $element.addClass('highlight-animation');

        // Funzione di animazione
        const animate = () => {
            step++;
            current += increment;

            // Assicurati che non superi il valore finale
            if ((increment > 0 && current > end) || (increment < 0 && current < end)) {
                current = end;
            }

            // Aggiorna il valore dell'elemento
            $element.text('€' + current.toFixed(2).replace('.', ','));

            // Continua l'animazione se non è finita
            if (step < steps) {
                setTimeout(animate, stepTime);
            } else {
                // Rimuovi l'evidenziazione alla fine dell'animazione
                setTimeout(() => {
                    $element.removeClass('highlight-animation');
                }, 500);
            }
        };

        // Avvia l'animazione
        animate();    }

    // Funzione per gestire il logout degli utenti esterni
    window.subscriberLogout = function(event) {
        if (event) {
            event.preventDefault();
        }

        console.log('Logout utente esterno in corso...');

        // Mostra indicatore di caricamento
        const $logoutLink = jQuery(event?.target)?.closest('a');
        const originalText = $logoutLink?.length ? $logoutLink.html() : '';

        if ($logoutLink?.length) {
            $logoutLink.html('<span class="spinner-inline"></span> Disconnessione...');
            $logoutLink.addClass('logging-out');
        }

        // Mostra notifica di operazione in corso
        if (typeof window.showDocumentNotification === 'function') {
            window.showDocumentNotification('<div class="spinner"></div> Disconnessione in corso...', 'processing');
        } else if (typeof showDocumentNotification === 'function') {
            showDocumentNotification('<div class="spinner"></div> Disconnessione in corso...', 'processing');
        }

        // Pulisci i cookie di sessione e autenticazione
        document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
        document.cookie = 'fa_actual_cost=; path=/; max-age=0';
        document.cookie = 'fa_subscriber_login=; path=/; max-age=0';
        document.cookie = 'fa_subscriber_data=; path=/; max-age=0';

        // Pulisci i dati nel localStorage mantenendo le statistiche
        if (localStorage.getItem('fa_external_user_stats')) {
            try {
                const statsData = JSON.parse(localStorage.getItem('fa_external_user_stats'));
                // Mantieni solo i dati necessari e azzera i costi
                const { analysis_count, tokens_used, credits_available } = statsData;
                localStorage.setItem('fa_external_user_stats', JSON.stringify({
                    analysis_count: analysis_count || 0,
                    tokens_used: tokens_used || 0,
                    credits_available: credits_available || 0,
                    actual_cost: '0,00',
                    // Mantieni il tot_cost esistente - non resettarlo
                    tot_cost: statsData.tot_cost || '0,00',
                    cost_estimate: '0,00'
                }));
            } catch (e) {
                console.error('Errore nel reset dei dati localStorage:', e);
            }
        }

        // Reindirizza alla root dopo un breve ritardo
        setTimeout(() => {
            window.location.href = '/';
        }, 500);
    };

    // Funzione per pulire i dati utente prima del logout
    window.cleanUserDataBeforeLogout = function(event) {
        if (event) {
            event.preventDefault();
        }

        console.log('Pulizia dati utente prima del logout...');

        // Mostra indicatore di caricamento
        const $logoutLink = jQuery(event?.target)?.closest('a');
        const originalText = $logoutLink?.length ? $logoutLink.html() : '';

        if ($logoutLink?.length) {
            $logoutLink.html('<span class="spinner-inline"></span> Disconnessione...');
            $logoutLink.addClass('logging-out');
        }

        // Mostra notifica di operazione in corso
        if (typeof window.showDocumentNotification === 'function') {
            window.showDocumentNotification('<div class="spinner"></div> Disconnessione in corso...', 'processing');
        } else if (typeof showDocumentNotification === 'function') {
            showDocumentNotification('<div class="spinner"></div> Disconnessione in corso...', 'processing');
        }

        // Pulisci i cookie di sessione e autenticazione
        document.cookie = 'fa_estimated_cost=; path=/; max-age=0';
        document.cookie = 'fa_actual_cost=; path=/; max-age=0';
        document.cookie = 'fa_subscriber_login=; path=/; max-age=0';
        document.cookie = 'fa_subscriber_data=; path=/; max-age=0';

        // Pulisci i dati nel localStorage mantenendo le statistiche
        if (localStorage.getItem('fa_external_user_stats')) {
            try {
                const statsData = JSON.parse(localStorage.getItem('fa_external_user_stats'));
                // Mantieni solo i dati necessari e azzera i costi
                const { analysis_count, tokens_used, credits_available } = statsData;
                localStorage.setItem('fa_external_user_stats', JSON.stringify({
                    analysis_count: analysis_count || 0,
                    tokens_used: tokens_used || 0,
                    credits_available: credits_available || 0,
                    actual_cost: '0,00',
                    // Mantieni il tot_cost esistente - non resettarlo
                    tot_cost: statsData.tot_cost || '0,00',
                    cost_estimate: '0,00'
                }));
            } catch (e) {
                console.error('Errore nel reset dei dati localStorage:', e);
            }
        }

        // Reindirizza alla root dopo un breve ritardo
        setTimeout(() => {
            window.location.href = '/';
        }, 500);
    };

    // Funzione helper per estrarre il valore di un cookie
    function getCookieValue(cookieName) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${cookieName}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    // Funzione per mostrare statistiche alternative per utenti esterni
    function showAlternativeStatsForExternalUser(userName) {
        console.log('Mostrando statistiche alternative per utente esterno');

        // Recupera i dati dal localStorage se disponibili
        let savedStats = localStorage.getItem('fa_external_user_stats');
        let statsData = savedStats ? JSON.parse(savedStats) : {
            analysis_count: 0,
            tokens_used: 0,
            credits_available: '0,00',
            actual_cost: '0,00',
            tot_cost: '0,00'
        };

        // Formatta il nome utente
        userName = userName || 'Utente Esterno';

        // Costruisci l'interfaccia utente
        const statsHtml = `
        <div class="document-viewer-stats-container">
            <div class="stats-grid">
            <div class="user-details">
                <div id="user-avatar"><div class="avatar-placeholder">U</div></div>
                <div id="user-name">${userName}</div>
            </div>
            <div class="stats-row usage-row">
                <div class="stats-item">
                    <div class="stats-label">
                        Analisi
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di analisi effettuate</div>
                    </div>
                    <div class="stats-value" id="analyses-count">${statsData.analysis_count}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">
                        Token
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di token utilizzati in tutte le analisi</div>
                    </div>
                    <div class="stats-value" id="tokens-count">${statsData.tokens_used}</div>
                </div>
            </div>

            <div class="stats-row costs-row">
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Stimata
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Stima del costo basata sul numero di token utilizzati</div>
                    </div>
                   <div class="stats-value cost-highlight" id="cost-estimate">${$('#cost-estimate').text() || '€0,00'}</div>
                </div>
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Effettiva
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo effettivo dell'analisi completata</div>
                    </div>
                    <div class="stats-value cost-highlight" id="actual-cost">€${statsData.actual_cost}</div>
                </div>
            </div>

            <!-- Costo totale nascosto - mantenuto solo nel database per statistiche -->
            <div class="stats-row total-cost-row" style="display: none;">
                <div class="stats-item total-cost-item">
                    <div class="stats-label">
                        Spesa Totale
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo totale di tutte le analisi (dato statistico)</div>
                    </div>
                    <div class="stats-value total-cost-highlight" id="tot-cost">€${statsData.tot_cost}</div>
                </div>
            </div>

            <div class="stats-row credit-row">
                <div class="stats-item credit-item">
                    <div class="stats-label">
                        Credito Disponibile
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Credito disponibile per l'esecuzione di nuove analisi</div>
                    </div>
                    <div class="stats-value credit-highlight" id="credits-available">€${statsData.credits_available}</div>
                </div>
            </div>

            <div class="stats-note">
                <p><i>Nota: Visualizzazione temporanea delle statistiche. I dati potrebbero non essere aggiornati.</i></p>
                <p><small>Il sistema sta utilizzando dati memorizzati localmente a causa di un problema di connessione.</small></p>
            </div>
        </div>`;

        // Inserisci l'HTML nel container
        $('#user-stats-container .stats-section-content').html(statsHtml);

        // Riapplica i gestori eventi per tooltip e altre funzionalità
        if (typeof setupTooltips === 'function') {
            setupTooltips();
        }
    }

    // Rimozione del refresh automatico delle statistiche per evitare aggiornamenti non necessari
    function refreshUserStats() {
        // Funzione mantenuta per retrocompatibilità ma senza auto-refresh
        // Può essere chiamata manualmente quando necessario
        jQuery.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'refresh_user_stats',
                nonce: documentViewerParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Statistiche aggiornate con successo');
                    updateStatsWidget(response.data);
                } else {
                    console.error('Errore nell\'aggiornamento delle statistiche:', response.data ? response.data.message : 'Errore sconosciuto');
                }
            },
            error: function(xhr, status, error) {
                console.error('Errore nella chiamata AJAX per aggiornare le statistiche:', error);
            }
        });
    }

    // Aggiorna le statistiche dopo l'analisi del documento
    $(document).on('document_analysis_complete', function() {
        refreshUserStats();
    });

    // Aggiorna la spesa stimata quando viene caricato un documento
    $(document).on('document_loaded', function(e, data) {
        console.log('Evento document_loaded ricevuto con dati:', data);

        // Ottieni gli elementi della spesa stimata
        const $costElements = $('#cost-estimate, #estimated-cost');

        // Mostra gli elementi con stili forzati
        $costElements.css({
            'display': 'inline-block',
            'opacity': '1',
            'visibility': 'visible'
        });

        // Se ci sono token, aggiorna la spesa stimata
        if (data && data.tokenCount) {
            console.log('Documento caricato con tokenCount:', data.tokenCount);

            // Usa la funzione centralizzata per aggiornare la spesa stimata
            if (window.documentStats && typeof window.documentStats.updateCostEstimate === 'function') {
                // Chiama direttamente senza timeout per evitare ritardi
                window.documentStats.updateCostEstimate(data.tokenCount);
            } else {
                console.error('documentStats.updateCostEstimate non è una funzione');
                // Fallback: aggiorna direttamente l'UI
                const estimatedCost = calculateEstimatedCost(data.tokenCount);
                $costElements
                    .text('€' + estimatedCost)
                    .addClass('cost-updated');

                setTimeout(() => {
                    $costElements.removeClass('cost-updated');
                }, 1500);
            }

            // Resetta la spesa effettiva quando si carica un nuovo documento
            if (window.documentStats && typeof window.documentStats.clearActualCost === 'function') {
                window.documentStats.clearActualCost();
            }
        } else {
            console.log('Nessun tokenCount nei dati del documento, imposto a 0,00');
            $costElements
                .text('€0,00')
                .removeClass('cost-updated');
        }
    });

    /**
     * Aggiorna visivamente il credito disponibile e applica l'evidenziazione
     *
     * @param {string|number} newValue Nuovo valore del credito disponibile
     */
    function updateAvailableCredits(newValue) {
        if (!$('#credits-available').length) {
            console.log('Elemento credito disponibile non trovato');
            return;
        }

        // Ottieni il valore corrente per l'animazione
        const currentCredit = parseFloat($('#credits-available').text().replace(/[^0-9,.]/g, '').replace(',', '.') || '0');
        const newCredit = parseFloat(String(newValue).replace(',', '.'));

        // Aggiorna il valore con animazione
        animateCost($('#credits-available'), currentCredit, newCredit);

        // Evidenzia il cambiamento
        $('#credits-available').addClass('highlight-update');
        setTimeout(() => {
            $('#credits-available').removeClass('highlight-update');
        }, 3000);

        console.log('Credito disponibile aggiornato visivamente da ' + currentCredit + ' a ' + newCredit);
    }

    // Esporta la funzione nell'oggetto globale
    window.documentStats = window.documentStats || {};
    window.documentStats.updateAvailableCredits = updateAvailableCredits;
});