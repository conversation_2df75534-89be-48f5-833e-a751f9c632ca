(function($) {
    'use strict';
    
    // Variabili globali
    let currentPopup = null;
    
    /**
     * Chiude il popup di help
     */
    function closeHelpPopup() {
        if (currentPopup && currentPopup.length) {
            currentPopup.removeClass('fa-wh-open');
            setTimeout(function() {
                currentPopup.remove();
                currentPopup = null;
            }, 300);
        }
        
        // Reset aria-expanded su tutte le icone
        $('.fa-wh-help-icon').attr('aria-expanded', 'false');
    }
    
    /**
     * Apre il popup di help
     */
    function openHelpPopup(widgetId, $icon) {
        // Chiudi popup esistente
        if (currentPopup) {
            closeHelpPopup();
        }
        
        // Crea nuovo popup
        currentPopup = $('<div class="fa-wh-help-popup" role="dialog" aria-labelledby="fa-wh-help-title" aria-modal="true">' +
            '<button class="fa-wh-close" aria-label="Chiudi help" title="<PERSON><PERSON>">&times;</button>' +
            '<h2 id="fa-wh-help-title">Help Widget</h2>' +
            '<div class="fa-wh-content fa-wh-loading">Caricamento contenuto help...</div>' +
            '</div>');
        
        $('body').append(currentPopup);
        
        // Mostra popup con animazione
        setTimeout(function() {
            if (currentPopup) {
                currentPopup.addClass('fa-wh-open');
                $icon.attr('aria-expanded', 'true');
            }
        }, 10);
        
        // Carica contenuto via AJAX
        loadHelpContent(widgetId);
        
        // Focus management per accessibilità
        currentPopup.find('.fa-wh-close').focus();
    }
    
    /**
     * Carica il contenuto dell'help via AJAX
     */
    function loadHelpContent(widgetId) {
        if (!currentPopup) return;
        
        // Verifica che ajaxurl sia disponibile
        const ajaxUrl = window.ajaxurl || window.faWidgetHelp?.ajaxUrl;
        if (!ajaxUrl) {
            currentPopup.find('.fa-wh-content').html('<em>Errore: URL AJAX non disponibile.</em>');
            return;
        }
        
        // Verifica nonce
        const nonce = window.faWidgetHelp?.nonce;
        if (!nonce) {
            currentPopup.find('.fa-wh-content').html('<em>Errore: Nonce di sicurezza non disponibile.</em>');
            return;
        }
        
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'fa_get_widget_help',
                widget_id: widgetId,
                _ajax_nonce: nonce
            },
            timeout: 10000,
            success: function(response) {
                if (!currentPopup) return;
                
                if (response.success && response.data && response.data.content) {
                    currentPopup.find('.fa-wh-content').html(response.data.content);
                } else {
                    currentPopup.find('.fa-wh-content').html('<em>Nessun contenuto di help disponibile per questo widget.</em>');
                }
            },
            error: function(xhr, status, error) {
                if (!currentPopup) return;
                
                let errorMsg = 'Errore nel caricamento del contenuto help.';
                if (status === 'timeout') {
                    errorMsg = 'Timeout nel caricamento del contenuto help.';
                }
                currentPopup.find('.fa-wh-content').html('<em>' + errorMsg + '</em>');
            }
        });
    }
    
    // Event handlers
    $(document).ready(function() {
        
        // Click sull'icona help
        $(document).on('click', '.fa-wh-help-icon', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const $icon = $(this);
            const widgetId = $icon.data('widget-id');
            
            if (!widgetId) {
                console.warn('Widget ID non trovato per l\'icona help');
                return;
            }
            
            openHelpPopup(widgetId, $icon);
        });
        
        // Click sul pulsante di chiusura
        $(document).on('click', '.fa-wh-close', function(e) {
            e.preventDefault();
            closeHelpPopup();
        });
        
        // Click fuori dal popup (non implementato per evitare chiusure accidentali)
        
        // Gestione tasti
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' || e.keyCode === 27) {
                closeHelpPopup();
            }
        });
        
        // Gestione focus trap nel popup
        $(document).on('keydown', '.fa-wh-help-popup', function(e) {
            if (e.key === 'Tab' || e.keyCode === 9) {
                const $popup = $(this);
                const $focusableElements = $popup.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                const $firstElement = $focusableElements.first();
                const $lastElement = $focusableElements.last();
                
                if (e.shiftKey) {
                    // Shift + Tab
                    if (document.activeElement === $firstElement[0]) {
                        $lastElement.focus();
                        e.preventDefault();
                    }
                } else {
                    // Tab
                    if (document.activeElement === $lastElement[0]) {
                        $firstElement.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    });
    
})(jQuery);
