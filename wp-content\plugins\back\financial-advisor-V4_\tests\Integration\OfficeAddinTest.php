<?php

namespace Tests\Integration;

use Tests\BaseTestCase;
use Mockery;

/**
 * Integration tests for Office_Addin class
 */
class OfficeAddinTest extends BaseTestCase
{
    private $office_addin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Include all required classes
        require_once __DIR__ . '/../../includes/class-document-analyzer.php';
        require_once __DIR__ . '/../../includes/class-office-addin-rate-limiter.php';
        require_once __DIR__ . '/../../includes/class-office-addin-cache-manager.php';
        require_once __DIR__ . '/../../includes/class-office-addin-error-reporter.php';
        require_once __DIR__ . '/../../includes/class-office-addin-performance-monitor.php';
        require_once __DIR__ . '/../../office-addin.php';
        
        $this->office_addin = new \Office_Addin();
    }

    public function testConstructorInitializesAllComponents(): void
    {
        $rate_limiter = $this->getProperty($this->office_addin, 'rate_limiter');
        $cache_manager = $this->getProperty($this->office_addin, 'cache_manager');
        $error_reporter = $this->getProperty($this->office_addin, 'error_reporter');
        $performance_monitor = $this->getProperty($this->office_addin, 'performance_monitor');
        
        $this->assertInstanceOf(\Office_Addin_Rate_Limiter::class, $rate_limiter);
        $this->assertInstanceOf(\Office_Addin_Cache_Manager::class, $cache_manager);
        $this->assertInstanceOf(\Office_Addin_Error_Reporter::class, $error_reporter);
        $this->assertInstanceOf(\Office_Addin_Performance_Monitor::class, $performance_monitor);
    }

    public function testGetSettingsHandlesRateLimiting(): void
    {
        global $wpdb;
        
        // Mock rate limiting - first call allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock WordPress functions
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock settings data
        \Brain\Monkey\Functions\when('get_option')
            ->andReturnUsing(function($option) {
                switch ($option) {
                    case 'openai_api_key': return 'test_api_key';
                    case 'claude_api_key': return 'test_claude_key';
                    case 'gemini_api_key': return 'test_gemini_key';
                    default: return false;
                }
            });
        
        $_POST['action'] = 'get_office_addin_settings';
        $_POST['nonce'] = 'test_nonce';
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('success', $output);
    }

    public function testGetSettingsRejectsWhenRateLimited(): void
    {
        global $wpdb;
        
        // Mock rate limiting - exceeded
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn([
            'count' => 65, // Exceeds settings limit of 60
            'window_start' => time() - 30
        ]);
        
        $wpdb->shouldReceive('insert')->once()->andReturn(1); // Error logging
        
        $_POST['action'] = 'get_office_addin_settings';
        $_POST['nonce'] = 'test_nonce';
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('rate_limit_exceeded', $output);
    }

    public function testAnalyzeDocumentFullWorkflow(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock performance monitoring
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        // Mock WordPress functions
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock document analyzer
        \Brain\Monkey\Functions\when('wp_remote_post')->justReturn([
            'response' => ['code' => 200],
            'body' => json_encode([
                'choices' => [
                    ['message' => ['content' => 'Test analysis result']]
                ]
            ])
        ]);
        
        $_POST['action'] = 'analyze_document_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['content'] = 'Test document content for analysis';
        $_POST['analysis_type'] = 'summary';
        
        ob_start();
        $this->office_addin->handle_analyze_document();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('success', $output);
    }

    public function testAnalyzeDocumentWithCaching(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->andReturnUsing(function($key) {
                if (strpos($key, 'rate_limit') !== false) {
                    return false; // No rate limiting
                }
                // Return cached analysis result
                return [
                    'result' => 'Cached analysis result',
                    'timestamp' => time() - 1800 // 30 minutes ago
                ];
            });
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        $_POST['action'] = 'analyze_document_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['content'] = 'Test document content';
        $_POST['analysis_type'] = 'summary';
        
        ob_start();
        $this->office_addin->handle_analyze_document();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('success', $output);
        $this->assertStringContainsString('Cached analysis result', $output);
    }

    public function testTestApiConnectionHandlesAllProviders(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock performance monitoring
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock successful API response
        \Brain\Monkey\Functions\when('wp_remote_post')->justReturn([
            'response' => ['code' => 200],
            'body' => json_encode([
                'choices' => [['message' => ['content' => 'Test response']]]
            ])
        ]);
        
        $_POST['action'] = 'test_api_connection_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['provider'] = 'openai';
        $_POST['api_key'] = 'test_api_key';
        
        ob_start();
        $this->office_addin->handle_test_api_connection();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('success', $output);
    }

    public function testErrorHandlingAndReporting(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock error logging
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock API failure
        \Brain\Monkey\Functions\when('wp_remote_post')->justReturn([
            'response' => ['code' => 500],
            'body' => 'Internal Server Error'
        ]);
        
        $_POST['action'] = 'test_api_connection_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['provider'] = 'openai';
        $_POST['api_key'] = 'invalid_key';
        
        ob_start();
        $this->office_addin->handle_test_api_connection();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('error', $output);
    }

    public function testQueryDocumentsWithPagination(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock performance monitoring
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock database query results
        $mock_posts = [
            (object)['ID' => 1, 'post_title' => 'Document 1', 'post_date' => '2024-01-01'],
            (object)['ID' => 2, 'post_title' => 'Document 2', 'post_date' => '2024-01-02']
        ];
        
        $wpdb->shouldReceive('prepare')->andReturn('prepared_query');
        $wpdb->shouldReceive('get_results')->andReturn($mock_posts);
        $wpdb->shouldReceive('get_var')->andReturn(2); // Total count
        
        $_POST['action'] = 'query_documents_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['page'] = 1;
        $_POST['per_page'] = 10;
        $_POST['search'] = '';
        
        ob_start();
        $this->office_addin->handle_query_documents();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('success', $output);
        $this->assertStringContainsString('Document 1', $output);
    }

    public function testSecurityValidation(): void
    {
        global $wpdb;
        
        // Test without nonce
        $_POST['action'] = 'get_office_addin_settings';
        unset($_POST['nonce']);
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('error', $output);
        $this->assertStringContainsString('nonce', $output);
        
        // Test with invalid nonce
        $this->mockNonce(false);
        $_POST['nonce'] = 'invalid_nonce';
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('error', $output);
        
        // Test without proper capabilities
        $this->mockNonce(true);
        $this->mockUserCan(false);
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('error', $output);
    }

    public function testPerformanceMonitoringIntegration(): void
    {
        global $wpdb;
        
        // Mock all database operations
        $wpdb->shouldReceive('insert')->andReturn(1);
        $wpdb->shouldReceive('get_results')->andReturn([]);
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock settings
        \Brain\Monkey\Functions\when('get_option')->justReturn('test_value');
        
        $_POST['action'] = 'get_office_addin_settings';
        $_POST['nonce'] = 'test_nonce';
        
        ob_start();
        $this->office_addin->handle_get_settings();
        $output = ob_get_clean();
        
        // Verify that performance monitoring was triggered
        $this->assertStringContainsString('success', $output);
    }

    public function testCacheInvalidationOnSettingsUpdate(): void
    {
        global $wpdb;
        
        // Mock cache operations
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        \Brain\Monkey\Functions\when('wp_cache_delete')->justReturn(true);
        \Brain\Monkey\Functions\when('wp_cache_flush_group')->justReturn(true);
        
        // Mock option updates
        \Brain\Monkey\Functions\when('update_option')->justReturn(true);
        \Brain\Monkey\Functions\when('get_option')->justReturn('2'); // Version increment
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        $_POST['action'] = 'update_office_addin_settings';
        $_POST['nonce'] = 'test_nonce';
        $_POST['openai_api_key'] = 'new_api_key';
        
        // Simulate settings update (would normally be in admin panel)
        $cache_manager = $this->getProperty($this->office_addin, 'cache_manager');
        $result = $cache_manager->invalidate('settings');
        
        $this->assertTrue($result);
    }

    public function testConcurrentRequestHandling(): void
    {
        global $wpdb;
        
        // Mock rate limiting for concurrent requests
        $request_count = 0;
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->andReturnUsing(function() use (&$request_count) {
                $request_count++;
                return [
                    'count' => $request_count,
                    'window_start' => time() - 30
                ];
            });
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Simulate multiple concurrent requests
        for ($i = 0; $i < 5; $i++) {
            $_POST['action'] = 'get_office_addin_settings';
            $_POST['nonce'] = 'test_nonce';
            
            ob_start();
            $this->office_addin->handle_get_settings();
            $output = ob_get_clean();
            
            // All should succeed as they're within limits
            $this->assertStringContainsString('success', $output);
        }
    }

    public function testDatabaseTableCreation(): void
    {
        global $wpdb;
        
        // Mock table existence checks and creation
        $wpdb->shouldReceive('get_var')->andReturn(null); // Tables don't exist
        $wpdb->shouldReceive('query')->andReturn(true); // Tables created successfully
        
        // This would normally be called during plugin activation
        $error_reporter = $this->getProperty($this->office_addin, 'error_reporter');
        $performance_monitor = $this->getProperty($this->office_addin, 'performance_monitor');
        
        // Use reflection to call protected methods
        $this->callMethod($error_reporter, 'ensure_table_exists');
        $this->callMethod($performance_monitor, 'ensure_table_exists');
        
        // No exceptions should be thrown
        $this->assertTrue(true);
    }

    public function testApiResponseValidation(): void
    {
        global $wpdb;
        
        // Mock rate limiting - allowed
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        // Mock error logging
        $wpdb->shouldReceive('insert')->andReturn(1);
        
        $this->mockNonce(true);
        $this->mockUserCan(true);
        
        // Mock invalid API response
        \Brain\Monkey\Functions\when('wp_remote_post')->justReturn([
            'response' => ['code' => 200],
            'body' => 'invalid json response'
        ]);
        
        $_POST['action'] = 'test_api_connection_office_addin';
        $_POST['nonce'] = 'test_nonce';
        $_POST['provider'] = 'openai';
        $_POST['api_key'] = 'test_key';
        
        ob_start();
        $this->office_addin->handle_test_api_connection();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('error', $output);
    }

    protected function tearDown(): void
    {
        unset($_POST);
        parent::tearDown();
    }
}
