/* 
 * Mega Tooltip CSS
 * Stili dedicati per il tooltip di visualizzazione estesa dell'analisi
 */

.mega-tooltip-trigger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: #1a4b78; /* Cambiato il colore da arancione a blu scuro per la lente */
    color: white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: none;
    position: absolute;
    left: 15px; /* Posizionato a sinistra */
    top: 15px;
    z-index: 5;
}

.mega-tooltip-trigger:hover {
    background: #0d3a67; /* Blu più scuro al passaggio del mouse */
    transform: scale(1.05);
}

.mega-tooltip-trigger i {
    font-size: 16px; /* Aumentato leggermente la dimensione dell'icona per migliore visibilità */
}

.mega-tooltip-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.7s ease; /* Aumentato da 0.3s a 0.7s per rallentare la transizione */
    overflow: hidden;
}

.mega-tooltip-container.visible {
    display: flex;
    opacity: 1;
}

.mega-tooltip-content {
    position: relative;
    width: 80%;
    max-width: 1200px;
    height: 85%;
    margin: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: fadeZoomIn 0.7s ease forwards; /* Aumentato da 0.3s a 0.7s per rallentare l'animazione */
}

.mega-tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.mega-tooltip-header h2 {
    margin: 0;
    color: #1a4b78;
    font-size: 24px;
    font-weight: 600;
}

.mega-tooltip-close {
    font-size: 24px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #f1f1f1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mega-tooltip-close:hover {
    background: #e0e0e0;
    color: #d32f2f;
}

.mega-tooltip-body {
    flex: 1;
    overflow-y: auto;
    padding: 0 5px;
}

.mega-tooltip-body::-webkit-scrollbar {
    width: 8px;
}

.mega-tooltip-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.mega-tooltip-body::-webkit-scrollbar-thumb {
    background: #d0d0d0;
    border-radius: 4px;
}

.mega-tooltip-body::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

.mega-tooltip-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 15px;
}

.mega-tooltip-tab {
    padding: 8px 16px;
    margin-right: 4px;
    cursor: pointer;
    background-color: #f5f5f5;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
}

.mega-tooltip-tab.active {
    background-color: #1a4b78;
    color: white;
    font-weight: 500;
}

.mega-tooltip-tab:hover:not(.active) {
    background-color: #e0e0e0;
}

.mega-tooltip-tab-content {
    display: none;
    padding: 10px 0;
}

.mega-tooltip-tab-content.active {
    display: block;
}

/* Animazione apertura tooltip */
@keyframes fadeZoomIn {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Stili per visualizzazione mobile */
@media (max-width: 768px) {
    .mega-tooltip-content {
        width: 95%;
        height: 90%;
    }
    
    .mega-tooltip-header h2 {
        font-size: 20px;
    }
}