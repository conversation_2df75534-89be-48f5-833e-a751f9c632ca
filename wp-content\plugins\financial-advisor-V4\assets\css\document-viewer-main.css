/**
 * Document Viewer Widget - Modern ChatGPT-style Theme
 * Stili moderni ispirati al Chat Model Widget per coerenza UI
 */

/* Force horizontal layout for document viewer */
.document-viewer-widget {
    background-color: #ffffff !important;
    border: 1px solid #e5e5e5 !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    margin: 15px 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    min-height: 500px !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Modern header styling - ChatGPT style */
.document-viewer-widget h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    color: #333;
}

/* Stats column (left) - Modern sidebar style */
.document-viewer-widget .stats-column {
    flex: 0 0 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
    background-color: #f7f7f8 !important;
    border-right: 1px solid #e5e5e5 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow-y: auto !important;
    height: 100% !important;
}

.stats-column h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    color: #333;
}

.stats-column h3::before {
    content: "📊";
    margin-right: 8px;
    font-size: 18px;
}

/* Form column (center) - Modern style */
.document-viewer-widget .document-form-column {
    flex: 1 !important;
    min-width: 350px !important;
    background-color: #ffffff !important;
    display: flex !important;
    flex-direction: column !important;
    border-right: 1px solid #e5e5e5 !important;
    height: 100% !important;
}

.document-form-column h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    color: #333;
}

.document-form-column h3::before {
    content: "📄";
    margin-right: 8px;
    font-size: 18px;
}

/* Display column (right) - Modern style */
.document-viewer-widget .document-display-column {
    flex: 1 !important;
    min-width: 350px !important;
    background-color: #ffffff !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

.document-display-column h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    color: #333;
}

.document-display-column h3::before {
    content: "👁️";
    margin-right: 8px;
    font-size: 18px;
}

/* Force horizontal layout on desktop and tablet */
@media (min-width: 769px) {
    .document-viewer-widget {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
    }

    .document-viewer-widget .stats-column {
        flex: 0 0 280px !important;
        border-right: 1px solid #e5e5e5 !important;
        border-bottom: none !important;
    }

    .document-viewer-widget .document-form-column {
        flex: 1 !important;
        border-right: 1px solid #e5e5e5 !important;
        border-bottom: none !important;
    }

    .document-viewer-widget .document-display-column {
        flex: 1 !important;
        border-right: none !important;
        border-bottom: none !important;
    }
}

/* Responsive design: stack columns only on very small screens */
@media (max-width: 768px) {
    .document-viewer-widget {
        flex-direction: column !important;
        min-height: auto;
    }

    .document-viewer-widget .stats-column,
    .document-viewer-widget .document-form-column,
    .document-viewer-widget .document-display-column {
        flex: none !important;
        min-width: 100% !important;
        border-right: none !important;
        border-bottom: 1px solid #e5e5e5 !important;
    }

    .document-viewer-widget .document-display-column {
        border-bottom: none !important;
    }
}

/* Content areas styling */
.stats-column .stats-section,
.document-form-column form,
.document-display-column > div {
    padding: 15px;
}

/* Modern statistics styling */
.stats-column .stats-section {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e5e5;
    margin: 0;
}

.stats-column .stats-section:last-child {
    border-bottom: none;
}

.stats-section-header {
    padding: 12px 15px;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stats-section-content {
    padding: 15px;
}

/* User info styling */
.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f9fafb;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    color: #6b7280;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: #111827;
    margin-bottom: 2px;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
}

/* Stats grid styling */
.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stats-row {
    display: flex;
    gap: 8px;
}

.stats-item {
    flex: 1;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px 8px;
    text-align: center;
    transition: all 0.2s ease;
}

.stats-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.stats-value {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    line-height: 1.2;
}

/* Stats value colors */
.cost-highlight {
    color: #dc2626;
}

.credit-highlight {
    color: #059669;
}

.total-cost-highlight {
    color: #b45309;
}

/* Ensure credit panel is always visible */
.document-viewer-widget .credit-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    margin: 0 !important;
    padding: 0 !important;
}

.document-viewer-widget .credit-row * {
    display: block !important;
    visibility: visible !important;
}

/* Stats info icons and tooltips */
.stats-info-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    background-color: #6b7280;
    color: #ffffff;
    border-radius: 50%;
    font-size: 10px;
    line-height: 14px;
    text-align: center;
    cursor: help;
    position: relative;
}

.stats-info-icon:hover + .stats-tooltip {
    opacity: 1;
    visibility: visible;
}

.stats-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    margin-bottom: 5px;
}

.stats-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

/* Recent analyses styling */
.recent-analyses-list {
    max-height: 200px;
    overflow-y: auto;
}

.recent-analysis-item {
    padding: 8px 12px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 13px;
    color: #6b7280;
}

.recent-analysis-item:last-child {
    border-bottom: none;
}

.recent-analysis-item:hover {
    background-color: #f9fafb;
}

/* Form styling improvements */
.document-form-column .form-row {
    margin-bottom: 15px;
}

.document-form-column label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #374151;
}

.document-form-column input,
.document-form-column textarea,
.document-form-column select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.document-form-column input:focus,
.document-form-column textarea:focus,
.document-form-column select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

/* Button styling improvements */
.document-form-column button {
    background-color: #0073aa;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-right: 8px;
    margin-bottom: 8px;
}

.document-form-column button:hover {
    background-color: #005a87;
}

.document-form-column button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

/* Modern analysis results styling */
.compact-analysis {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-container {
    position: relative;
    margin: 15px 0;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    background-color: #ffffff;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
}

.analysis-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.analysis-controls {
    display: flex;
    gap: 8px;
}

.toggle-view-btn {
    background: none;
    border: 1px solid #d1d5db;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.toggle-view-btn:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

.toggle-view-btn .compress-icon,
.toggle-view-btn .expand-icon {
    font-size: 16px;
    font-weight: bold;
    color: #6b7280;
}

.analysis-content {
    padding: 15px;
    position: relative;
    overflow: hidden;
    max-height: 500px;
    background-color: #ffffff;
}

.analysis-tabs {
    display: flex;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
}

.analysis-tab {
    padding: 12px 16px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    border-right: 1px solid #e5e5e5;
    color: #6b7280;
}

.analysis-tab.active {
    background-color: #ffffff;
    border-bottom: 2px solid #0073aa;
    color: #0073aa;
}

.analysis-tab:hover:not(.active) {
    background-color: #f3f4f6;
    color: #374151;
}

.tab-content {
    display: none;
    padding: 15px;
    background-color: #ffffff;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
    max-height: 400px;
    overflow-y: auto;
}

#analysis-content {
    min-height: 100px;
    overflow-y: auto !important;
    line-height: 1.6;
    max-height: 370px;
    padding-right: 10px;
    color: #374151;
}

.analysis-toggle-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #f7f7f8;
    border: none;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    border-top: 1px solid #e5e5e5;
    color: #6b7280;
    transition: background-color 0.2s ease;
}

.analysis-toggle-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.analysis-summary {
    padding: 12px 15px;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
    font-style: italic;
    color: #6b7280;
    font-size: 14px;
}

/* Custom logo upload section styling */
.form-row {
    margin-bottom: 15px;
}

/* Logo upload styling */
.custom-logo-field {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.custom-logo-field input[type="file"] {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

.custom-logo-field label[for="custom-logo-upload"] {
    display: inline-block;
    padding: 8px 14px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background-color: #1a4b78;
    color: white;
    border-radius: 4px;
}

.custom-logo-field label[for="custom-logo-upload"]:hover {
    background-color: #0d3a67;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

.custom-logo-field label[for="custom-logo-upload"]:first-of-type {
    order: 1;
}

.custom-logo-field label.logo-description {
    background: none;
    box-shadow: none;
    color: #333;
    padding: 0;
    font-weight: normal;
    order: 2;
}

.custom-logo-field label.logo-description:hover {
    background: none;
    box-shadow: none;
    transform: none;
}

.logo-preview-container {
    width: 100%;
    margin-top: 10px;
    order: 3;
}

#logo-preview {
    max-width: 200px;
    max-height: 60px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
}

#logo-dimensions-info {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
}

/* PDF export button styling */
#export-pdf {
    background-color: #d9534f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    margin-top: 10px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#export-pdf:hover {
    background-color: #c9302c;
}

#export-pdf:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="white"><path d="M14,0H2C0.9,0,0,0.9,0,2v12c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V2C16,0.9,15.1,0,14,0z M5,14H3v-2h2V14z M5,10H3V8h2V10z M5,6H3V4h2V6z M9,14H7v-2h2V14z M9,10H7V8h2V10z M9,6H7V4h2V6z M13,14h-2v-2h2V14z M13,10h-2V8h2V10z M13,6h-2V4h2V6z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

/* PDF export section */
.export-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Style for success messages */
.success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    padding: 10px;
    border-radius: 4px;
}

/* Style for error messages */
.error {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 10px;
    border-radius: 4px;
}

/* Basic responsive container */
.document-viewer-widget {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}

/* Chat widget styling */
.chat-widget {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    background-color: #f9f9f9;
}

.chat-messages {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    background-color: white;
    margin-bottom: 15px;
}

.chat-input {
    display: flex;
    gap: 10px;
}

.chat-input textarea {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 60px;
}

.chat-input button {
    align-self: flex-end;
    padding: 8px 15px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.user-message {
    background-color: #e3f2fd;
    padding: 8px 12px;
    border-radius: 18px 18px 0 18px;
    margin: 8px 0;
    align-self: flex-end;
    max-width: 80%;
    margin-left: auto;
}

.bot-message {
    background-color: #f1f1f1;
    padding: 8px 12px;
    border-radius: 18px 18px 18px 0;
    margin: 8px 0;
    align-self: flex-start;
    max-width: 80%;
}

#chat-typing, #settings-chat-typing {
    font-style: italic;
    color: #666;
    margin: 5px 0;
    display: none;
}

.dots {
    display: inline-block;
    width: 20px;
    text-align: left;
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 33% { content: '.'; }
    34%, 66% { content: '..'; }
    67%, 100% { content: '...'; }
}

/* Document frame container with flex layout */
#document-display {
    border: 1px solid #ddd;
    margin-bottom: 15px;
    cursor: grab;
    overflow: hidden !important; /* Impostiamo hidden con !important per avere priorità massima */
    position: relative;
    height: 100% !important; /* Imposta l'altezza al 100% del contenitore */
    min-height: 400px; /* Altezza minima ridotta per adattarsi meglio al form */
    max-height: 800px; /* Limita l'altezza massima */
    width: 100%;
    display: flex; /* Garantisce che l'iframe si adatti correttamente */
    align-items: center; /* Centra verticalmente il contenuto */
    justify-content: center; /* Centra orizzontalmente il contenuto */
    background-color: #f5f5f5; /* Sfondo leggermente più visibile */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#document-display:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

#document-display.focus-view {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

#document-display.grabbing {
    cursor: grabbing;
}

/* Stile migliorato per l'iframe che contiene il documento */
#document-frame {
    border: none;
    width: 100%;
    height: 100%;
    transform-origin: top left;
    display: block; /* Garantisce che l'iframe si comporti come un blocco */
    background-color: white; /* Sfondo bianco per il documento */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* Ombra leggera per dare profondità */
    
}

/* Simulazione di un foglio A4 nel visualizzatore */
.a4-container {
    position: relative;
    width: calc(100% - 20px); /* Larghezza leggermente ridotta per mostrare i bordi */
    height: 100%;
    max-width: 595px; /* Larghezza standard A4 in pixel (72dpi) */
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Zoom controls migliorati */
.zoom-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background: rgba(255,255,255,0.9);
    border-radius: 5px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.zoom-btn {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: #e1e1e1;
    transform: scale(1.05);
}

/* Responsive adjustments for A4 paper size */
@media (max-width: 992px) {
    #document-display {
        max-height: 700px;
    }
}

@media (max-width: 768px) {
    #document-display {
        max-height: 600px;
    }
}

@media (max-width: 600px) {
    #document-display {
        max-height: 500px;
    }
    
    .zoom-controls {
        top: 5px;
        right: 5px;
    }
    
    .zoom-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
}

/* Zoom controls */
.zoom-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.zoom-btn {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
}

.zoom-btn:hover {
    background: #e1e1e1;
}

/* Analysis form styling */
.analysis-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.analysis-form label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

.analysis-form textarea,
.analysis-form input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.analysis-form button {
    padding: 8px 15px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    align-self: flex-start;
}

.analysis-form button:hover {
    background-color: #005177;
}

/* Document controller */
.document-controller {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.document-actions button {
    background-color: #f7f7f7;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.document-actions button:hover {
    background-color: #f0f0f0;
}

/* Enhanced media query support */
@media (max-width: 768px) {
    .document-controller {
        flex-direction: column;
    }
    
    .analysis-form button {
        width: 100%;
    }
    
    .export-actions {
        flex-direction: column;
    }
    
    .chat-input {
        flex-direction: column;
    }
    
    .chat-input button {
        align-self: stretch;
    }
}

/* Add responsive styles for the document viewer */
@media (max-width: 600px) {
    #document-display {
        height: 500px !important; /* Force a reasonable height on small screens */
    }
    
    .zoom-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        background: rgba(255,255,255,0.8);
        border-radius: 5px;
        padding: 5px;
    }
}

/* Button styling - look & feel migliorato */
.document-form-column button {
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-right: 8px;
    margin-bottom: 8px;
}

/* Stile default per tutti i pulsanti - blu scuro */
#analyze-description, #export-pdf, button[type="button"]:not(#clear-document) {
    background-color: #1a4b78; /* Blu scuro */
    color: white;
}

#analyze-description:hover, #export-pdf:hover, button[type="button"]:not(#clear-document):hover {
    background-color: #0d3a67; /* Blu più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

/* Stile specifico per il pulsante Clear - rosso */
#clear-document {
    background-color: #d63638; /* Rosso */
    color: white;
}

#clear-document:hover {
    background-color: #b32d2e; /* Rosso più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

/* Stile per i pulsanti attivi/premuti */
.document-form-column button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Stile per i pulsanti disabilitati */
.document-form-column button:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
    box-shadow: none;
}

/* Aggiunta di icone ai pulsanti principali usando Unicode */
#analyze-description:before {
    content: "🔍";
    margin-right: 6px;
}

#export-pdf:before {
    content: "📄";
    margin-right: 6px;
}

#clear-document:before {
    content: "❌";
    margin-right: 6px;
}

/* Override per la sezione export-actions */
.export-actions button {
    background-color: #1a4b78 !important;
    color: white !important;
}

.export-actions button:hover {
    background-color: #0d3a67 !important;
}

/* Miglioramento della leggibilità */
.document-form-column button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Styling per i pulsanti di input file per uniformarli allo stile dei pulsanti blu */
input[type="file"] {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

input[type="file"] + label {
    display: inline-block;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-right: 8px;
    margin-bottom: 8px;
    background-color: #1a4b78; /* Blu scuro come gli altri pulsanti */
    color: white;
}

input[type="file"] + label:hover {
    background-color: #0d3a67; /* Blu più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

input[type="file"] + label:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Aggiunge l'icona al pulsante di upload */
input[type="file"] + label:before {
    content: "📎";
    margin-right: 6px;
}

/* Stile per il messaggio di estrazione completata */
.extraction-success {
    background-color: #e8f5e9;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: fadeInUp 0.5s ease;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.extraction-success p {
    margin: 5px 0;
}

.extraction-success strong {
    color: #2e7d32;
}

/* Animazione per il messaggio di estrazione */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stile per l'area informazioni documento */
#document-info {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    font-size: 13px;
    display: none; /* Nascosto inizialmente */
}

#document-info p {
    margin: 5px 0;
    line-height: 1.4;
}

/* Stile per il pannello di notifica */
#document-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    max-width: 300px;
    opacity: 0;
    transform: translateY(-20px);
    animation: notificationFade 0.3s forwards;
}

#document-notification h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

#document-notification p {
    margin: 0;
    font-size: 14px;
}

@keyframes notificationFade {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ottimizzazione delle notifiche per scomparire gradualmente */
.document-notification-area {
    background-color: #f8f9f8;
    border-radius: 4px;
    margin-bottom: 15px;
    padding: 10px 15px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    font-size: 14px;
    line-height: 1.5;
    animation: fadein 0.5s;
    transition: opacity 0.3s ease, transform 0.3s ease;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    opacity: 0.95;
}

.document-notification-area.error {
    border-left: 4px solid #f44336;
    background-color: #fff8f8;
}

.document-notification-area.processing {
    border-left: 4px solid #2196F3;
    background-color: #f6f9ff;
}

@keyframes fadein {
    from { opacity: 0; transform: translateY(-20px); }
    to   { opacity: 1; transform: translateY(0); }
}

@keyframes fadeout {
    from { opacity: 1; transform: translateY(0); }
    to   { opacity: 0; transform: translateY(-20px); }
}

.notification-content {
    position: relative;
}

/* Spinner for processing notifications */
.notification-content .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid rgba(0,0,0,0.1);
    border-top-color: #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Area informazioni inline con transizione a scomparsa */
#document-info-inline {
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9em;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    transition: opacity 0.5s ease;
}

#document-info-inline.fadeout {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

/* Save result message styling */
.save-result-message {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    display: none;
}

.save-result-message.success {
    display: block;
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #4CAF50;
    animation: fadeIn 0.5s ease;
}

.save-result-message.error {
    display: block;
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Stile specifico per il pulsante Salva Analisi - verde scuro */
#save-analysis {
    background-color: #2e7d32 !important; /* Verde scuro */
    color: white !important;
    padding: 10px 16px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#save-analysis:hover {
    background-color: #1b5e20 !important; /* Verde più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

#save-analysis:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Aggiunta di icona al pulsante Salva Analisi */
#save-analysis:before {
    content: "💾";
    margin-right: 6px;
}

/* Stile per il menu a tendina delle richieste predefinite */
.preset-queries-container {
    position: relative;
    margin-bottom: 10px;
    width: 100%;
}

.preset-queries-select {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-queries-select:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.preset-queries-select:focus {
    outline: none;
    border-color: #1a4b78;
    box-shadow: 0 0 5px rgba(26, 75, 120, 0.3);
}

.preset-info-tip {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #1a4b78;
    color: white;
    text-align: center;
    font-size: 12px;
    line-height: 18px;
    cursor: help;
    pointer-events: none;
}

/* Animazione per la visualizzazione delle richieste nel textarea */
@keyframes highlightTextarea {
    0% { background-color: #f8f9f8; }
    20% { background-color: #e3f2fd; }
    100% { background-color: #f8f9f8; }
}

.textarea-highlight {
    animation: highlightTextarea 1.5s ease;
}

/* Stile responsive per i dispositivi mobili */
@media (max-width: 768px) {
    .preset-queries-select {
        font-size: 13px;
        padding: 8px;
    }
    
    .preset-info-tip {
        width: 16px;
        height: 16px;
        line-height: 16px;
        font-size: 10px;
    }
}

/* Dynamic styles moved from shortcode inline styling */
.document-viewer-widget {
    max-width: 100%;
}

#document-frame {
    width: 100%;
}

/* File upload with inline info */
.file-upload-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.file-upload-input {
    flex: 0 0 auto;
}

.document-info-inline {
    flex: 1;
    font-size: 0.9em;
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
    margin-left: 10px;
}

.document-info-inline span {
    margin-right: 12px;
    white-space: nowrap;
}

/* Optional: Custom instance-specific branding */
.document-viewer-widget h3 {
    color: #2c3e50;
}

/* Enhanced styling for the instructions panel */
.instructions-panel {
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 3px solid #0073aa;
    font-size: 0.9em;
}

.instructions-panel h4 {
    margin-top: 0;
    color: #0073aa;
}

.instructions-panel ol {
    padding-left: 25px;
    margin-bottom: 0;
}

.instructions-panel li {
    margin-bottom: 5px;
}

/* Spinner per indicazione caricamento */
.analysis-loading-indicator {
    text-align: center;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #eee;
    margin: 20px 0;
}

.analysis-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

.analysis-wait-message {
    font-style: italic;
    color: #777;
    font-size: 0.9em;
    margin-top: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* L'indicatore di caricamento viene visualizzato quando l'analisi è in corso */
.analysis-loading-indicator p {
    margin: 5px 0;
    font-weight: 500;
    color: #333;
}

/* Effetto Blink per i campi che vengono aggiornati dopo caricamento documento */
.blink-effect {
    animation: blink-animation 4s ease-in-out;
    background-color: rgba(220, 255, 220, 0.9) !important; /* Sfondo verdino più soft */
    box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    border-color: #8cd98c !important; /* Bordo verde chiaro */
    transition: background-color 2s ease-out, box-shadow 2s ease-out, border-color 2s ease-out;
}

@keyframes blink-animation {
    0% {
        background-color: rgba(220, 255, 220, 0.9);
        box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    }
    25% {
        background-color: rgba(200, 255, 200, 0.95);
        box-shadow: 0 0 12px rgba(100, 200, 100, 0.8);
    }
    50% {
        background-color: rgba(180, 255, 180, 1);
        box-shadow: 0 0 15px rgba(80, 200, 80, 0.9);
    }
    75% {
        background-color: rgba(200, 255, 200, 0.95);
        box-shadow: 0 0 12px rgba(100, 200, 100, 0.8);
    }
    100% {
        background-color: rgba(220, 255, 220, 0.9);
        box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    }
}

/* Miglioramento stile per il campo token */
#document-tokens-inline {
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
    transition: all 0.3s ease;
}

/* Miglioramento stile per il menu domande preset */
.preset-queries-select {
    transition: all 0.3s ease;
    border-radius: 4px;
}