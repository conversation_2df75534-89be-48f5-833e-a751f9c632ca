<?php
/**
 * Test Page per Chat Widget
 * 
 * Testa il widget chat e verifica che le categorie vengano caricate correttamente
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    // Se non siamo in WordPress, includiamo wp-config.php
    $wp_config_path = dirname(dirname(dirname(dirname(dirname(__FILE__))))) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once $wp_config_path;
    } else {
        die('WordPress non trovato. Esegui questo script dal browser.');
    }
}

// Enqueue degli script necessari
add_action('wp_enqueue_scripts', function() {
    wp_enqueue_script('jquery');
    
    // Enqueue script del chat widget
    if (class_exists('Chat_Model_Widget')) {
        $widget = new Chat_Model_Widget();
        $widget->enqueue_scripts();
    }
});

// Esegui enqueue
do_action('wp_enqueue_scripts');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Chat Widget</title>
    <?php wp_head(); ?>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f1f1f1;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .debug-panel h3 {
            margin-top: 0;
            color: #495057;
        }
        .debug-output {
            background: #212529;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005177;
        }
        
        /* Stili per il widget chat */
        .chat-widget {
            border: 2px solid #0073aa;
            border-radius: 8px;
            margin: 20px 0;
            background: white;
        }
        
        .loading-categories {
            padding: 20px;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .loading-categories.error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            padding: 20px;
        }
        
        .category-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-card:hover {
            border-color: #0073aa;
            box-shadow: 0 2px 8px rgba(0, 115, 170, 0.2);
        }
        
        .category-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .category-name {
            font-weight: bold;
            margin: 10px 0 5px 0;
            color: #2c3e50;
        }
        
        .category-count {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Chat Widget - Categorie</h1>
        
        <div class="test-info">
            <strong>Istruzioni:</strong>
            <ul>
                <li>Questa pagina testa il caricamento delle categorie nel widget chat</li>
                <li>Le categorie dovrebbero apparire automaticamente nel widget qui sotto</li>
                <li>Controlla la console del browser per eventuali errori JavaScript</li>
                <li>Usa il pannello debug per testare le chiamate AJAX manualmente</li>
            </ul>
        </div>
        
        <div class="debug-panel">
            <h3>🔧 Pannello Debug</h3>
            <button class="test-button" onclick="testCategoryStats()">Test get_category_stats</button>
            <button class="test-button" onclick="testAcademyQuestions()">Test get_academy_questions</button>
            <button class="test-button" onclick="reloadCategories()">Ricarica Categorie</button>
            <button class="test-button" onclick="clearDebugOutput()">Pulisci Output</button>
            
            <div id="debug-output" class="debug-output" style="margin-top: 15px; display: none;">
                <div id="debug-content"></div>
            </div>
        </div>
        
        <!-- Widget Chat di Test -->
        <div class="chat-widget" data-api-endpoint="openai" data-api-model="gpt-3.5-turbo">
            <h2 style="padding: 20px; margin: 0; background: #0073aa; color: white; border-radius: 6px 6px 0 0;">
                💬 Chat Widget - Test Categorie
            </h2>
            
            <!-- Academy Mega Menu -->
            <div class="academy-mega-menu">
                <div class="categories-grid">
                    <!-- Le categorie verranno caricate qui via JavaScript -->
                </div>
            </div>
            
            <!-- Financial Questions Menu (overlay) -->
            <div class="financial-questions-menu" style="display: none;">
                <div class="questions-header">
                    <button class="back-to-categories">← Torna alle categorie</button>
                    <h3 class="category-title"></h3>
                </div>
                <div class="questions-list">
                    <!-- Le domande verranno caricate qui -->
                </div>
            </div>
            
            <!-- Chat Input Area -->
            <div style="padding: 20px; border-top: 1px solid #ddd;">
                <div style="display: flex; gap: 10px;">
                    <input type="text" class="chat-input" placeholder="Scrivi la tua domanda..." style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <button class="send-btn test-button">Invia</button>
                </div>
            </div>
            
            <!-- Chat Log -->
            <div class="chat-log" style="height: 200px; overflow-y: auto; padding: 20px; background: #f8f9fa; border-top: 1px solid #ddd;">
                <div class="message system-message">
                    <strong>Sistema:</strong> Widget chat inizializzato. Le categorie dovrebbero caricarsi automaticamente sopra.
                </div>
            </div>
        </div>
        
        <div class="debug-panel">
            <h3>📊 Informazioni Sistema</h3>
            <div id="system-info">
                <p><strong>chatWidgetParams disponibile:</strong> <span id="chat-params-status">Verificando...</span></p>
                <p><strong>documentViewerParams disponibile:</strong> <span id="doc-params-status">Verificando...</span></p>
                <p><strong>AJAX URL:</strong> <span id="ajax-url">Verificando...</span></p>
                <p><strong>Nonce:</strong> <span id="nonce-status">Verificando...</span></p>
                <p><strong>Widget trovati:</strong> <span id="widgets-found">Verificando...</span></p>
            </div>
        </div>
    </div>

    <script>
        // Funzioni di debug
        function debugLog(message, data = null) {
            const debugOutput = document.getElementById('debug-output');
            const debugContent = document.getElementById('debug-content');
            
            debugOutput.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '10px';
            logEntry.style.borderBottom = '1px solid #495057';
            logEntry.style.paddingBottom = '5px';
            
            let content = `[${timestamp}] ${message}`;
            if (data) {
                content += '\n' + JSON.stringify(data, null, 2);
            }
            
            logEntry.textContent = content;
            debugContent.appendChild(logEntry);
            
            // Scroll to bottom
            debugOutput.scrollTop = debugOutput.scrollHeight;
            
            // Also log to console
            console.log(message, data);
        }
        
        function clearDebugOutput() {
            document.getElementById('debug-content').innerHTML = '';
            document.getElementById('debug-output').style.display = 'none';
        }
        
        function testCategoryStats() {
            debugLog('Testing get_category_stats...');
            
            const ajaxUrl = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.ajaxUrl : '/wp-admin/admin-ajax.php');
            const nonce = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.nonce : '');
            
            jQuery.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'get_category_stats',
                    nonce: nonce
                },
                success: function(response) {
                    debugLog('get_category_stats SUCCESS:', response);
                },
                error: function(xhr, status, error) {
                    debugLog('get_category_stats ERROR:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });
                }
            });
        }
        
        function testAcademyQuestions() {
            debugLog('Testing get_academy_questions...');
            
            const ajaxUrl = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.ajaxUrl : '/wp-admin/admin-ajax.php');
            const nonce = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.nonce : '');
            
            jQuery.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'get_academy_questions',
                    category: 'general',
                    nonce: nonce
                },
                success: function(response) {
                    debugLog('get_academy_questions SUCCESS:', response);
                },
                error: function(xhr, status, error) {
                    debugLog('get_academy_questions ERROR:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });
                }
            });
        }
        
        function reloadCategories() {
            debugLog('Reloading categories manually...');
            
            const $widget = jQuery('.chat-widget');
            if ($widget.length > 0 && typeof window.loadCategories === 'function') {
                window.loadCategories($widget);
            } else {
                debugLog('ERROR: Widget not found or loadCategories function not available');
            }
        }
        
        // Verifica informazioni sistema al caricamento
        jQuery(document).ready(function($) {
            debugLog('Page loaded, checking system info...');
            
            // Verifica parametri disponibili
            $('#chat-params-status').text(typeof chatWidgetParams !== 'undefined' ? 'SÌ' : 'NO');
            $('#doc-params-status').text(typeof documentViewerParams !== 'undefined' ? 'SÌ' : 'NO');
            
            // Mostra AJAX URL
            const ajaxUrl = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.ajaxUrl : 
                           (typeof documentViewerParams !== 'undefined' ? documentViewerParams.ajaxUrl : '/wp-admin/admin-ajax.php'));
            $('#ajax-url').text(ajaxUrl);
            
            // Mostra nonce
            const nonce = (typeof chatWidgetParams !== 'undefined' ? chatWidgetParams.nonce : 
                         (typeof documentViewerParams !== 'undefined' ? documentViewerParams.nonce : 'N/A'));
            $('#nonce-status').text(nonce ? 'Disponibile' : 'Non disponibile');
            
            // Conta widget trovati
            const widgetsFound = $('.chat-widget').length;
            $('#widgets-found').text(widgetsFound);
            
            debugLog('System info updated', {
                chatWidgetParams: typeof chatWidgetParams !== 'undefined',
                documentViewerParams: typeof documentViewerParams !== 'undefined',
                ajaxUrl: ajaxUrl,
                nonce: nonce,
                widgetsFound: widgetsFound
            });
            
            // Attendi un po' e poi verifica se le categorie sono state caricate
            setTimeout(function() {
                const categoriesGrid = $('.categories-grid');
                const categoriesContent = categoriesGrid.html().trim();
                
                debugLog('Categories check after 3 seconds', {
                    gridFound: categoriesGrid.length > 0,
                    hasContent: categoriesContent.length > 0,
                    content: categoriesContent
                });
                
                if (categoriesContent.includes('loading-categories')) {
                    debugLog('WARNING: Categories still loading after 3 seconds');
                } else if (categoriesContent.includes('category-card')) {
                    debugLog('SUCCESS: Categories loaded successfully');
                } else {
                    debugLog('ERROR: No categories found and no loading indicator');
                }
            }, 3000);
        });
    </script>

    <?php wp_footer(); ?>
</body>
</html>
