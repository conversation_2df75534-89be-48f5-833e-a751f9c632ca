<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Widget per la chat con modelli AI
 */
class Chat_Model_Widget extends WP_Widget {
    private $api_status = null;

    public function __construct() {
        parent::__construct(
            'chat_model_widget',
            __('Chat Model Widget', 'document-viewer-plugin'),
            array('description' => __('A widget to chat with AI models.', 'document-viewer-plugin'))
        );
        // Check API configuration on widget initialization
        $this->check_api_configuration();
        
        // Register and enqueue widget specific scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles for the widget
     */
    public function enqueue_scripts() {
        // Register and enqueue the Chat Model Widget CSS
        wp_register_style(
            'chat-model-widget-style',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/chat-model-widget.css',
            array(),
            DOCUMENT_ADVISOR_VERSION
        );
        wp_enqueue_style('chat-model-widget-style');
        
        // Register and enqueue the Chat Model Widget JavaScript
        wp_register_script(
            'chat-model-widget-script',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/chat-model-widget.js',
            array('jquery'),
            DOCUMENT_ADVISOR_VERSION,
            true
        );
        
        // Make sure we have access to documentViewerParams for AJAX URL and nonce
        // This will share the same nonce and URL with the document viewer
        wp_enqueue_script('chat-model-widget-script');
    }

    private function check_api_configuration() {
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            $this->api_status = 'unconfigured';
            return false;
        }
        $this->api_status = 'configured';
        return true;
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Show configuration notice if API is not configured
        if ($this->api_status === 'unconfigured') {
            // Mostra il messaggio di configurazione solo agli amministratori
            if (current_user_can('manage_options')) {
                echo '<div class="notice notice-warning"><p>';
                echo __('Chat Model requires API configuration. ', 'document-viewer-plugin');
                echo '<a href="' . admin_url('admin.php?page=document-viewer-settings') . '">';
                echo __('Configure API Settings', 'document-viewer-plugin');
                echo '</a>';
                echo '</p></div>';
            } else {
                // Per gli utenti normali, mostra un messaggio generico
                echo '<div class="notice notice-info"><p>';
                echo __('Chat temporarily unavailable. Please try again later.', 'document-viewer-plugin');
                echo '</p></div>';
            }
            return;
        }

        // Add API configuration data to chat widget
        // Security note: We don't pass the API key directly to the client to avoid exposing it
        // The API key will be handled securely by the server-side AJAX handler
        echo '<div class="chat-widget" 
                   data-api-endpoint="' . esc_attr(get_option('document_viewer_api_endpoint')) . '"
                   data-api-model="' . esc_attr(get_option('document_viewer_model')) . '">
            <h3>' . (!empty($instance['custom_title']) ? esc_html($instance['custom_title']) : __('Chat con AI', 'document-viewer-plugin')) . '</h3>
            <div class="chat-form">
                <div class="chat-log"></div>
                <div class="input-container">
                    <div class="academy-icon">A</div>
                    <div class="input-wrapper">
                        <input type="text" class="chat-input" placeholder="' . __('Scrivi un messaggio...', 'document-viewer-plugin') . '">
                    </div>
                    <button class="send-btn" aria-label="' . __('Invia messaggio', 'document-viewer-plugin') . '"></button>
                </div>
            </div>
        </div>';
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Chat with AI', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" 
                   type="text" value="<?php echo esc_attr($title); ?>">
        </p>

        <?php

        // Add API status indicator in widget admin
        if ($this->api_status === 'unconfigured') {
            echo '<p class="description" style="color: #d63638;">';
            echo __('API not configured. Please check the settings.', 'document-viewer-plugin');
            echo '</p>';
        } else {
            echo '<p class="description" style="color: #00a32a;">';
            echo __('API configured and ready.', 'document-viewer-plugin');
            echo '</p>';
        }
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}