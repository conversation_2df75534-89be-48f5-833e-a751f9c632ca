/**
 * Document Stats Panel
 * Professional styling for document statistics in the viewer widget
 * Consistent with subscription management design system
 */

/* Loading spinner */
.document-viewer-stats-container .spinner-inline {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Costo stimato - Stile migliorato */
.document-viewer-stats-container #cost-estimate,
.document-viewer-stats-container #estimated-cost {
    display: inline-block;
    min-width: 60px;
    text-align: right;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.2em;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
    transition: all 0.2s ease;
}

/* Classe per nascondere elementi */
.hidden {
    display: none !important;
}

/* Main stats container - Modern style */
.document-viewer-stats-container .stats-column {
    background-color: #ffffff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    flex: none;
    width: 100%;
    min-width: auto;
    max-width: none;
    box-sizing: border-box;
    margin: 0;
    overflow: visible;
    transition: none;
}

.document-viewer-stats-container .stats-column:hover {
    box-shadow: none;
}

/* Stats sections - Modern style */
.document-viewer-stats-container .stats-section {
    background-color: #ffffff;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 0;
    overflow: hidden;
    transition: none;
}

.document-viewer-stats-container .stats-section:hover {
    border-color: #e5e5e5;
}

.document-viewer-stats-container .stats-section:last-child {
    border-bottom: none;
}

.document-viewer-stats-container .stats-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
    cursor: pointer;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
}

.document-viewer-stats-container .stats-section-header:hover {
    background-color: #f3f4f6;
}

.document-viewer-stats-container .stats-section-content {
    padding: 15px;
    background-color: #ffffff;
    border-radius: 0;
    transition: none;
    position: relative;
    z-index: 1;
    overflow: visible;
}

/* Modern stats grid */
.document-viewer-stats-container .stats-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 0;
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

/* Modern stats row layout */
.document-viewer-stats-container .stats-row {
    display: flex;
    gap: 8px;
    width: 100%;
    overflow: visible;
    margin-bottom: 0;
}

.document-viewer-stats-container .usage-row,
.document-viewer-stats-container .costs-row,
.document-viewer-stats-container .credit-row,
.document-viewer-stats-container .total-cost-row {
    width: 100%;
}

/* Modern stats items styling */
.document-viewer-stats-container .costs-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.document-viewer-stats-container .total-cost-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.document-viewer-stats-container .credit-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px 8px;
}

/* Modern stats items */
.document-viewer-stats-container .stats-item {
    position: relative;
    flex: 1;
    background-color: #ffffff;
    border-radius: 6px;
    padding: 12px 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    text-align: center;
    box-shadow: none;
}

.document-viewer-stats-container .stats-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern stats values */
.document-viewer-stats-container .stats-value {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 4px 0;
    line-height: 1.2;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Modern logout button style */
.document-viewer-stats-container .logout-button,
.document-viewer-stats-container .fa-logout-btn {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: auto;
    margin-top: 8px;
}

.document-viewer-stats-container .logout-button:hover,
.document-viewer-stats-container .fa-logout-btn:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-viewer-stats-container .logout-button:active,
.document-viewer-stats-container .fa-logout-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.document-viewer-stats-container .logout-button .dashicons,
.document-viewer-stats-container .fa-logout-btn .dashicons {
    margin-right: 6px;
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Stats labels */
.document-viewer-stats-container .stats-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 8px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

/* Info icon and tooltip */
.document-viewer-stats-container .stats-info-icon {
    position: relative;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    margin-left: 6px;
    cursor: help;
    border: none;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.document-viewer-stats-container .stats-info-icon:hover {
    background: #0073aa;
    color: #fff;
}

/* Tooltip styling */
.document-viewer-stats-container .stats-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-8px);
    background: #2c3e50;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: normal;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
}

.document-viewer-stats-container .stats-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #2c3e50 transparent transparent transparent;
}

.document-viewer-stats-container .stats-info-icon:hover .stats-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-12px);
}

/* CORREZIONE: tooltip con posizionamento absolute ma con z-index elevato */
.document-viewer-stats-container .stats-tooltip {
    position: absolute;
    background: rgba(51, 51, 51, 0.95);
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: normal;
    width: 180px;
    z-index: 9999;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateX(-50%);
    bottom: 130%;
    left: 50%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
    margin: 0 auto;
    text-align: center;
    max-width: calc(100vw - 40px);
    word-break: normal;
    word-wrap: break-word;
}

/* CORREZIONE: freccia del tooltip - posizionata SOTTO il tooltip puntando verso l'icona */
.document-viewer-stats-container .stats-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(51, 51, 51, 0.95);
}

/* Stats values styling */
.document-viewer-stats-container .stats-value {
    font-size: 19.2px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

/* Colori personalizzati per valori - MINIMALI */
.document-viewer-stats-container .cost-highlight {
    color: #e67e22;
}

.document-viewer-stats-container .total-cost-highlight {
    color: #d35400;
    font-weight: 500;
    font-size: 16px;
}

.document-viewer-stats-container .credit-highlight {
    color: #27ae60;
    font-size: 20px;
}

/* Effetto di aggiornamento per i valori */
.document-viewer-stats-container .highlight-update {
    animation: highlight-update 2s ease-out 1;
    font-weight: bold;
}

@keyframes highlight-update {
    0% { background-color: rgba(255, 222, 173, 0.9); color: #d35400; }
    50% { background-color: rgba(255, 222, 173, 0.6); }
    100% { background-color: transparent; color: inherit; }
}

/* Date info - RIMOSSA */

/* Pulsante aggiornamento - MINIMALE */
.document-viewer-stats-container .refresh-stats-btn {
    background: #f5f5f5;
    border: none;
    padding: 5px 8px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 6px;
    font-size: 12px;
    display: block;
    width: 100%;
    color: #555;
    text-align: center;
    transition: all 0.2s ease;
}

.document-viewer-stats-container .refresh-stats-btn:hover {
    background: #e0e0e0;
}

/* Icona toggle - MINIMALE */
.toggle-icon {
    width: 12px;
    height: 12px;
    position: relative;
    transition: transform 0.3s;
}

.toggle-icon:before, .toggle-icon:after {
    content: '';
    position: absolute;
    background-color: #777;
    transition: transform 0.3s;
}

.toggle-icon:before {
    width: 2px;
    height: 12px;
    top: 0;
    left: 5px;
}

.toggle-icon:after {
    width: 12px;
    height: 2px;
    top: 5px;
    left: 0;
}

.toggle-icon.expanded:after {
    opacity: 0;
    transform: rotate(90deg);
}

.toggle-icon.collapsed:before {
    transform: rotate(90deg);
}

/* Analisi recenti - MINIMALE */
.recent-analyses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-top: 6px;
}

.recent-analysis-item {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.recent-analysis-item:last-child {
    border-bottom: none;
}

.recent-analysis-item:hover {
    background-color: #f5f5f5;
}

.analysis-item-title {
    font-weight: 500;
    margin-bottom: 2px;
    color: #333;
}

.analysis-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #777;
}

.no-analyses {
    font-style: italic;
    color: #999;
    text-align: center;
    padding: 10px 0;
    font-size: 12px;
}

/* Stati di loading e errore - MINIMALE */
.stats-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #777;
    flex-direction: column;
    font-size: 12px;
}

.stats-spinner {
    border: 2px solid #f5f5f5;
    border-radius: 50%;
    border-top: 2px solid #999;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

/* Animazione spin già definita all'inizio del file */

.stats-error {
    color: #e74c3c;
    padding: 10px;
    text-align: center;
    background-color: #fef5f5;
    border-radius: 3px;
    font-size: 12px;
}

#retry-stats-btn {
    margin-top: 6px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

#retry-stats-btn:hover {
    background-color: #e6e6e6;
}

/* Responsività */
@media (max-width: 768px) {
    .document-viewer-stats-container .stats-row {
        flex-direction: column;
    }

    .document-viewer-stats-container .stats-tooltip {
        width: 160px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
        bottom: 130%;
        max-width: calc(100vw - 20px);
    }
}

/* IMPORTANTE: Nasconde completamente la card spesa totale nel document viewer widget */
.document-viewer-stats-container .total-cost-row {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* IMPORTANTE: Nasconde completamente le card analisi e token nel document viewer widget */
.document-viewer-stats-container .usage-row {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Assicura che anche gli elementi figli siano nascosti */
.document-viewer-stats-container .total-cost-row *,
.document-viewer-stats-container .usage-row * {
    display: none !important;
    visibility: hidden !important;
}

/* Usa valori opzionali per il tema */
:root {
    --stats-border-color: #eaeaea;
    --stats-highlight-bg: #fafafa;
    --stats-cost-color: #d35400;
    --stats-bg-color: #ffffff;
    --stats-text-color: #333333;
    --stats-accent-color: #27ae60;
}

/* Stile minimal per la user-info - RIMOSSO DUPLICATO */

/* Highlight animation per elementi aggiornati */
@keyframes flash-animation {
    0% { background-color: rgba(255, 240, 165, 0); }
    20% { background-color: rgba(255, 240, 165, 0.5); }
    80% { background-color: rgba(255, 240, 165, 0.3); }
    100% { background-color: rgba(255, 240, 165, 0); }
}

/* Stili per il pulsante di logout nel pannello statistiche - RIMOSSO (duplicato) */

/* Consolidato: tutti gli stili per stats-logout-link */
.stats-logout-link {
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    color: #555;
    padding: 5px 10px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 12px;
}

.stats-logout-link:hover {
    background-color: #e4e4e4;
    text-decoration: none;
    color: #d63638;  /* Colore rosso per indicare logout */
}

/* Stato di logout in corso */
.stats-logout-link.logging-out {
    pointer-events: none;
    opacity: 0.7;
    background-color: #eaeaea;
}

.stats-logout-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 3px;
}

/* Stili per la testata del pannello statistiche con utente e tipo sottoscrizione - CONSOLIDATO */

#user-avatar {
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e5e5e5;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #555;
    background-color: #e5e5e5;
}

#user-info {
    flex: 1;
}

#user-name {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

#user-subscription-type {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}