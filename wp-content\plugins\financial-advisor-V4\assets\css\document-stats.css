/**
 * Document Stats Panel
 * Professional styling for document statistics in the viewer widget
 * Consistent with subscription management design system
 */

/* Loading spinner */
.document-viewer-stats-container .spinner-inline {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Costo stimato - Stile migliorato */
.document-viewer-stats-container #cost-estimate,
.document-viewer-stats-container #estimated-cost {
    display: inline-block;
    min-width: 60px;
    text-align: right;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.2em;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
    transition: all 0.2s ease;
}

/* Classe per nascondere elementi */
.hidden {
    display: none !important;
}

/* Main stats container - Modern style */
.document-viewer-stats-container .stats-column {
    background-color: #ffffff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    flex: none;
    width: 100%;
    min-width: auto;
    max-width: none;
    box-sizing: border-box;
    margin: 0;
    overflow: visible;
    transition: none;
}

.document-viewer-stats-container .stats-column:hover {
    box-shadow: none;
}

/* Stats sections - Modern style */
.document-viewer-stats-container .stats-section {
    background-color: #ffffff;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 0;
    overflow: hidden;
    transition: none;
}

.document-viewer-stats-container .stats-section:hover {
    border-color: #e5e5e5;
}

.document-viewer-stats-container .stats-section:last-child {
    border-bottom: none;
}

.document-viewer-stats-container .stats-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f7f7f8;
    border-bottom: 1px solid #e5e5e5;
    cursor: pointer;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
}

.document-viewer-stats-container .stats-section-header:hover {
    background-color: #f3f4f6;
}

.document-viewer-stats-container .stats-section-content {
    padding: 15px;
    background-color: #ffffff;
    border-radius: 0;
    transition: none;
    position: relative;
    z-index: 1;
    overflow: visible;
}

/* Modern stats grid */
.document-viewer-stats-container .stats-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 0;
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

/* Modern stats row layout */
.document-viewer-stats-container .stats-row {
    display: flex;
    gap: 8px;
    width: 100%;
    overflow: visible;
    margin-bottom: 0;
}

.document-viewer-stats-container .usage-row,
.document-viewer-stats-container .costs-row,
.document-viewer-stats-container .credit-row,
.document-viewer-stats-container .total-cost-row {
    width: 100%;
}

/* Modern stats items styling */
.document-viewer-stats-container .costs-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.document-viewer-stats-container .total-cost-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.document-viewer-stats-container .credit-row .stats-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px 8px;
}

/* Modern stats items */
.document-viewer-stats-container .stats-item {
    position: relative;
    flex: 1;
    background-color: #ffffff;
    border-radius: 6px;
    padding: 12px 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    text-align: center;
    box-shadow: none;
}

.document-viewer-stats-container .stats-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern stats values */
.document-viewer-stats-container .stats-value {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 4px 0;
    line-height: 1.2;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Modern logout button style */
.document-viewer-stats-container .logout-button,
.document-viewer-stats-container .fa-logout-btn {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: auto;
    margin-top: 8px;
}

.document-viewer-stats-container .logout-button:hover,
.document-viewer-stats-container .fa-logout-btn:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-viewer-stats-container .logout-button:active,
.document-viewer-stats-container .fa-logout-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.document-viewer-stats-container .logout-button .dashicons,
.document-viewer-stats-container .fa-logout-btn .dashicons {
    margin-right: 6px;
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Modern stats labels */
.document-viewer-stats-container .stats-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    letter-spacing: 0;
    text-transform: none;
    gap: 4px;
}

/* Modern info icon and tooltip */
.document-viewer-stats-container .stats-info-icon {
    position: relative;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #6b7280;
    color: #ffffff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    margin-left: 4px;
    cursor: help;
    border: none;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.document-viewer-stats-container .stats-info-icon:hover {
    background-color: #374151;
    color: #ffffff;
}

/* Modern tooltip styling */
.document-viewer-stats-container .stats-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.2s ease;
    z-index: 1000;
    margin-bottom: 5px;
}

.document-viewer-stats-container .stats-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.document-viewer-stats-container .stats-info-icon:hover + .stats-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Modern stats value colors */
.document-viewer-stats-container .cost-highlight {
    color: #dc2626;
}

.document-viewer-stats-container .total-cost-highlight {
    color: #b45309;
    font-weight: 600;
    font-size: 18px;
}

.document-viewer-stats-container .credit-highlight {
    color: #059669;
    font-size: 18px;
}

/* Modern update animation */
.document-viewer-stats-container .highlight-update {
    animation: highlight-update 1.5s ease-out 1;
    font-weight: 600;
}

@keyframes highlight-update {
    0% { background-color: rgba(59, 130, 246, 0.1); color: #1d4ed8; }
    50% { background-color: rgba(59, 130, 246, 0.05); }
    100% { background-color: transparent; color: inherit; }
}

/* Modern refresh button */
.document-viewer-stats-container .refresh-stats-btn {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 8px;
    font-size: 12px;
    display: block;
    width: 100%;
    color: #6b7280;
    text-align: center;
    transition: all 0.2s ease;
    font-weight: 500;
}

.document-viewer-stats-container .refresh-stats-btn:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
    color: #374151;
}

/* Modern toggle icon */
.toggle-icon {
    width: 12px;
    height: 12px;
    position: relative;
    transition: transform 0.2s ease;
}

.toggle-icon:before, .toggle-icon:after {
    content: '';
    position: absolute;
    background-color: #6b7280;
    transition: transform 0.2s ease;
}

.toggle-icon:before {
    width: 2px;
    height: 12px;
    top: 0;
    left: 5px;
}

.toggle-icon:after {
    width: 12px;
    height: 2px;
    top: 5px;
    left: 0;
}

.toggle-icon.expanded:after {
    opacity: 0;
    transform: rotate(90deg);
}

.toggle-icon.collapsed:before {
    transform: rotate(90deg);
}

/* Modern recent analyses */
.recent-analyses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
}

.recent-analysis-item {
    padding: 8px 12px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: #6b7280;
}

.recent-analysis-item:last-child {
    border-bottom: none;
}

.recent-analysis-item:hover {
    background-color: #f9fafb;
}

.analysis-item-title {
    font-weight: 500;
    margin-bottom: 2px;
    color: #374151;
}

.analysis-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #9ca3af;
}

.no-analyses {
    font-style: italic;
    color: #9ca3af;
    text-align: center;
    padding: 12px 0;
    font-size: 13px;
}

/* Modern loading and error states */
.stats-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #6b7280;
    flex-direction: column;
    font-size: 13px;
}

.stats-spinner {
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    border-top: 2px solid #6b7280;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

.stats-error {
    color: #dc2626;
    padding: 12px;
    text-align: center;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    font-size: 13px;
}

#retry-stats-btn {
    margin-top: 8px;
    padding: 6px 12px;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease;
}

#retry-stats-btn:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
    color: #374151;
}

/* Modern responsive design */
@media (max-width: 768px) {
    .document-viewer-stats-container .stats-row {
        flex-direction: column;
        gap: 8px;
    }

    .document-viewer-stats-container .stats-tooltip {
        width: 180px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
        bottom: 100%;
        max-width: calc(100vw - 20px);
        margin-bottom: 8px;
    }
}

/* IMPORTANTE: Nasconde completamente la card spesa totale nel document viewer widget */
.document-viewer-stats-container .total-cost-row {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* IMPORTANTE: Nasconde completamente le card analisi e token nel document viewer widget */
.document-viewer-stats-container .usage-row {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Assicura che anche gli elementi figli siano nascosti */
.document-viewer-stats-container .total-cost-row *,
.document-viewer-stats-container .usage-row * {
    display: none !important;
    visibility: hidden !important;
}

/* Modern CSS variables */
:root {
    --stats-border-color: #e5e7eb;
    --stats-highlight-bg: #f9fafb;
    --stats-cost-color: #dc2626;
    --stats-bg-color: #ffffff;
    --stats-text-color: #374151;
    --stats-accent-color: #059669;
    --stats-secondary-color: #6b7280;
}

/* Stile minimal per la user-info - RIMOSSO DUPLICATO */

/* Modern flash animation */
@keyframes flash-animation {
    0% { background-color: rgba(59, 130, 246, 0); }
    20% { background-color: rgba(59, 130, 246, 0.1); }
    80% { background-color: rgba(59, 130, 246, 0.05); }
    100% { background-color: rgba(59, 130, 246, 0); }
}

/* Modern logout link styling */
.stats-logout-link {
    position: relative;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 6px 10px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
}

.stats-logout-link:hover {
    background-color: #fef2f2;
    border-color: #fecaca;
    text-decoration: none;
    color: #dc2626;
}

.stats-logout-link.logging-out {
    pointer-events: none;
    opacity: 0.7;
    background-color: #f9fafb;
}

.stats-logout-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 4px;
}

/* Stili per la testata del pannello statistiche con utente e tipo sottoscrizione - CONSOLIDATO */

#user-avatar {
    margin-right: 12px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e5e7eb;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6b7280;
    background-color: #e5e7eb;
    font-size: 16px;
}

#user-info {
    flex: 1;
}

#user-name {
    font-weight: 600;
    font-size: 14px;
    color: #111827;
    margin-bottom: 2px;
}

#user-subscription-type {
    font-size: 12px;
    color: #6b7280;
    background-color: #f3f4f6;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
    border: 1px solid #e5e7eb;
}