<?php
/**
 * Class Document_Viewer_Logger
 * 
 * Gestisce tutte le funzionalità di logging per il plugin Document Viewer
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit;
}

class Document_Viewer_Logger {
    /**
     * Costante per abilitare/disabilitare il debug logging
     */
    private $debug_enabled;

    /**
     * Percorso del file di log
     */
    private $log_file;

    /**
     * Costruttore
     */
    public function __construct() {
        // Definisce se il debug è abilitato
        $this->debug_enabled = defined('DOCUMENT_VIEWER_DEBUG') ? DOCUMENT_VIEWER_DEBUG : false;
        
        // Definisce il percorso del file di log
        $this->log_file = plugin_dir_path(dirname(__FILE__)) . 'assets/logs/debug.log';
        
        // Crea la directory dei log se non esiste
        $this->initialize_log_directory();
        
        // Aggiunge gli action handler per le chiamate AJAX
        add_action('wp_ajax_clear_shared_log', array($this, 'clear_shared_log'));
        add_action('wp_ajax_get_shared_log', array($this, 'get_shared_log'));
        add_action('wp_ajax_ocr_log', array($this, 'handle_ocr_log'));
    }

    /**
     * Inizializza la directory dei log
     */
    private function initialize_log_directory() {
        $log_dir = dirname($this->log_file);
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
            
            // Crea index.php nella directory dei log
            $index_file = $log_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, "<?php\n// Silence is golden.");
            }
            
            // Crea .htaccess nella directory dei log
            $htaccess_file = $log_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                $htaccess_content = "# Protect logs\nDeny from all\n";
                file_put_contents($htaccess_file, $htaccess_content);
            }
        }
    }

    /**
     * Scrive un messaggio nel file di log
     * 
     * @param string $msg Messaggio da loggare
     * @param string $context Contesto del messaggio (default, primary, secondary, analysis)
     */
    public function debug_log($msg, $context = 'default') {
        if (!$this->debug_enabled) {
            return;
        }
        
        $prefix = '';
        switch($context) {
            case 'primary':
                $prefix = '[PRIMARY API]';
                break;
            case 'secondary':
                $prefix = '[SECONDARY API]';
                break;
            case 'analysis':
                $prefix = '[ANALYSIS API]';
                break;
            default:
                $prefix = '[GENERAL]';
        }
        
        $formatted_msg = '[' . date('Y-m-d H:i:s') . '] ' . $prefix . ' ' . $msg . "\n";
        
        // Create logs directory if it doesn't exist (additional check)
        $log_dir = dirname($this->log_file);
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }
        
        // Write to plugin-specific log file
        file_put_contents($this->log_file, $formatted_msg, FILE_APPEND);
    }

    /**
     * Cancella i log dal file di log
     */
    public function clear_shared_log() {
        check_ajax_referer('document_viewer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'document-viewer-plugin')));
            return;
        }
        
        $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'all';
        
        if ($context === 'all') {
            if (file_exists($this->log_file) && is_writable($this->log_file)) {
                file_put_contents($this->log_file, '');
                wp_send_json_success(array('message' => __('All logs cleared successfully.', 'document-viewer-plugin')));
                return;
            }
        } else {
            if (file_exists($this->log_file) && is_writable($this->log_file)) {
                $content = file_get_contents($this->log_file);
                $lines = explode("\n", $content);
                $filtered_lines = array_filter($lines, function($line) use ($context) {
                    return strpos($line, "[$context]") === false;
                });
                file_put_contents($this->log_file, implode("\n", $filtered_lines));
                wp_send_json_success(array('message' => sprintf(__('%s logs cleared successfully.', 'document-viewer-plugin'), ucfirst($context))));
                return;
            }
        }
        
        wp_send_json_error(array('message' => __('Could not clear logs. Check file permissions.', 'document-viewer-plugin')));
    }

    /**
     * Ottiene il contenuto del file di log
     * 
     * @param string $context Contesto dei log da recuperare (all, primary, secondary, analysis)
     * @return string Contenuto del file di log
     */
    public function get_shared_log($context = 'all') {
        // Se chiamato come action AJAX
        if (doing_ajax()) {
            check_ajax_referer('document_viewer_nonce', 'nonce');
            
            if (!current_user_can('manage_options')) {
                wp_send_json_error(['message' => __('Permission denied.', 'document-viewer-plugin')]);
                return;
            }
            
            $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'all';
            
            $log_content = $this->get_log_content($context);
            wp_send_json_success(['content' => $log_content]);
            return;
        }
        
        // Se chiamato come metodo normale
        return $this->get_log_content($context);
    }
    
    /**
     * Recupera il contenuto del file di log
     * 
     * @param string $context Contesto dei log da recuperare
     * @return string Contenuto del file di log
     */
    private function get_log_content($context = 'all') {
        if (!file_exists($this->log_file)) {
            return '';
        }

        $content = file_get_contents($this->log_file);
        if ($context === 'all') {
            return $content;
        }

        $lines = explode("\n", $content);
        $filtered_lines = array_filter($lines, function($line) use ($context) {
            return strpos($line, "[$context]") !== false;
        });
        
        return implode("\n", $filtered_lines);
    }

    /**
     * Gestisce i messaggi di log dall'OCR JavaScript
     */
    public function handle_ocr_log() {
        check_ajax_referer('document_viewer_nonce', 'nonce');
        
        $message = isset($_POST['message']) ? sanitize_text_field($_POST['message']) : '';
        $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'ocr';
        $extracted_text = isset($_POST['extracted_text']) ? $_POST['extracted_text'] : '';
        
        // Log the message
        $this->debug_log($message, $context);
        
        // If we have extracted text, save it to session for potential debugging
        if (!empty($extracted_text)) {
            if (!session_id() && !headers_sent()) {
                session_start();
            }
            $_SESSION['ocr_extracted_text'] = $extracted_text;
            $this->debug_log('Testo OCR salvato in sessione (' . strlen($extracted_text) . ' caratteri)', $context);
        }
        
        wp_send_json_success(['message' => 'Log saved']);
    }

    /**
     * Funzione statica per ottenere l'istanza della classe
     */
    public static function instance() {
        static $instance = null;
        if ($instance === null) {
            $instance = new self();
        }
        return $instance;
    }
}

// Funzione di utilità per l'accesso rapido alla funzionalità di logging
function dv_logger() {
    return Document_Viewer_Logger::instance();
}