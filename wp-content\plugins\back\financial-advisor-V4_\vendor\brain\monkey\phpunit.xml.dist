<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/7.5/phpunit.xsd"
         beStrictAboutTestsThatDoNotTestAnything="false"
         bootstrap="tests/bootstrap.php"
         colors="true"
         convertDeprecationsToExceptions="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false">
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
        </whitelist>
    </filter>
    <testsuites>
        <testsuite name="unit">
            <directory>tests/cases/unit</directory>
        </testsuite>
        <testsuite name="unit:api">
            <directory>tests/cases/unit/Api</directory>
        </testsuite>
        <testsuite name="unit:expectation">
            <directory>tests/cases/unit/Expectation</directory>
        </testsuite>
        <testsuite name="unit:name">
            <directory>tests/cases/unit/Name</directory>
        </testsuite>
        <testsuite name="unit:hook">
            <directory>tests/cases/unit/Hook</directory>
        </testsuite>
        <testsuite name="functional">
            <directory>tests/cases/functional</directory>
        </testsuite>
    </testsuites>
</phpunit>
