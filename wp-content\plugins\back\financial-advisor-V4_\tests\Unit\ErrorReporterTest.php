<?php

namespace Tests\Unit;

use Tests\BaseTestCase;
use Mockery;

/**
 * Unit tests for Office_Addin_Error_Reporter class
 */
class ErrorReporterTest extends BaseTestCase
{
    private $error_reporter;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Include the error reporter class
        require_once __DIR__ . '/../../includes/class-office-addin-error-reporter.php';
        
        $this->error_reporter = new \Office_Addin_Error_Reporter();
    }

    public function testLogErrorStoresErrorInDatabase(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_errors',
                \Mockery::subset([
                    'message' => 'Test error message',
                    'severity' => 'error',
                    'category' => 'api'
                ])
            )
            ->andReturn(1);
        
        $result = $this->error_reporter->log_error(
            'Test error message',
            'error',
            'api',
            ['context' => 'test']
        );
        
        $this->assertTrue($result);
    }

    public function testLogWarningStoresWithCorrectSeverity(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_errors',
                \Mockery::subset([
                    'severity' => 'warning'
                ])
            )
            ->andReturn(1);
        
        $result = $this->error_reporter->log_warning('Warning message', 'validation');
        
        $this->assertTrue($result);
    }

    public function testLogInfoStoresWithCorrectSeverity(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_errors',
                \Mockery::subset([
                    'severity' => 'info'
                ])
            )
            ->andReturn(1);
        
        $result = $this->error_reporter->log_info('Info message', 'system');
        
        $this->assertTrue($result);
    }

    public function testLogDebugStoresWithCorrectSeverity(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_errors',
                \Mockery::subset([
                    'severity' => 'debug'
                ])
            )
            ->andReturn(1);
        
        $result = $this->error_reporter->log_debug('Debug message', 'performance');
        
        $this->assertTrue($result);
    }

    public function testGetErrorsRetrievesFromDatabase(): void
    {
        global $wpdb;
        
        $mock_errors = [
            (object)[
                'id' => 1,
                'message' => 'Error 1',
                'severity' => 'error',
                'category' => 'api',
                'created_at' => '2024-01-01 12:00:00'
            ],
            (object)[
                'id' => 2,
                'message' => 'Error 2',
                'severity' => 'warning',
                'category' => 'validation',
                'created_at' => '2024-01-01 12:01:00'
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_errors);
        
        $errors = $this->error_reporter->get_errors();
        
        $this->assertIsArray($errors);
        $this->assertCount(2, $errors);
        $this->assertEquals('Error 1', $errors[0]->message);
        $this->assertEquals('warning', $errors[1]->severity);
    }

    public function testGetErrorsWithFilters(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->with('prepared_query')
            ->andReturn([]);
        
        $errors = $this->error_reporter->get_errors([
            'severity' => 'error',
            'category' => 'api',
            'limit' => 50
        ]);
        
        $this->assertIsArray($errors);
    }

    public function testGetStatsReturnsAggregatedData(): void
    {
        global $wpdb;
        
        $mock_stats = [
            (object)['severity' => 'error', 'count' => 10],
            (object)['severity' => 'warning', 'count' => 25],
            (object)['severity' => 'info', 'count' => 100]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_stats);
        
        $stats = $this->error_reporter->get_stats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total', $stats);
        $this->assertArrayHasKey('by_severity', $stats);
        $this->assertEquals(135, $stats['total']); // 10 + 25 + 100
    }

    public function testCleanupOldErrorsRemovesExpiredEntries(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('query')
            ->once()
            ->andReturn(15); // 15 entries removed
        
        $result = $this->error_reporter->cleanup(30); // Clean older than 30 days
        
        $this->assertEquals(15, $result);
    }

    public function testSendEmailNotificationForCriticalErrors(): void
    {
        // Mock email sending
        \Brain\Monkey\Functions\when('wp_mail')->justReturn(true);
        \Brain\Monkey\Functions\when('get_option')
            ->andReturnUsing(function($option) {
                return $option === 'admin_email' ? '<EMAIL>' : false;
            });
        
        $result = $this->error_reporter->send_notification(
            'Critical error occurred',
            'error',
            'api',
            ['user_id' => 123]
        );
        
        $this->assertTrue($result);
    }

    public function testEmailNotificationNotSentForNonCriticalErrors(): void
    {
        // Mock email functions but expect no calls for non-critical errors
        \Brain\Monkey\Functions\when('wp_mail')->justReturn(true);
        
        $result = $this->error_reporter->send_notification(
            'Info message',
            'info',
            'system'
        );
        
        // Should not send email for info level
        $this->assertFalse($result);
    }

    public function testErrorContextIsSerialized(): void
    {
        global $wpdb;
        
        $complex_context = [
            'user_id' => 123,
            'request_data' => ['param1' => 'value1'],
            'stack_trace' => debug_backtrace()
        ];
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_errors',
                \Mockery::on(function($data) {
                    return isset($data['context']) && is_string($data['context']);
                })
            )
            ->andReturn(1);
        
        $result = $this->error_reporter->log_error(
            'Test error',
            'error',
            'api',
            $complex_context
        );
        
        $this->assertTrue($result);
    }

    public function testGetErrorsByCategory(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->with(
                \Mockery::pattern('/WHERE category = %s/'),
                'api'
            )
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->with('prepared_query')
            ->andReturn([]);
        
        $errors = $this->error_reporter->get_errors_by_category('api');
        
        $this->assertIsArray($errors);
    }

    public function testGetErrorsBySeverity(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->with(
                \Mockery::pattern('/WHERE severity = %s/'),
                'error'
            )
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->with('prepared_query')
            ->andReturn([]);
        
        $errors = $this->error_reporter->get_errors_by_severity('error');
        
        $this->assertIsArray($errors);
    }

    public function testGetRecentErrorsUsesTimeLimit(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->with(
                \Mockery::pattern('/WHERE created_at >= %s/'),
                \Mockery::type('string')
            )
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->with('prepared_query')
            ->andReturn([]);
        
        $errors = $this->error_reporter->get_recent_errors(3600); // Last hour
        
        $this->assertIsArray($errors);
    }

    public function testErrorRateLimiting(): void
    {
        // Test that similar errors within a short timeframe are rate limited
        global $wpdb;
        
        // First error should be logged
        $wpdb->shouldReceive('get_var')
            ->once()
            ->andReturn(0); // No recent similar errors
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->andReturn(1);
        
        $result1 = $this->error_reporter->log_error('Same error', 'error', 'api');
        $this->assertTrue($result1);
        
        // Second identical error within rate limit window should be skipped
        $wpdb->shouldReceive('get_var')
            ->once()
            ->andReturn(1); // Found recent similar error
        
        $result2 = $this->error_reporter->log_error('Same error', 'error', 'api');
        $this->assertFalse($result2);
    }

    public function testExportErrorsToJson(): void
    {
        global $wpdb;
        
        $mock_errors = [
            (object)[
                'id' => 1,
                'message' => 'Test error',
                'severity' => 'error',
                'category' => 'api',
                'created_at' => '2024-01-01 12:00:00'
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_errors);
        
        $json = $this->error_reporter->export_errors('json');
        
        $this->assertIsString($json);
        $this->assertJson($json);
        
        $decoded = json_decode($json, true);
        $this->assertIsArray($decoded);
        $this->assertCount(1, $decoded);
    }

    public function testErrorCreatesTableIfNotExists(): void
    {
        global $wpdb;
        
        // Mock table creation check
        $wpdb->shouldReceive('get_var')
            ->once()
            ->andReturn(null); // Table doesn't exist
        
        $wpdb->shouldReceive('query')
            ->once()
            ->andReturn(true); // Table created successfully
        
        $this->callMethod($this->error_reporter, 'ensure_table_exists');
        
        // No exception should be thrown
        $this->assertTrue(true);
    }

    public function testInvalidSeverityThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid severity level');
        
        $this->error_reporter->log_error('Test', 'invalid_severity', 'api');
    }

    public function testInvalidCategoryThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid category');
        
        $this->error_reporter->log_error('Test', 'error', 'invalid_category');
    }
}
