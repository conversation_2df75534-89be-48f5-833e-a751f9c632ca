<?php
/**
 * Template Loader for Financial Advisor
 * 
 * Manages custom page templates for the plugin
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Register custom page templates
 *
 * @param array $templates Array of page templates
 * @return array Updated array of page templates
 */
function fa_add_page_templates($templates) {
    $templates['templates/page-subscriber-template.php'] = __('Pagina per Sottoscrittori', 'document-viewer-plugin');
    return $templates;
}
add_filter('theme_page_templates', 'fa_add_page_templates');

/**
 * Load custom page templates
 *
 * @param string $template Current template path
 * @return string Modified template path
 */
function fa_load_page_templates($template) {
    global $post;

    if (is_null($post))
        return $template;

    // Get template set in page
    $page_template = get_post_meta($post->ID, '_wp_page_template', true);

    // Check if this is one of our templates
    if (strpos($page_template, 'templates/') === 0) {
        $file = plugin_dir_path(dirname(__FILE__)) . $page_template;
        
        if (file_exists($file)) {
            return $file;
        }
    }

    return $template;
}
add_filter('page_template', 'fa_load_page_templates');

/**
 * Register custom CSS for page templates
 */
function fa_add_template_styles() {
    global $post;
    
    if (!$post)
        return;
        
    $page_template = get_post_meta($post->ID, '_wp_page_template', true);
    
    // Add styles for subscriber template
    if ($page_template === 'templates/page-subscriber-template.php') {
        wp_enqueue_style(
            'fa-subscriber-template-style',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/subscriber-template.css',
            array(),
            DOCUMENT_ADVISOR_VERSION
        );
    }
}
add_action('wp_enqueue_scripts', 'fa_add_template_styles');
